{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\Banners.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { IconButton, Box, Typography, MobileStepper, Button, LinearProgress } from \"@mui/material\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\nimport { Link } from \"@mui/icons-material\";\nimport RTEsection from \"../guideSetting/PopupSections/RTEsection\";\nimport ImageSectionField from \"./selectedpopupfields/ImageSectionField\";\nimport ButtonSection from \"../guideSetting/PopupSections/Button\";\nimport HtmlSection from \"../guideSetting/PopupSections/HtmlSection\";\nimport EmojiPicker from \"emoji-picker-react\";\nimport \"./guideBanner.css\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport ButtonSettings from \"./selectedpopupfields/ButtonSettings\";\nimport Tooltip from \"@mui/material/Tooltip\";\nimport AlertPopup from \"../drawer/AlertPopup\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Banner = ({\n  setImageSrc,\n  imageSrc,\n  textBoxRef,\n  setHtmlContent,\n  htmlContent,\n  buttonColor,\n  setButtonColor,\n  setImageName,\n  imageName,\n  alignment,\n  setAlignment,\n  textvalue,\n  setTextvalue,\n  isBanner,\n  overlays,\n  setOverLays,\n  Bposition,\n  setBposition,\n  bpadding,\n  setbPadding,\n  isUnSavedChanges,\n  openWarning,\n  setopenWarning,\n  handleLeave,\n  savedGuideData,\n  Progress\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _savedGuideData$Guide3, _bannerJson$GuideStep;\n  const guidePopUpRef = useRef(null);\n  const {\n    dismissData,\n    sectionColor,\n    setSectionColor,\n    buttonProperty,\n    setButtonProperty,\n    BborderSize,\n    Bbordercolor,\n    backgroundC,\n    textArray,\n    setBannerButtonSelected,\n    setTextArray,\n    preview,\n    setPreview,\n    bannerButtonSelected,\n    clearGuideDetails,\n    btnBgColor,\n    btnTextColor,\n    btnBorderColor,\n    buttonsContainer,\n    setButtonsContainer,\n    clearBannerButtonDetials,\n    setRTEAnchorEl,\n    deleteRTEContainer,\n    rteAnchorEl,\n    bannerJson,\n    currentStep,\n    selectedOption,\n    ProgressColor,\n    setProgressColor,\n    steps,\n    progress,\n    createWithAI,\n    selectedTemplate,\n    syncAIAnnouncementDataForPreview,\n    rtesContainer\n  } = useDrawerStore(state => state);\n  const [textAreas, setTextAreas] = useState([[{\n    name: \"Rich Text\",\n    value: /*#__PURE__*/_jsxDEV(RTEsection, {\n      textBoxRef: textBoxRef,\n      isBanner: true,\n      index: 0,\n      handleDeleteRTESection: () => {}\n      // @ts-ignore\n      ,\n      ref: textBoxRef,\n      guidePopUpRef: guidePopUpRef\n    }, 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 6\n    }, this)\n  }]]);\n\n  // Effect to restore textAreas state from persisted store data on component mount\n  useEffect(() => {\n    // Only run this restoration logic once on component mount\n    const restoreTextAreasFromStore = () => {\n      const rowIndex = 0;\n      let restoredTextAreas = [...textAreas];\n      let hasChanges = false;\n\n      // Check if we need to restore button state from the store\n      if (bannerButtonSelected && !textAreas[rowIndex].some(item => item.name === \"Button\")) {\n        const newButton = {\n          name: \"Button\",\n          value: /*#__PURE__*/_jsxDEV(ButtonSection, {\n            buttonColor: buttonColor,\n            setButtonColor: setButtonColor,\n            isBanner: true\n          }, 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 7\n          }, this)\n        };\n        restoredTextAreas[rowIndex] = [...restoredTextAreas[rowIndex], newButton];\n        hasChanges = true;\n      }\n\n      // Check if RTE content exists in the store and needs to be restored\n      // For banners, RTE content is stored in rtesContainer\n      const hasRTEContent = rtesContainer && rtesContainer.length > 0 && rtesContainer[0].rtes && rtesContainer[0].rtes.length > 0 && rtesContainer[0].rtes[0].text && rtesContainer[0].rtes[0].text.trim() !== \"\";\n\n      // If there's RTE content in the store but the banner is not visible,\n      // it means we need to restore the textAreas properly\n      if (hasRTEContent) {\n        // console.log(\"Banner: Restoring RTE content from store\", {\n        // \trteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + \"...\",\n        // \tcurrentTextAreas: textAreas.length\n        // });\n\n        // Ensure the RTE section is properly initialized with the stored content\n        // The RTEsection component will automatically pick up the content from rtesContainer\n        // We just need to make sure the textAreas structure is correct\n        const hasRTEInTextAreas = restoredTextAreas[rowIndex].some(item => item.name === \"Rich Text\");\n        if (!hasRTEInTextAreas) {\n          // This shouldn't happen normally, but if it does, recreate the RTE section\n          const rteSection = {\n            name: \"Rich Text\",\n            value: /*#__PURE__*/_jsxDEV(RTEsection, {\n              textBoxRef: textBoxRef,\n              isBanner: true,\n              index: 0,\n              handleDeleteRTESection: () => {}\n              // @ts-ignore\n              ,\n              ref: textBoxRef,\n              guidePopUpRef: guidePopUpRef\n            }, 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 8\n            }, this)\n          };\n          restoredTextAreas[rowIndex] = [rteSection, ...restoredTextAreas[rowIndex].filter(item => item.name !== \"Rich Text\")];\n          hasChanges = true;\n        }\n      }\n\n      // Update textAreas if changes were made\n      if (hasChanges) {\n        setTextAreas(restoredTextAreas);\n      }\n    };\n\n    // Run restoration logic after a small delay to ensure store is fully loaded\n    const timeoutId = setTimeout(restoreTextAreasFromStore, 100);\n    return () => clearTimeout(timeoutId);\n  }, []); // Empty dependency array - only run once on mount\n\n  // UseEffect to update textAreas when setBannerButtonSelected changes\n  useEffect(() => {\n    const rowIndex = 0; // Assuming we want to update the first row\n    const existingRow = textAreas[rowIndex];\n    if (bannerButtonSelected) {\n      // Check if Button already exists in the first row\n\n      // If Button is not already in the row, add it\n      if (!existingRow.some(item => item.name === \"Button\")) {\n        const newButton = {\n          name: \"Button\",\n          value: /*#__PURE__*/_jsxDEV(ButtonSection, {\n            buttonColor: buttonColor,\n            setButtonColor: setButtonColor,\n            isBanner: true\n          }, 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 5\n          }, this)\n        };\n        const updatedTextAreas = [...textAreas];\n        updatedTextAreas[rowIndex] = [...existingRow, newButton];\n        setTextAreas(updatedTextAreas);\n      }\n    } else {\n      // Find the button index and remove it\n      const buttonIndex = existingRow.findIndex(item => item.name === \"Button\");\n      if (buttonIndex !== -1) {\n        removeTextArea(rowIndex, buttonIndex);\n      }\n    }\n  }, [bannerButtonSelected]); // Trigger when setBannerButtonSelected changes\n\n  // Update textArray whenever textAreas changes\n  useEffect(() => {\n    setTextArray(textAreas);\n  }, [textAreas]);\n\n  // Sync AI announcement data on component mount to ensure progress bar is visible\n  useEffect(() => {\n    // Only run this for AI-created announcements\n    if (!createWithAI || selectedTemplate !== \"Announcement\") return;\n    console.log(\"Banner component mounted for AI announcement - syncing data\");\n\n    // Synchronize AI announcement data to ensure progress bar and other settings are properly initialized\n    syncAIAnnouncementDataForPreview(true); // Preserve global state during banner initialization\n  }, [createWithAI, selectedTemplate, syncAIAnnouncementDataForPreview]);\n\n  // Sync textAreas with AI-created guide data on mount and when relevant data changes\n  useEffect(() => {\n    // Only run this for AI-created guides\n    if (!createWithAI) return;\n    const rowIndex = 0;\n    const existingRow = textAreas[rowIndex];\n\n    // Check if we need to add a button based on the AI guide data\n    const hasButtonInData = bannerButtonSelected;\n    const hasButtonInTextAreas = existingRow.some(item => item.name === \"Button\");\n    if (hasButtonInData && !hasButtonInTextAreas) {\n      // Add button to textAreas if it exists in AI data but not in local state\n      const newButton = {\n        name: \"Button\",\n        value: /*#__PURE__*/_jsxDEV(ButtonSection, {\n          buttonColor: buttonColor,\n          setButtonColor: setButtonColor,\n          isBanner: true\n        }, 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 5\n        }, this)\n      };\n      const updatedTextAreas = [...textAreas];\n      updatedTextAreas[rowIndex] = [...existingRow, newButton];\n      setTextAreas(updatedTextAreas);\n    }\n  }, [createWithAI, bannerButtonSelected, buttonColor, setButtonColor]); // Dependencies for AI guide sync\n\n  // Additional effect to ensure button state is preserved for AI guides\n  useEffect(() => {\n    if (!createWithAI) return;\n\n    // Check if we have button data in the store but not in textAreas\n    const rowIndex = 0;\n    const existingRow = textAreas[rowIndex];\n    const hasButtonInTextAreas = existingRow.some(item => item.name === \"Button\");\n\n    // If buttonsContainer has buttons but textAreas doesn't, restore the button\n    if (buttonsContainer.length > 0 && !hasButtonInTextAreas && !bannerButtonSelected) {\n      setBannerButtonSelected(true);\n    }\n  }, [createWithAI, buttonsContainer, textAreas, bannerButtonSelected, setBannerButtonSelected]); // Monitor button container changes\n\n  // Handle overflow behavior based on banner position in creation mode\n  useEffect(() => {\n    // Use a small delay to ensure this runs after resetHeightofBanner\n    const timeoutId = setTimeout(() => {\n      // Only apply overflow hidden in creation/edit mode when position is \"Cover Top\"\n      // This component is only rendered in creation mode (when !showBannerenduser)\n      // Preview mode is handled by separate preview components\n      if (Bposition === \"Cover Top\") {\n        document.body.style.setProperty(\"overflow\", \"hidden\", \"important\");\n      } else {\n        // Restore normal scrolling for other positions\n        document.body.style.removeProperty(\"overflow\");\n      }\n    }, 100); // Small delay to ensure it runs after resetHeightofBanner\n\n    // Cleanup function to restore overflow when component unmounts\n    return () => {\n      clearTimeout(timeoutId);\n      document.body.style.removeProperty(\"overflow\");\n    };\n  }, [Bposition]); // Re-run when position changes\n\n  const overlayEnabled = useDrawerStore(state => state.overlayEnabled);\n  const [showOptions, setShowOptions] = useState(false);\n  const [focusedRowIndex, setFocusedRowIndex] = useState(null);\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const handleDeleteRTESection = index => {\n    const newTextAreas = [...textAreas];\n    newTextAreas.splice(index, 1);\n    setTextAreas(newTextAreas);\n    setTextArray(newTextAreas);\n  };\n  const addTextAreaInSameRow = (rowIndex, option) => {\n    if (!textAreas[rowIndex]) {\n      textAreas[rowIndex] = [];\n    }\n    const existingRow = textAreas[rowIndex];\n    // Check if a Rich Text or Button already exists in the row\n    if (option === \"richText\" && existingRow.some(item => item.name === \"Rich Text\") || option === \"button\" && existingRow.some(item => item.name === \"Button\")) {\n      alert(`Only one ${option === \"richText\" ? \"Rich Text\" : \"Button\"} is allowed per row.`);\n      return;\n    }\n    let newValue;\n    let newName = \"\";\n    switch (option) {\n      case \"image\":\n        newValue = /*#__PURE__*/_jsxDEV(ImageSectionField, {\n          setImageSrc: setImageSrc,\n          imageSrc: imageSrc,\n          setImageName: setImageName\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 6\n        }, this);\n        newName = \"Image\";\n        break;\n      case \"richText\":\n        // setBannerButtonSelected(false);\n\n        newValue = /*#__PURE__*/_jsxDEV(RTEsection, {\n          textBoxRef: textBoxRef,\n          isBanner: true,\n          index: rowIndex,\n          handleDeleteRTESection: handleDeleteRTESection\n          // @ts-ignore\n          ,\n          ref: textBoxRef,\n          guidePopUpRef: guidePopUpRef\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 6\n        }, this);\n        newName = \"Rich Text\";\n        break;\n      case \"button\":\n        setBannerButtonSelected(true);\n\n        // CRITICAL FIX: Ensure button container exists when adding button through banner editor\n        // This creates the actual button data that ButtonSection component needs to display buttons\n        if (buttonsContainer.length === 0) {\n          const newButtonContainer = {\n            id: crypto.randomUUID(),\n            buttons: [{\n              id: crypto.randomUUID(),\n              name: \"Got It\",\n              position: \"center\",\n              type: \"primary\",\n              isEditing: false,\n              index: 0,\n              style: {\n                backgroundColor: \"#5F9EA0\",\n                borderColor: \"#70afaf\",\n                color: \"#ffffff\"\n              },\n              actions: {\n                value: \"close\",\n                targetURL: \"\",\n                tab: \"same-tab\",\n                interaction: null\n              },\n              survey: null\n            }],\n            style: {\n              backgroundColor: \"transparent\"\n            }\n          };\n\n          // Add the button container to the store\n          setButtonsContainer([newButtonContainer]);\n          console.log(\"🔄 Banner editor: Created new button container for banner tour\", {\n            containerId: newButtonContainer.id,\n            buttonCount: newButtonContainer.buttons.length\n          });\n        }\n        newValue = /*#__PURE__*/_jsxDEV(ButtonSection, {\n          buttonColor: buttonColor,\n          setButtonColor: setButtonColor,\n          isBanner: true\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 6\n        }, this);\n        newName = \"Button\";\n        break;\n      case \"html\":\n        newValue = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"htmlbanner\",\n          children: /*#__PURE__*/_jsxDEV(HtmlSection, {\n            htmlContent: htmlContent,\n            setHtmlContent: setHtmlContent,\n            isBanner: true\n          }, rowIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 6\n        }, this);\n        newName = \"HTML\";\n        break;\n      default:\n        newValue = \"\";\n        newName = \"Text\";\n    }\n    const newTextAreas = [...textAreas];\n    newTextAreas[rowIndex] = [...existingRow, {\n      name: newName,\n      value: newValue\n    }];\n    setTextAreas(newTextAreas);\n    setTextArray(newTextAreas);\n  };\n  const removeTextArea = (rowIndex, textAreaIndex) => {\n    const updatedTextAreas = textAreas.map((row, index) => {\n      if (index === rowIndex) {\n        const filteredRow = row.filter((_, idx) => idx !== textAreaIndex);\n\n        // Check if there are any buttons remaining in the row after removal\n        const hasButtonInRow = filteredRow.some(t => t.name === \"Button\");\n        setBannerButtonSelected(hasButtonInRow);\n        return filteredRow;\n      }\n      return row;\n    });\n    if (textAreaIndex === 1) {\n      clearBannerButtonDetials();\n    } else if (textAreaIndex === 0) {\n      setRTEAnchorEl({\n        rteId: \"\",\n        containerId: \"\",\n        // @ts-ignore\n        value: null\n      });\n      deleteRTEContainer(rteAnchorEl.containerId);\n    }\n    setTextAreas(updatedTextAreas);\n    setTextArray(updatedTextAreas);\n  };\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : (_savedGuideData$Guide3 = _savedGuideData$Guide2.Tooltip) === null || _savedGuideData$Guide3 === void 0 ? void 0 : _savedGuideData$Guide3.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide4 = savedGuideData.GuideStep) === null || _savedGuideData$Guide4 === void 0 ? void 0 : (_savedGuideData$Guide5 = _savedGuideData$Guide4[0]) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5.Tooltip) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    var _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9;\n    // Check both the global progress state and the saved guide data for progress settings\n    const enableProgressFromData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[0]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.Tooltip) === null || _savedGuideData$Guide9 === void 0 ? void 0 : _savedGuideData$Guide9.EnableProgress;\n    const shouldShowProgress = progress || enableProgressFromData;\n    if (!shouldShowProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: steps.length,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          padding: \"8px 0 0 0 !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: \"8px 0 0 0 !important\",\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\"\n        },\n        children: Array.from({\n          length: steps.length\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '14px',\n            height: '4px',\n            backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0',\n            // Active color and inactive color\n            borderRadius: '100px'\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 23\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 17\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: \"8px 0 0 0 !important\",\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"12px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", steps.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"calc(50% - 410px)\",\n          padding: \"8px 0 0 0\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: Progress,\n            sx: {\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  const style = (_bannerJson$GuideStep = bannerJson.GuideStep.find(step => step.stepName === currentStep)) === null || _bannerJson$GuideStep === void 0 ? void 0 : _bannerJson$GuideStep.Canvas;\n  // Apply overflow hidden to body when canvas position is \"Cover Top\" in creation mode\n  useEffect(() => {\n    if ((style === null || style === void 0 ? void 0 : style.Position) === \"Cover Top\") {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"\";\n    }\n\n    // Cleanup function to restore overflow when component unmounts\n    return () => {\n      document.body.style.overflow = \"\";\n    };\n  }, [style === null || style === void 0 ? void 0 : style.Position]); // Re-run when canvas position changes\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-container creation\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-box\",\n        sx: {\n          padding: (style === null || style === void 0 ? void 0 : style.Padding) !== undefined && (style === null || style === void 0 ? void 0 : style.Padding) !== null ? `${style.Padding}px` : \"10px\",\n          boxShadow: style && (style === null || style === void 0 ? void 0 : style.Position) === \"Push Down\" ? \"none\" : \"0px 1px 15px rgba(0, 0, 0, 0.7)\",\n          borderTop: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          borderRight: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          borderLeft: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          borderBottom: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          backgroundColor: `${style === null || style === void 0 ? void 0 : style.BackgroundColor} !important ` || \"#f0f0f0\",\n          position: (style === null || style === void 0 ? void 0 : style.Position) || \"Cover Top\",\n          zIndex: (style === null || style === void 0 ? void 0 : style.Zindex) || 9999\n        },\n        id: \"guide-popup\",\n        ref: guidePopUpRef,\n        children: [textArray.map((row, rowIndex) => /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-row\",\n          children: [row.map((textArea, textAreaIndex) => /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-text-area-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-text-area\",\n              style: {\n                backgroundColor: (textArea.name === \"RTE\" || textArea.name === \"Rich Text\") && sectionColor ? sectionColor : \"\"\n              },\n              children: textArea.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 9\n            }, this), textArea.name === \"Button\" && /*#__PURE__*/_jsxDEV(IconButton, {\n              className: \"qadpt-add-btn\",\n              size: \"large\",\n              onClick: () => removeTextArea(rowIndex, textAreaIndex),\n              children: /*#__PURE__*/_jsxDEV(DeleteOutlineOutlinedIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 13\n            }, this)]\n          }, textAreaIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 7\n          }, this)), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            children: [row.some(item => item.name === \"Button\") ? /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Only one button is allowed\",\n              placement: \"bottom\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => {\n                    setShowOptions(true);\n                    setFocusedRowIndex(rowIndex);\n                  },\n                  className: \"qadpt-add-btn\",\n                  size: \"small\",\n                  disabled: true,\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 6\n            }, this) : /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => {\n                setShowOptions(true);\n                setFocusedRowIndex(rowIndex);\n              },\n              className: \"qadpt-add-btn\",\n              size: \"small\",\n              disabled: row.some(item => item.name === \"Button\"),\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                // position: \"fixed\",\n                padding: \"3px\",\n                boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\n                marginLeft: \"2px\",\n                background: \"#fff !important\",\n                border: \"1px solid #ccc\",\n                zIndex: \"999999\",\n                display: dismissData !== null && dismissData !== void 0 && dismissData.dismisssel ? \"flex\" : \"none\" // Show/hide based on dismisssel\n              },\n              children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n                sx: {\n                  zoom: \"1\",\n                  color: \"#000\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 6\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 7\n          }, this), isUnSavedChanges && openWarning && /*#__PURE__*/_jsxDEV(AlertPopup, {\n            openWarning: openWarning,\n            setopenWarning: setopenWarning,\n            handleLeave: handleLeave\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 4\n          }, this), showOptions && focusedRowIndex === rowIndex && /*#__PURE__*/_jsxDEV(Box, {\n            onMouseEnter: () => setShowOptions(true),\n            onMouseLeave: () => setShowOptions(false),\n            className: \"qadpt-options-menu\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-options-content\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"row\",\n                alignItems: \"center\",\n                sx: {\n                  cursor: \"pointer\",\n                  gap: \"10px\",\n                  placeContent: \"center\",\n                  width: \"100%\"\n                },\n                onClick: () => addTextAreaInSameRow(rowIndex, \"button\"),\n                children: [/*#__PURE__*/_jsxDEV(Link, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Button\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 4\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 7\n          }, this)]\n        }, rowIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 3\n        }, this)), ((savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideType) === \"Tour\" || (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideType) === \"Announcement\") && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...(progressTemplate === \"linear\" && {\n              display: \"flex\",\n              placeContent: \"center\",\n              alignItems: \"center\"\n            })\n          },\n          children: steps.length >= 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: renderProgress()\n          }, void 0, false) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 1\n        }, this), showEmojiPicker && /*#__PURE__*/_jsxDEV(EmojiPicker, {\n          onEmojiClick: () => {}\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 4\n    }, this), buttonProperty && /*#__PURE__*/_jsxDEV(ButtonSettings, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 24\n    }, this)]\n  }, void 0, true);\n};\n_s(Banner, \"nswiEtwm43QMTDh5AHVvkHbY5Fc=\", false, function () {\n  return [useDrawerStore, useDrawerStore];\n});\n_c = Banner;\nexport default Banner;\nvar _c;\n$RefreshReg$(_c, \"Banner\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "IconButton", "Box", "Typography", "MobileStepper", "<PERSON><PERSON>", "LinearProgress", "AddIcon", "DeleteOutlineOutlinedIcon", "Link", "RTEsection", "ImageSectionField", "ButtonSection", "HtmlSection", "EmojiPicker", "CloseIcon", "useDrawerStore", "ButtonSettings", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Banner", "setImageSrc", "imageSrc", "textBoxRef", "setHtmlContent", "htmlContent", "buttonColor", "setButtonColor", "setImageName", "imageName", "alignment", "setAlignment", "textvalue", "setTextvalue", "isBanner", "overlays", "setOverLays", "Bposition", "setBposition", "bpadding", "setbPadding", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "savedGuideData", "Progress", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_bannerJson$GuideStep", "guidePopUpRef", "dismissData", "sectionColor", "setSectionColor", "buttonProperty", "setButtonProperty", "BborderSize", "Bbordercolor", "backgroundC", "textArray", "setBannerButtonSelected", "setTextArray", "preview", "setPreview", "bannerButtonSelected", "clearGuideDetails", "btnBgColor", "btnTextColor", "btnBorderColor", "buttonsContainer", "setButtonsContainer", "clearBannerButtonDetials", "setRTEAnchorEl", "deleteRTEContainer", "rteAnchorEl", "<PERSON><PERSON><PERSON>", "currentStep", "selectedOption", "ProgressColor", "setProgressColor", "steps", "progress", "createWithAI", "selectedTemplate", "syncAIAnnouncementDataForPreview", "rtesContainer", "state", "textAreas", "setTextAreas", "name", "value", "index", "handleDeleteRTESection", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "restoreTextAreasFromStore", "rowIndex", "restoredTextAreas", "has<PERSON><PERSON><PERSON>", "some", "item", "newButton", "hasRTEContent", "length", "rtes", "text", "trim", "hasRTEInTextAreas", "rteSection", "filter", "timeoutId", "setTimeout", "clearTimeout", "existingRow", "updatedTextAreas", "buttonIndex", "findIndex", "removeTextArea", "console", "log", "hasButtonInData", "hasButtonInTextAreas", "document", "body", "style", "setProperty", "removeProperty", "overlayEnabled", "showOptions", "setShowOptions", "focusedRowIndex", "setFocusedRowIndex", "showEmojiPicker", "setShowEmojiPicker", "newTextAreas", "splice", "addTextAreaInSameRow", "option", "alert", "newValue", "newName", "newButtonContainer", "id", "crypto", "randomUUID", "buttons", "position", "type", "isEditing", "backgroundColor", "borderColor", "color", "actions", "targetURL", "tab", "interaction", "survey", "containerId", "buttonCount", "className", "children", "textAreaIndex", "map", "row", "filteredRow", "_", "idx", "hasButtonInRow", "t", "rteId", "enableProgress", "GuideStep", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "ProgressTemplate", "progressTemplate", "renderProgress", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "enableProgressFromData", "shouldShowProgress", "variant", "activeStep", "sx", "padding", "backButton", "visibility", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "Array", "from", "width", "height", "borderRadius", "fontSize", "find", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Position", "overflow", "Padding", "undefined", "boxShadow", "borderTop", "BorderSize", "BorderColor", "borderRight", "borderLeft", "borderBottom", "BackgroundColor", "zIndex", "Zindex", "textArea", "size", "onClick", "title", "placement", "disabled", "marginLeft", "background", "border", "dismisssel", "zoom", "onMouseEnter", "onMouseLeave", "flexDirection", "cursor", "GuideType", "onEmojiClick", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideBanners/Banners.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { IconButton, Box, Typography, MobileStepper, Button, LinearProgress } from \"@mui/material\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport { TextFormat, Link } from \"@mui/icons-material\";\r\nimport RTEsection from \"../guideSetting/PopupSections/RTEsection\";\r\nimport ImageSectionField from \"./selectedpopupfields/ImageSectionField\";\r\nimport ButtonSection from \"../guideSetting/PopupSections/Button\";\r\nimport HtmlSection from \"../guideSetting/PopupSections/HtmlSection\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport \"./guideBanner.css\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport ButtonSettings from \"./selectedpopupfields/ButtonSettings\";\r\nimport { saveGuide } from \"../../services/GuideListServices\";\r\nimport Tooltip from \"@mui/material/Tooltip\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nconst Banner = ({\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\ttextBoxRef,\r\n\tsetHtmlContent,\r\n\thtmlContent,\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tsetImageName,\r\n\timageName,\r\n\talignment,\r\n\tsetAlignment,\r\n\ttextvalue,\r\n\tsetTextvalue,\r\n\tisBanner,\r\n\toverlays,\r\n\tsetOverLays,\r\n\tBposition,\r\n\tsetBposition,\r\n\tbpadding,\r\n\tsetbPadding,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tsavedGuideData,\r\n\tProgress\r\n}: {\r\n\tsetImageSrc: any;\r\n\timageSrc: any;\r\n\ttextBoxRef: any;\r\n\tsetHtmlContent: any;\r\n\thtmlContent: any;\r\n\tbuttonColor: any;\r\n\tsetButtonColor: any;\r\n\tsetImageName: any;\r\n\timageName: any;\r\n\talignment: any;\r\n\tsetAlignment: any;\r\n\ttextvalue: any;\r\n\tsetTextvalue: any;\r\n\tisBanner: boolean;\r\n\toverlays: boolean;\r\n\tsetOverLays: any;\r\n\tBposition: any;\r\n\tsetBposition: any;\r\n\tbpadding: any;\r\n\t\tsetbPadding: any;\r\n\t\tisUnSavedChanges: boolean;\r\n\t\topenWarning: boolean;\r\n\t\tsetopenWarning: (params: boolean) => void;\r\n\t\thandleLeave: () => void;\r\n\t\tsavedGuideData: GuideData | null;\r\n\t\tProgress: any;\r\n}) => {\r\n\tconst guidePopUpRef = useRef<HTMLDivElement | null>(null);\r\n\tconst {\r\n\t\tdismissData,\r\n\t\tsectionColor,\r\n\t\tsetSectionColor,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tBborderSize,\r\n\t\tBbordercolor,\r\n\t\tbackgroundC,\r\n\t\ttextArray,\r\n\t\tsetBannerButtonSelected,\r\n\t\tsetTextArray,\r\n\t\tpreview,\r\n\t\tsetPreview,\r\n\t\tbannerButtonSelected,\r\n\t\tclearGuideDetails,\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tbuttonsContainer,\r\n\t\tsetButtonsContainer,\r\n\t\tclearBannerButtonDetials,\r\n\t\tsetRTEAnchorEl,\r\n\t\tdeleteRTEContainer,\r\n\t\trteAnchorEl,\r\n\t\tbannerJson,\r\n\t\tcurrentStep,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tsteps,\r\n\t\tprogress,\r\n\t\tcreateWithAI,\r\n\t\tselectedTemplate,\r\n\t\tsyncAIAnnouncementDataForPreview,\r\n\t\trtesContainer\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst [textAreas, setTextAreas] = useState<{ name: string; value: string | JSX.Element }[][]>([\r\n\t\t[\r\n\t\t  {\r\n\t\t\tname: \"Rich Text\",\r\n\t\t\tvalue: (\r\n\t\t\t  <RTEsection\r\n\t\t\t\tkey={0}\r\n\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\tisBanner={true}\r\n\t\t\t\tindex={0}\r\n\t\t\t\thandleDeleteRTESection={() => {}}\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tref={textBoxRef}\r\n\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t  />\r\n\t\t\t),\r\n\t\t  },\r\n\t\t],\r\n\t  ]);\r\n\r\n\t  // Effect to restore textAreas state from persisted store data on component mount\r\n\t  useEffect(() => {\r\n\t\t// Only run this restoration logic once on component mount\r\n\t\tconst restoreTextAreasFromStore = () => {\r\n\t\t\tconst rowIndex = 0;\r\n\t\t\tlet restoredTextAreas = [...textAreas];\r\n\t\t\tlet hasChanges = false;\r\n\r\n\t\t\t// Check if we need to restore button state from the store\r\n\t\t\tif (bannerButtonSelected && !textAreas[rowIndex].some((item) => item.name === \"Button\")) {\r\n\t\t\t\tconst newButton = {\r\n\t\t\t\t\tname: \"Button\",\r\n\t\t\t\t\tvalue: (\r\n\t\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\t\tkey={0}\r\n\t\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t};\r\n\t\t\t\trestoredTextAreas[rowIndex] = [...restoredTextAreas[rowIndex], newButton];\r\n\t\t\t\thasChanges = true;\r\n\t\t\t}\r\n\r\n\t\t\t// Check if RTE content exists in the store and needs to be restored\r\n\t\t\t// For banners, RTE content is stored in rtesContainer\r\n\t\t\tconst hasRTEContent = rtesContainer && rtesContainer.length > 0 &&\r\n\t\t\t\trtesContainer[0].rtes && rtesContainer[0].rtes.length > 0 &&\r\n\t\t\t\trtesContainer[0].rtes[0].text && rtesContainer[0].rtes[0].text.trim() !== \"\";\r\n\r\n\t\t\t// If there's RTE content in the store but the banner is not visible,\r\n\t\t\t// it means we need to restore the textAreas properly\r\n\t\t\tif (hasRTEContent) {\r\n\t\t\t\t// console.log(\"Banner: Restoring RTE content from store\", {\r\n\t\t\t\t// \trteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + \"...\",\r\n\t\t\t\t// \tcurrentTextAreas: textAreas.length\r\n\t\t\t\t// });\r\n\r\n\t\t\t\t// Ensure the RTE section is properly initialized with the stored content\r\n\t\t\t\t// The RTEsection component will automatically pick up the content from rtesContainer\r\n\t\t\t\t// We just need to make sure the textAreas structure is correct\r\n\t\t\t\tconst hasRTEInTextAreas = restoredTextAreas[rowIndex].some((item) => item.name === \"Rich Text\");\r\n\r\n\t\t\t\tif (!hasRTEInTextAreas) {\r\n\t\t\t\t\t// This shouldn't happen normally, but if it does, recreate the RTE section\r\n\t\t\t\t\tconst rteSection = {\r\n\t\t\t\t\t\tname: \"Rich Text\",\r\n\t\t\t\t\t\tvalue: (\r\n\t\t\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\t\t\tkey={0}\r\n\t\t\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t\t\tindex={0}\r\n\t\t\t\t\t\t\t\thandleDeleteRTESection={() => {}}\r\n\t\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t),\r\n\t\t\t\t\t};\r\n\t\t\t\t\trestoredTextAreas[rowIndex] = [rteSection, ...restoredTextAreas[rowIndex].filter(item => item.name !== \"Rich Text\")];\r\n\t\t\t\t\thasChanges = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Update textAreas if changes were made\r\n\t\t\tif (hasChanges) {\r\n\t\t\t\tsetTextAreas(restoredTextAreas);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Run restoration logic after a small delay to ensure store is fully loaded\r\n\t\tconst timeoutId = setTimeout(restoreTextAreasFromStore, 100);\r\n\r\n\t\treturn () => clearTimeout(timeoutId);\r\n\t  }, []); // Empty dependency array - only run once on mount\r\n\r\n\t  // UseEffect to update textAreas when setBannerButtonSelected changes\r\n\tuseEffect(() => {\r\n\t\tconst rowIndex = 0; // Assuming we want to update the first row\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\tif (bannerButtonSelected) {\r\n\t\t  // Check if Button already exists in the first row\r\n\r\n\r\n\t\t  // If Button is not already in the row, add it\r\n\t\t  if (!existingRow.some((item) => item.name === \"Button\")) {\r\n\t\t\tconst newButton = {\r\n\t\t\t  name: \"Button\",\r\n\t\t\t  value: (\r\n\t\t\t\t<ButtonSection\r\n\t\t\t\t  key={0}\r\n\t\t\t\t  buttonColor={buttonColor}\r\n\t\t\t\t  setButtonColor={setButtonColor}\r\n\t\t\t\t  isBanner={true}\r\n\t\t\t\t/>\r\n\t\t\t  ),\r\n\t\t\t};\r\n\r\n\t\t\tconst updatedTextAreas = [...textAreas];\r\n\t\t\tupdatedTextAreas[rowIndex] = [...existingRow, newButton];\r\n\t\t\tsetTextAreas(updatedTextAreas);\r\n\t\t  }\r\n\t\t} else {\r\n\t\t\t// Find the button index and remove it\r\n\t\t\tconst buttonIndex = existingRow.findIndex((item) => item.name === \"Button\");\r\n\t\t\tif (buttonIndex !== -1) {\r\n\t\t\t\tremoveTextArea(rowIndex, buttonIndex);\r\n\t\t\t}\r\n\t\t}\r\n\t  }, [bannerButtonSelected]); // Trigger when setBannerButtonSelected changes\r\n\r\n\t  // Update textArray whenever textAreas changes\r\n\t  useEffect(() => {\r\n\t\tsetTextArray(textAreas);\r\n\t  }, [textAreas]);\r\n\r\n\t  // Sync AI announcement data on component mount to ensure progress bar is visible\r\n\t  useEffect(() => {\r\n\t\t// Only run this for AI-created announcements\r\n\t\tif (!createWithAI || selectedTemplate !== \"Announcement\") return;\r\n\r\n\t\tconsole.log(\"Banner component mounted for AI announcement - syncing data\");\r\n\r\n\t\t// Synchronize AI announcement data to ensure progress bar and other settings are properly initialized\r\n\t\tsyncAIAnnouncementDataForPreview(true); // Preserve global state during banner initialization\r\n\t  }, [createWithAI, selectedTemplate, syncAIAnnouncementDataForPreview]);\r\n\r\n\t  // Sync textAreas with AI-created guide data on mount and when relevant data changes\r\n\t  useEffect(() => {\r\n\t\t// Only run this for AI-created guides\r\n\t\tif (!createWithAI) return;\r\n\r\n\t\tconst rowIndex = 0;\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\r\n\t\t// Check if we need to add a button based on the AI guide data\r\n\t\tconst hasButtonInData = bannerButtonSelected;\r\n\t\tconst hasButtonInTextAreas = existingRow.some((item) => item.name === \"Button\");\r\n\r\n\t\tif (hasButtonInData && !hasButtonInTextAreas) {\r\n\t\t\t// Add button to textAreas if it exists in AI data but not in local state\r\n\t\t\tconst newButton = {\r\n\t\t\t  name: \"Button\",\r\n\t\t\t  value: (\r\n\t\t\t\t<ButtonSection\r\n\t\t\t\t  key={0}\r\n\t\t\t\t  buttonColor={buttonColor}\r\n\t\t\t\t  setButtonColor={setButtonColor}\r\n\t\t\t\t  isBanner={true}\r\n\t\t\t\t/>\r\n\t\t\t  ),\r\n\t\t\t};\r\n\r\n\t\t\tconst updatedTextAreas = [...textAreas];\r\n\t\t\tupdatedTextAreas[rowIndex] = [...existingRow, newButton];\r\n\t\t\tsetTextAreas(updatedTextAreas);\r\n\t\t}\r\n\t  }, [createWithAI, bannerButtonSelected, buttonColor, setButtonColor]); // Dependencies for AI guide sync\r\n\r\n\t  // Additional effect to ensure button state is preserved for AI guides\r\n\t  useEffect(() => {\r\n\t\tif (!createWithAI) return;\r\n\r\n\t\t// Check if we have button data in the store but not in textAreas\r\n\t\tconst rowIndex = 0;\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\tconst hasButtonInTextAreas = existingRow.some((item) => item.name === \"Button\");\r\n\r\n\t\t// If buttonsContainer has buttons but textAreas doesn't, restore the button\r\n\t\tif (buttonsContainer.length > 0 && !hasButtonInTextAreas && !bannerButtonSelected) {\r\n\t\t\tsetBannerButtonSelected(true);\r\n\t\t}\r\n\t  }, [createWithAI, buttonsContainer, textAreas, bannerButtonSelected, setBannerButtonSelected]); // Monitor button container changes\r\n\r\n\t// Handle overflow behavior based on banner position in creation mode\r\n\tuseEffect(() => {\r\n\t\t// Use a small delay to ensure this runs after resetHeightofBanner\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\t// Only apply overflow hidden in creation/edit mode when position is \"Cover Top\"\r\n\t\t\t// This component is only rendered in creation mode (when !showBannerenduser)\r\n\t\t\t// Preview mode is handled by separate preview components\r\n\t\t\tif (Bposition === \"Cover Top\") {\r\n\t\t\t\tdocument.body.style.setProperty(\"overflow\", \"hidden\", \"important\");\r\n\t\t\t} else {\r\n\t\t\t\t// Restore normal scrolling for other positions\r\n\t\t\t\tdocument.body.style.removeProperty(\"overflow\");\r\n\t\t\t}\r\n\t\t}, 100); // Small delay to ensure it runs after resetHeightofBanner\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tclearTimeout(timeoutId);\r\n\t\t\tdocument.body.style.removeProperty(\"overflow\");\r\n\t\t};\r\n\t}, [Bposition]); // Re-run when position changes\r\n\r\n\tconst overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\tconst [showOptions, setShowOptions] = useState(false);\r\n\tconst [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null);\r\n\tconst [showEmojiPicker, setShowEmojiPicker] = useState(false);\r\n\r\n\tconst handleDeleteRTESection = (index: number) => {\r\n\t\tconst newTextAreas = [...textAreas];\r\n\t\tnewTextAreas.splice(index, 1);\r\n\t\tsetTextAreas(newTextAreas);\r\n\t\tsetTextArray(newTextAreas);\r\n\r\n\t};\r\n\r\n\tconst addTextAreaInSameRow = (rowIndex: number, option: string) => {\r\n\t\tif (!textAreas[rowIndex]) {\r\n\t\t\ttextAreas[rowIndex] = [];\r\n\t\t}\r\n\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\t// Check if a Rich Text or Button already exists in the row\r\n\t\tif (\r\n\t\t\t(option === \"richText\" && existingRow.some((item) => item.name === \"Rich Text\")) ||\r\n\t\t\t(option === \"button\" && existingRow.some((item) => item.name === \"Button\"))\r\n\t\t) {\r\n\t\t\talert(`Only one ${option === \"richText\" ? \"Rich Text\" : \"Button\"} is allowed per row.`);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet newValue: JSX.Element | string;\r\n\t\tlet newName: string = \"\";\r\n\r\n\t\tswitch (option) {\r\n\t\t\tcase \"image\":\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<ImageSectionField\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\tsetImageSrc={setImageSrc}\r\n\t\t\t\t\t\timageSrc={imageSrc}\r\n\t\t\t\t\t\tsetImageName={setImageName}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Image\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"richText\":\r\n\t\t\t\t// setBannerButtonSelected(false);\r\n\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\tindex={rowIndex}\r\n\t\t\t\t\t\thandleDeleteRTESection={handleDeleteRTESection}\r\n\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Rich Text\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"button\":\r\n\t\t\t\tsetBannerButtonSelected(true);\r\n\r\n\t\t\t\t// CRITICAL FIX: Ensure button container exists when adding button through banner editor\r\n\t\t\t\t// This creates the actual button data that ButtonSection component needs to display buttons\r\n\t\t\t\tif (buttonsContainer.length === 0) {\r\n\t\t\t\t\tconst newButtonContainer = {\r\n\t\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\t\tbuttons: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\t\t\t\tname: \"Got It\",\r\n\t\t\t\t\t\t\t\tposition: \"center\" as const,\r\n\t\t\t\t\t\t\t\ttype: \"primary\" as const,\r\n\t\t\t\t\t\t\t\tisEditing: false,\r\n\t\t\t\t\t\t\t\tindex: 0,\r\n\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\tborderColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#ffffff\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tactions: {\r\n\t\t\t\t\t\t\t\t\tvalue: \"close\" as const,\r\n\t\t\t\t\t\t\t\t\ttargetURL: \"\",\r\n\t\t\t\t\t\t\t\t\ttab: \"same-tab\" as const,\r\n\t\t\t\t\t\t\t\t\tinteraction: null,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tsurvey: null,\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// Add the button container to the store\r\n\t\t\t\t\tsetButtonsContainer([newButtonContainer]);\r\n\r\n\t\t\t\t\tconsole.log(\"🔄 Banner editor: Created new button container for banner tour\", {\r\n\t\t\t\t\t\tcontainerId: newButtonContainer.id,\r\n\t\t\t\t\t\tbuttonCount: newButtonContainer.buttons.length\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Button\";\r\n\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"html\":\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<div className=\"htmlbanner\">\r\n\t\t\t\t\t\t<HtmlSection\r\n\t\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\t\thtmlContent={htmlContent}\r\n\t\t\t\t\t\t\tsetHtmlContent={setHtmlContent}\r\n\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"HTML\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tnewValue = \"\";\r\n\t\t\t\tnewName = \"Text\";\r\n\t\t}\r\n\r\n\r\n\t\tconst newTextAreas = [...textAreas];\r\n\r\n\t\tnewTextAreas[rowIndex] = [...existingRow, { name: newName, value: newValue }];\r\n\t\tsetTextAreas(newTextAreas);\r\n\t\tsetTextArray(newTextAreas);\r\n\t};\r\n\r\n\tconst removeTextArea = (rowIndex: number, textAreaIndex: number) => {\r\n\t\tconst updatedTextAreas = textAreas.map((row, index) => {\r\n\t\t\tif (index === rowIndex) {\r\n\t\t\t  const filteredRow = row.filter((_, idx) => idx !== textAreaIndex);\r\n\r\n\t\t\t  // Check if there are any buttons remaining in the row after removal\r\n\t\t\t  const hasButtonInRow = filteredRow.some((t) => t.name === \"Button\");\r\n\t\t\t  setBannerButtonSelected(hasButtonInRow);\r\n\r\n\t\t\t  return filteredRow;\r\n\t\t\t}\r\n\t\t\treturn row;\r\n\t\t});\r\n\t\tif (textAreaIndex === 1)\r\n\t\t{\r\n\t\t\tclearBannerButtonDetials();\r\n\t\t}\r\n\t\telse if (textAreaIndex === 0)\r\n\t\t{\r\n\t\t\tsetRTEAnchorEl({\r\n\t\t\t\trteId: \"\",\r\n\t\t\t\tcontainerId: \"\",\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: null,\r\n\t\t\t});\r\n\r\n\t\t\tdeleteRTEContainer(rteAnchorEl.containerId);\r\n\t\t}\r\n\t\tsetTextAreas(updatedTextAreas);\r\n\t\tsetTextArray(updatedTextAreas);\r\n\r\n\t};\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\t// Check both the global progress state and the saved guide data for progress settings\r\n\t\tconst enableProgressFromData = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress;\r\n\t\tconst shouldShowProgress = progress || enableProgressFromData;\r\n\r\n\t\tif(!shouldShowProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\", padding:\"8px 0 0 0 !important\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: steps.length }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\",\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{fontSize:\"12px\", color: ProgressColor}} >\r\n\t\t\t\t\t\t Step {currentStep} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{    width: \"calc(50% - 410px)\",\r\n\t\t\t\t\tpadding: \"8px 0 0 0\"}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={Progress}\r\n                            sx={{'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\tconst style = bannerJson.GuideStep.find((step:any) => step.stepName === currentStep)?.Canvas as\r\n\t\t| Record<string, unknown>\r\n\t\t| undefined;\r\n// Apply overflow hidden to body when canvas position is \"Cover Top\" in creation mode\r\nuseEffect(() => {\r\n\tif (style?.Position === \"Cover Top\") {\r\n\t\tdocument.body.style.overflow = \"hidden\";\r\n\t} else {\r\n\t\tdocument.body.style.overflow = \"\";\r\n\t}\r\n\r\n\t// Cleanup function to restore overflow when component unmounts\r\n\treturn () => {\r\n\t\tdocument.body.style.overflow = \"\";\r\n\t};\r\n}, [style?.Position]); // Re-run when canvas position changes\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{/* <Box\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\twidth: \"100vw\",\r\n\t\t\t\t\theight: \"17vh\",\r\n\t\t\t\t\tborderColor: (style?.BorderColor as string) || \"defaultColor\",\r\n    zIndex: overlays ? 1300 : \"\",\r\n    backgroundColor: style?.BackgroundColor as string | undefined,\r\n\t\t\t\t}}\r\n\t\t\t/> */}\r\n\r\n\t\t\t<div className=\"qadpt-container creation\">\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-box\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tpadding: style?.Padding !== undefined && style?.Padding !== null ? `${style.Padding}px` : \"10px\",\r\n\t\t\t\t\t\tboxShadow: (style && (style?.Position === \"Push Down\")) ?  \"none\" : \"0px 1px 15px rgba(0, 0, 0, 0.7)\",\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t`${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important` ,\r\n\r\n\t\t\t\t\tborderBottom: `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t,\r\n\t\t\t\t\tbackgroundColor: `${style?.BackgroundColor} !important ` || \"#f0f0f0\",\r\n\t\t\t\t\tposition: style?.Position || \"Cover Top\",\r\n\t\t\t\t\tzIndex: style?.Zindex || 9999,\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\tref={guidePopUpRef}\r\n\t\t\t>\r\n\r\n\r\n{(textArray ).map((row: any, rowIndex: number) => (\r\n  <Box key={rowIndex} className=\"qadpt-row\">\r\n    {row.map((textArea: any, textAreaIndex: number) => (\r\n      <Box key={textAreaIndex} className=\"qadpt-text-area-wrapper\">\r\n        <div\r\n          className=\"qadpt-text-area\"\r\n          style={{\r\n            backgroundColor:\r\n\t\t\t\t  (textArea.name === \"RTE\" || textArea.name === \"Rich Text\") && sectionColor ? sectionColor : \"\",\r\n          }}\r\n        >\r\n\t\t\t\t{textArea.value}\r\n        </div>\r\n          {textArea.name === \"Button\" && (\r\n            <IconButton\r\n              className=\"qadpt-add-btn\"\r\n              size=\"large\"\r\n              onClick={() => removeTextArea(rowIndex, textAreaIndex)}\r\n            >\r\n              <DeleteOutlineOutlinedIcon />\r\n            </IconButton>\r\n          )}\r\n      </Box>\r\n    ))}\r\n\r\n\r\n      <Box display=\"flex\" alignItems=\"center\">\r\n\t  {row.some((item: any) => item.name === \"Button\") ? (\r\n    \t<Tooltip title=\"Only one button is allowed\" placement=\"bottom\">\r\n          <span>\r\n            <IconButton\r\n              onClick={() => {\r\n                setShowOptions(true);\r\n                setFocusedRowIndex(rowIndex);\r\n              }}\r\n              className=\"qadpt-add-btn\"\r\n              size=\"small\"\r\n\t\t\t  disabled={true}\r\n\t\t\t\t\t>\r\n              <AddIcon />\r\n            </IconButton>\r\n          </span>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t) : (\r\n\t\t\t\t<IconButton\r\n\t\t\t\tonClick={() => {\r\n\t\t\t\t  setShowOptions(true);\r\n\t\t\t\t  setFocusedRowIndex(rowIndex);\r\n\t\t\t\t}}\r\n\t\t\t\tclassName=\"qadpt-add-btn\"\r\n\t\t\t\tsize=\"small\"\r\n\t\t\t\tdisabled={row.some((item: any) => item.name === \"Button\")}\r\n\t\t\t  >\r\n\t\t\t\t<AddIcon />\r\n\t\t\t  </IconButton>\r\n\t\t\t)}\r\n        {/* Always render the dismiss icon, but conditionally show/hide it based on dismissData.dismisssel */}\r\n        <IconButton\r\n          sx={{\r\n\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\t\tpadding:\"3px\",\r\n            boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n            marginLeft: \"2px\",\r\n            background: \"#fff !important\",\r\n            border: \"1px solid #ccc\",\r\n            zIndex:\"999999\",\r\n            display: dismissData?.dismisssel ? \"flex\" : \"none\", // Show/hide based on dismisssel\r\n          }}\r\n        >\r\n\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\" }}   />\r\n        </IconButton>\r\n      </Box>\r\n\t  {isUnSavedChanges && openWarning &&(\r\n\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n    {showOptions && focusedRowIndex === rowIndex && (\r\n      <Box\r\n        onMouseEnter={() => setShowOptions(true)}\r\n        onMouseLeave={() => setShowOptions(false)}\r\n        className=\"qadpt-options-menu\"\r\n      >\r\n        <Box className=\"qadpt-options-content\">\r\n          <Box\r\n            display=\"flex\"\r\n            flexDirection=\"row\"\r\n            alignItems=\"center\"\r\n            sx={{ cursor: \"pointer\",gap : \"10px\",placeContent:\"center\",width:\"100%\" }}\r\n\r\n            onClick={() => addTextAreaInSameRow(rowIndex, \"button\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t<Link />\r\n\t\t\t<Typography variant=\"caption\">Button</Typography>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n    )}\r\n  </Box>\r\n))}\r\n{(savedGuideData?.GuideType === \"Tour\" || savedGuideData?.GuideType === \"Announcement\") &&\r\n\r\n<Box\r\nsx={{\r\n  ...(progressTemplate === \"linear\" && {\r\n\tdisplay: \"flex\",\r\n\tplaceContent: \"center\",\r\n\talignItems:\"center\"\r\n  }),\r\n}}\r\n>\r\n\t\t{steps.length >= 1 ? (\r\n                    <>\r\n                        {renderProgress()}\r\n                    </>\r\n                ) : (\r\n                    null\r\n                )}\r\n\t\t\t\t</Box>\r\n\r\n}\r\n\t\t\t\t\t{showEmojiPicker && <EmojiPicker onEmojiClick={() => {}} />}\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t\t{buttonProperty  && <ButtonSettings />}\r\n\r\n\t\t\t{/* {dismissData?.dismisssel && (\r\n\t\t\t\t<IconButton\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\ttop: \"8%\",\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tcolor: \"red\",\r\n\t\t\t\t\t\tborderRadius: \"2px\",\r\n\t\t\t\t\t\t//padding: \"5px\",\r\n\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\tzIndex: \"999\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t</IconButton>\r\n\t\t\t)} */}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default Banner;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,EAAEC,cAAc,QAAQ,eAAe;AAClG,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,SAAqBC,IAAI,QAAQ,qBAAqB;AACtD,OAAOC,UAAU,MAAM,0CAA0C;AACjE,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAO,mBAAmB;AAC1B,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAiC,yBAAyB;AAC/E,OAAOC,cAAc,MAAM,sCAAsC;AAEjE,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,MAAM,GAAGA,CAAC;EACfC,WAAW;EACXC,QAAQ;EACRC,UAAU;EACVC,cAAc;EACdC,WAAW;EACXC,WAAW;EACXC,cAAc;EACdC,YAAY;EACZC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,SAAS;EACTC,YAAY;EACZC,QAAQ;EACRC,QAAQ;EACRC,WAAW;EACXC,SAAS;EACTC,YAAY;EACZC,QAAQ;EACRC,WAAW;EACXC,gBAAgB;EAChBC,WAAW;EACXC,cAAc;EACdC,WAAW;EACXC,cAAc;EACdC;AA4BD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACL,MAAMC,aAAa,GAAGzD,MAAM,CAAwB,IAAI,CAAC;EACzD,MAAM;IACL0D,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,cAAc;IACdC,iBAAiB;IACjBC,WAAW;IACXC,YAAY;IACZC,WAAW;IACXC,SAAS;IACTC,uBAAuB;IACvBC,YAAY;IACZC,OAAO;IACPC,UAAU;IACVC,oBAAoB;IACpBC,iBAAiB;IACjBC,UAAU;IACVC,YAAY;IACZC,cAAc;IACdC,gBAAgB;IAChBC,mBAAmB;IACnBC,wBAAwB;IACxBC,cAAc;IACdC,kBAAkB;IAClBC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,aAAa;IACbC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC,YAAY;IACZC,gBAAgB;IAChBC,gCAAgC;IAChCC;EACD,CAAC,GAAG3E,cAAc,CAAE4E,KAAK,IAAKA,KAAK,CAAC;EAEpC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhG,QAAQ,CAAoD,CAC7F,CACE;IACDiG,IAAI,EAAE,WAAW;IACjBC,KAAK,eACH3E,OAAA,CAACX,UAAU;MAEZiB,UAAU,EAAEA,UAAW;MACvBW,QAAQ,EAAE,IAAK;MACf2D,KAAK,EAAE,CAAE;MACTC,sBAAsB,EAAEA,CAAA,KAAM,CAAC;MAC/B;MAAA;MACAC,GAAG,EAAExE,UAAW;MAChB6B,aAAa,EAAEA;IAAc,GAPxB,CAAC;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQJ;EAEF,CAAC,CACF,CACC,CAAC;;EAEF;EACAvG,SAAS,CAAC,MAAM;IACjB;IACA,MAAMwG,yBAAyB,GAAGA,CAAA,KAAM;MACvC,MAAMC,QAAQ,GAAG,CAAC;MAClB,IAAIC,iBAAiB,GAAG,CAAC,GAAGb,SAAS,CAAC;MACtC,IAAIc,UAAU,GAAG,KAAK;;MAEtB;MACA,IAAIrC,oBAAoB,IAAI,CAACuB,SAAS,CAACY,QAAQ,CAAC,CAACG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC,EAAE;QACxF,MAAMe,SAAS,GAAG;UACjBf,IAAI,EAAE,QAAQ;UACdC,KAAK,eACJ3E,OAAA,CAACT,aAAa;YAEbkB,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BO,QAAQ,EAAE;UAAK,GAHV,CAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIN;QAEH,CAAC;QACDG,iBAAiB,CAACD,QAAQ,CAAC,GAAG,CAAC,GAAGC,iBAAiB,CAACD,QAAQ,CAAC,EAAEK,SAAS,CAAC;QACzEH,UAAU,GAAG,IAAI;MAClB;;MAEA;MACA;MACA,MAAMI,aAAa,GAAGpB,aAAa,IAAIA,aAAa,CAACqB,MAAM,GAAG,CAAC,IAC9DrB,aAAa,CAAC,CAAC,CAAC,CAACsB,IAAI,IAAItB,aAAa,CAAC,CAAC,CAAC,CAACsB,IAAI,CAACD,MAAM,GAAG,CAAC,IACzDrB,aAAa,CAAC,CAAC,CAAC,CAACsB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,IAAIvB,aAAa,CAAC,CAAC,CAAC,CAACsB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;;MAE7E;MACA;MACA,IAAIJ,aAAa,EAAE;QAClB;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA,MAAMK,iBAAiB,GAAGV,iBAAiB,CAACD,QAAQ,CAAC,CAACG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,WAAW,CAAC;QAE/F,IAAI,CAACqB,iBAAiB,EAAE;UACvB;UACA,MAAMC,UAAU,GAAG;YAClBtB,IAAI,EAAE,WAAW;YACjBC,KAAK,eACJ3E,OAAA,CAACX,UAAU;cAEViB,UAAU,EAAEA,UAAW;cACvBW,QAAQ,EAAE,IAAK;cACf2D,KAAK,EAAE,CAAE;cACTC,sBAAsB,EAAEA,CAAA,KAAM,CAAC;cAC/B;cAAA;cACAC,GAAG,EAAExE,UAAW;cAChB6B,aAAa,EAAEA;YAAc,GAPxB,CAAC;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQN;UAEH,CAAC;UACDG,iBAAiB,CAACD,QAAQ,CAAC,GAAG,CAACY,UAAU,EAAE,GAAGX,iBAAiB,CAACD,QAAQ,CAAC,CAACa,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK,WAAW,CAAC,CAAC;UACpHY,UAAU,GAAG,IAAI;QAClB;MACD;;MAEA;MACA,IAAIA,UAAU,EAAE;QACfb,YAAY,CAACY,iBAAiB,CAAC;MAChC;IACD,CAAC;;IAED;IACA,MAAMa,SAAS,GAAGC,UAAU,CAAChB,yBAAyB,EAAE,GAAG,CAAC;IAE5D,OAAO,MAAMiB,YAAY,CAACF,SAAS,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACFvH,SAAS,CAAC,MAAM;IACf,MAAMyG,QAAQ,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;IACvC,IAAInC,oBAAoB,EAAE;MACxB;;MAGA;MACA,IAAI,CAACoD,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC,EAAE;QAC1D,MAAMe,SAAS,GAAG;UAChBf,IAAI,EAAE,QAAQ;UACdC,KAAK,eACN3E,OAAA,CAACT,aAAa;YAEZkB,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BO,QAAQ,EAAE;UAAK,GAHV,CAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIP;QAEF,CAAC;QAED,MAAMoB,gBAAgB,GAAG,CAAC,GAAG9B,SAAS,CAAC;QACvC8B,gBAAgB,CAAClB,QAAQ,CAAC,GAAG,CAAC,GAAGiB,WAAW,EAAEZ,SAAS,CAAC;QACxDhB,YAAY,CAAC6B,gBAAgB,CAAC;MAC7B;IACF,CAAC,MAAM;MACN;MACA,MAAMC,WAAW,GAAGF,WAAW,CAACG,SAAS,CAAEhB,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC;MAC3E,IAAI6B,WAAW,KAAK,CAAC,CAAC,EAAE;QACvBE,cAAc,CAACrB,QAAQ,EAAEmB,WAAW,CAAC;MACtC;IACD;EACC,CAAC,EAAE,CAACtD,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAE5B;EACAtE,SAAS,CAAC,MAAM;IACjBmE,YAAY,CAAC0B,SAAS,CAAC;EACtB,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA7F,SAAS,CAAC,MAAM;IACjB;IACA,IAAI,CAACwF,YAAY,IAAIC,gBAAgB,KAAK,cAAc,EAAE;IAE1DsC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;IAE1E;IACAtC,gCAAgC,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,CAACF,YAAY,EAAEC,gBAAgB,EAAEC,gCAAgC,CAAC,CAAC;;EAEtE;EACA1F,SAAS,CAAC,MAAM;IACjB;IACA,IAAI,CAACwF,YAAY,EAAE;IAEnB,MAAMiB,QAAQ,GAAG,CAAC;IAClB,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;;IAEvC;IACA,MAAMwB,eAAe,GAAG3D,oBAAoB;IAC5C,MAAM4D,oBAAoB,GAAGR,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC;IAE/E,IAAIkC,eAAe,IAAI,CAACC,oBAAoB,EAAE;MAC7C;MACA,MAAMpB,SAAS,GAAG;QAChBf,IAAI,EAAE,QAAQ;QACdC,KAAK,eACN3E,OAAA,CAACT,aAAa;UAEZkB,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BO,QAAQ,EAAE;QAAK,GAHV,CAAC;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIP;MAEF,CAAC;MAED,MAAMoB,gBAAgB,GAAG,CAAC,GAAG9B,SAAS,CAAC;MACvC8B,gBAAgB,CAAClB,QAAQ,CAAC,GAAG,CAAC,GAAGiB,WAAW,EAAEZ,SAAS,CAAC;MACxDhB,YAAY,CAAC6B,gBAAgB,CAAC;IAC/B;EACC,CAAC,EAAE,CAACnC,YAAY,EAAElB,oBAAoB,EAAExC,WAAW,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEvE;EACA/B,SAAS,CAAC,MAAM;IACjB,IAAI,CAACwF,YAAY,EAAE;;IAEnB;IACA,MAAMiB,QAAQ,GAAG,CAAC;IAClB,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;IACvC,MAAMyB,oBAAoB,GAAGR,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC;;IAE/E;IACA,IAAIpB,gBAAgB,CAACqC,MAAM,GAAG,CAAC,IAAI,CAACkB,oBAAoB,IAAI,CAAC5D,oBAAoB,EAAE;MAClFJ,uBAAuB,CAAC,IAAI,CAAC;IAC9B;EACC,CAAC,EAAE,CAACsB,YAAY,EAAEb,gBAAgB,EAAEkB,SAAS,EAAEvB,oBAAoB,EAAEJ,uBAAuB,CAAC,CAAC,CAAC,CAAC;;EAElG;EACAlE,SAAS,CAAC,MAAM;IACf;IACA,MAAMuH,SAAS,GAAGC,UAAU,CAAC,MAAM;MAClC;MACA;MACA;MACA,IAAI/E,SAAS,KAAK,WAAW,EAAE;QAC9B0F,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;MACnE,CAAC,MAAM;QACN;QACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC;MAC/C;IACD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET;IACA,OAAO,MAAM;MACZd,YAAY,CAACF,SAAS,CAAC;MACvBY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC;IAC/C,CAAC;EACF,CAAC,EAAE,CAAC9F,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEjB,MAAM+F,cAAc,GAAGxH,cAAc,CAAE4E,KAAK,IAAKA,KAAK,CAAC4C,cAAc,CAAC;EACtE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6I,eAAe,EAAEC,kBAAkB,CAAC,GAAG9I,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAAC+I,eAAe,EAAEC,kBAAkB,CAAC,GAAGhJ,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMoG,sBAAsB,GAAID,KAAa,IAAK;IACjD,MAAM8C,YAAY,GAAG,CAAC,GAAGlD,SAAS,CAAC;IACnCkD,YAAY,CAACC,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAAC;IAC7BH,YAAY,CAACiD,YAAY,CAAC;IAC1B5E,YAAY,CAAC4E,YAAY,CAAC;EAE3B,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAACxC,QAAgB,EAAEyC,MAAc,KAAK;IAClE,IAAI,CAACrD,SAAS,CAACY,QAAQ,CAAC,EAAE;MACzBZ,SAAS,CAACY,QAAQ,CAAC,GAAG,EAAE;IACzB;IAEA,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;IACvC;IACA,IACEyC,MAAM,KAAK,UAAU,IAAIxB,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,WAAW,CAAC,IAC9EmD,MAAM,KAAK,QAAQ,IAAIxB,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAE,EAC1E;MACDoD,KAAK,CAAC,YAAYD,MAAM,KAAK,UAAU,GAAG,WAAW,GAAG,QAAQ,sBAAsB,CAAC;MACvF;IACD;IAEA,IAAIE,QAA8B;IAClC,IAAIC,OAAe,GAAG,EAAE;IAExB,QAAQH,MAAM;MACb,KAAK,OAAO;QACXE,QAAQ,gBACP/H,OAAA,CAACV,iBAAiB;UAEjBc,WAAW,EAAEA,WAAY;UACzBC,QAAQ,EAAEA,QAAS;UACnBM,YAAY,EAAEA;QAAa,GAHtByE,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIb,CACD;QACD8C,OAAO,GAAG,OAAO;QACjB;MACD,KAAK,UAAU;QACd;;QAEAD,QAAQ,gBACP/H,OAAA,CAACX,UAAU;UAEViB,UAAU,EAAEA,UAAW;UACvBW,QAAQ,EAAE,IAAK;UACf2D,KAAK,EAAEQ,QAAS;UAChBP,sBAAsB,EAAEA;UACxB;UAAA;UACAC,GAAG,EAAExE,UAAW;UAChB6B,aAAa,EAAEA;QAAc,GAPxBiD,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQb,CACD;QACD8C,OAAO,GAAG,WAAW;QACrB;MACD,KAAK,QAAQ;QACZnF,uBAAuB,CAAC,IAAI,CAAC;;QAE7B;QACA;QACA,IAAIS,gBAAgB,CAACqC,MAAM,KAAK,CAAC,EAAE;UAClC,MAAMsC,kBAAkB,GAAG;YAC1BC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;YACvBC,OAAO,EAAE,CACR;cACCH,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;cACvB1D,IAAI,EAAE,QAAQ;cACd4D,QAAQ,EAAE,QAAiB;cAC3BC,IAAI,EAAE,SAAkB;cACxBC,SAAS,EAAE,KAAK;cAChB5D,KAAK,EAAE,CAAC;cACRoC,KAAK,EAAE;gBACNyB,eAAe,EAAE,SAAS;gBAC1BC,WAAW,EAAE,SAAS;gBACtBC,KAAK,EAAE;cACR,CAAC;cACDC,OAAO,EAAE;gBACRjE,KAAK,EAAE,OAAgB;gBACvBkE,SAAS,EAAE,EAAE;gBACbC,GAAG,EAAE,UAAmB;gBACxBC,WAAW,EAAE;cACd,CAAC;cACDC,MAAM,EAAE;YACT,CAAC,CACD;YACDhC,KAAK,EAAE;cACNyB,eAAe,EAAE;YAClB;UACD,CAAC;;UAED;UACAlF,mBAAmB,CAAC,CAAC0E,kBAAkB,CAAC,CAAC;UAEzCvB,OAAO,CAACC,GAAG,CAAC,gEAAgE,EAAE;YAC7EsC,WAAW,EAAEhB,kBAAkB,CAACC,EAAE;YAClCgB,WAAW,EAAEjB,kBAAkB,CAACI,OAAO,CAAC1C;UACzC,CAAC,CAAC;QACH;QAEAoC,QAAQ,gBACP/H,OAAA,CAACT,aAAa;UAEbkB,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BO,QAAQ,EAAE;QAAK,GAHVmE,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIb,CACD;QACD8C,OAAO,GAAG,QAAQ;QAElB;MACD,KAAK,MAAM;QACVD,QAAQ,gBACP/H,OAAA;UAAKmJ,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC1BpJ,OAAA,CAACR,WAAW;YAEXgB,WAAW,EAAEA,WAAY;YACzBD,cAAc,EAAEA,cAAe;YAC/BU,QAAQ,EAAE;UAAK,GAHVmE,QAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACL;QACD8C,OAAO,GAAG,MAAM;QAChB;MACD;QACCD,QAAQ,GAAG,EAAE;QACbC,OAAO,GAAG,MAAM;IAClB;IAGA,MAAMN,YAAY,GAAG,CAAC,GAAGlD,SAAS,CAAC;IAEnCkD,YAAY,CAACtC,QAAQ,CAAC,GAAG,CAAC,GAAGiB,WAAW,EAAE;MAAE3B,IAAI,EAAEsD,OAAO;MAAErD,KAAK,EAAEoD;IAAS,CAAC,CAAC;IAC7EtD,YAAY,CAACiD,YAAY,CAAC;IAC1B5E,YAAY,CAAC4E,YAAY,CAAC;EAC3B,CAAC;EAED,MAAMjB,cAAc,GAAGA,CAACrB,QAAgB,EAAEiE,aAAqB,KAAK;IACnE,MAAM/C,gBAAgB,GAAG9B,SAAS,CAAC8E,GAAG,CAAC,CAACC,GAAG,EAAE3E,KAAK,KAAK;MACtD,IAAIA,KAAK,KAAKQ,QAAQ,EAAE;QACtB,MAAMoE,WAAW,GAAGD,GAAG,CAACtD,MAAM,CAAC,CAACwD,CAAC,EAAEC,GAAG,KAAKA,GAAG,KAAKL,aAAa,CAAC;;QAEjE;QACA,MAAMM,cAAc,GAAGH,WAAW,CAACjE,IAAI,CAAEqE,CAAC,IAAKA,CAAC,CAAClF,IAAI,KAAK,QAAQ,CAAC;QACnE7B,uBAAuB,CAAC8G,cAAc,CAAC;QAEvC,OAAOH,WAAW;MACpB;MACA,OAAOD,GAAG;IACX,CAAC,CAAC;IACF,IAAIF,aAAa,KAAK,CAAC,EACvB;MACC7F,wBAAwB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI6F,aAAa,KAAK,CAAC,EAC5B;MACC5F,cAAc,CAAC;QACdoG,KAAK,EAAE,EAAE;QACTZ,WAAW,EAAE,EAAE;QACf;QACAtE,KAAK,EAAE;MACR,CAAC,CAAC;MAEFjB,kBAAkB,CAACC,WAAW,CAACsF,WAAW,CAAC;IAC5C;IACAxE,YAAY,CAAC6B,gBAAgB,CAAC;IAC9BxD,YAAY,CAACwD,gBAAgB,CAAC;EAE/B,CAAC;EACD,MAAMwD,cAAc,GAAG,CAAAlI,cAAc,aAAdA,cAAc,wBAAAG,qBAAA,GAAdH,cAAc,CAAEmI,SAAS,cAAAhI,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCnC,OAAO,cAAAoC,sBAAA,uBAAvCA,sBAAA,CAAyC+H,cAAc,KAAI,KAAK;EACvF,SAASC,mBAAmBA,CAACnG,cAAmB,EAAE;IAAA,IAAAoG,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACjD,IAAItG,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MACU,IAAIA,cAAc,KAAK,CAAC,EAAE;MACpC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAAlC,cAAc,aAAdA,cAAc,wBAAAsI,sBAAA,GAAdtI,cAAc,CAAEmI,SAAS,cAAAG,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCtK,OAAO,cAAAuK,sBAAA,uBAAvCA,sBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAACnG,cAAc,CAAC;EAC5D,MAAMyG,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC5B;IACA,MAAMC,sBAAsB,GAAG/I,cAAc,aAAdA,cAAc,wBAAA4I,sBAAA,GAAd5I,cAAc,CAAEmI,SAAS,cAAAS,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC5K,OAAO,cAAA6K,sBAAA,uBAAvCA,sBAAA,CAAyCV,cAAc;IACtF,MAAMY,kBAAkB,GAAG1G,QAAQ,IAAIyG,sBAAsB;IAE7D,IAAG,CAACC,kBAAkB,EAAE,OAAO,IAAI;IAEnC,IAAIN,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCtK,OAAA,CAACjB,aAAa;QACb8L,OAAO,EAAC,MAAM;QACd5G,KAAK,EAAEA,KAAK,CAAC0B,MAAO;QACpB2C,QAAQ,EAAC,QAAQ;QACjBwC,UAAU,EAAEjH,WAAW,GAAG,CAAE;QAC5BkH,EAAE,EAAE;UAAEtC,eAAe,EAAE,aAAa;UAAEuC,OAAO,EAAC,sBAAsB;UAAG,+BAA+B,EAAE;YACrFvC,eAAe,EAAE1E,aAAa,CAAE;UAClC;QAAG,CAAE;QACtBkH,UAAU,eAAEjL,OAAA,CAAChB,MAAM;UAACgI,KAAK,EAAE;YAAEkE,UAAU,EAAE;UAAS;QAAE;UAAAnG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDiG,UAAU,eAAEnL,OAAA,CAAChB,MAAM;UAACgI,KAAK,EAAE;YAAEkE,UAAU,EAAE;UAAS;QAAE;UAAAnG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACM,IAAIoF,gBAAgB,KAAK,aAAa,EAAE;MAC7C,oBACatK,OAAA,CAACnB,GAAG;QAACkM,EAAE,EAAE;UAACC,OAAO,EAAC,sBAAsB;UAACI,OAAO,EAAE,MAAM;UACnEC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,QAAQ;UACtBC,GAAG,EAAE;QAAK,CAAE;QAAAnC,QAAA,EAGIoC,KAAK,CAACC,IAAI,CAAC;UAAE9F,MAAM,EAAE1B,KAAK,CAAC0B;QAAO,CAAC,CAAC,CAAC2D,GAAG,CAAC,CAACG,CAAC,EAAE7E,KAAK,kBACjD5E,OAAA;UAEEgH,KAAK,EAAE;YACL0E,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACblD,eAAe,EAAE7D,KAAK,KAAKf,WAAW,GAAG,CAAC,GAAGE,aAAa,GAAG,SAAS;YAAE;YACxE6H,YAAY,EAAE;UAChB;QAAE,GANGhH,KAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAED,CAAC;IAEpB;IACA,IAAIoF,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCtK,OAAA,CAACnB,GAAG;QAACkM,EAAE,EAAE;UAACC,OAAO,EAAC,sBAAsB;UAACI,OAAO,EAAE,MAAM;UACxDC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE;QACd,CAAE;QAAAlC,QAAA,eACDpJ,OAAA,CAAClB,UAAU;UAACiM,EAAE,EAAE;YAACc,QAAQ,EAAC,MAAM;YAAElD,KAAK,EAAE5E;UAAa,CAAE;UAAAqF,QAAA,GAAE,OACnD,EAACvF,WAAW,EAAC,MAAI,EAACI,KAAK,CAAC0B,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAIoF,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCtK,OAAA,CAACnB,GAAG;QAACkM,EAAE,EAAE;UAAKW,KAAK,EAAE,mBAAmB;UACvCV,OAAO,EAAE;QAAW,CAAE;QAAA5B,QAAA,eACtBpJ,OAAA,CAAClB,UAAU;UAAC+L,OAAO,EAAC,OAAO;UAAAzB,QAAA,eAC1BpJ,OAAA,CAACf,cAAc;YACd4L,OAAO,EAAC,aAAa;YACrBlG,KAAK,EAAE9C,QAAS;YACKkJ,EAAE,EAAE;cAAC,0BAA0B,EAAE;gBAC7BtC,eAAe,EAAE1E,aAAa,CAAE;cAClC;YAAE;UAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,MAAM8B,KAAK,IAAA9E,qBAAA,GAAG0B,UAAU,CAACmG,SAAS,CAAC+B,IAAI,CAAEC,IAAQ,IAAKA,IAAI,CAACC,QAAQ,KAAKnI,WAAW,CAAC,cAAA3B,qBAAA,uBAAtEA,qBAAA,CAAwE+J,MAE1E;EACb;EACAtN,SAAS,CAAC,MAAM;IACf,IAAI,CAAAqI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkF,QAAQ,MAAK,WAAW,EAAE;MACpCpF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACmF,QAAQ,GAAG,QAAQ;IACxC,CAAC,MAAM;MACNrF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACmF,QAAQ,GAAG,EAAE;IAClC;;IAEA;IACA,OAAO,MAAM;MACZrF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACmF,QAAQ,GAAG,EAAE;IAClC,CAAC;EACF,CAAC,EAAE,CAACnF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkF,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtB,oBACClM,OAAA,CAAAE,SAAA;IAAAkJ,QAAA,gBAcCpJ,OAAA;MAAKmJ,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACxCpJ,OAAA,CAACnB,GAAG;QACHsK,SAAS,EAAC,WAAW;QACrB4B,EAAE,EAAE;UACHC,OAAO,EAAE,CAAAhE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoF,OAAO,MAAKC,SAAS,IAAI,CAAArF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoF,OAAO,MAAK,IAAI,GAAG,GAAGpF,KAAK,CAACoF,OAAO,IAAI,GAAG,MAAM;UAChGE,SAAS,EAAGtF,KAAK,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkF,QAAQ,MAAK,WAAY,GAAK,MAAM,GAAG,iCAAiC;UACrGK,SAAS,EACP,GAAG,CAAAvF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwF,UAAU,KAAI,CAAC,YAAY,CAAAxF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyF,WAAW,KAAI,SAAS,aAAa;UAElFC,WAAW,EACV,GAAG,CAAA1F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwF,UAAU,KAAI,CAAC,YAAY,CAAAxF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyF,WAAW,KAAI,SAAS,aAAa;UAElFE,UAAU,EACX,GAAG,CAAA3F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwF,UAAU,KAAI,CAAC,YAAY,CAAAxF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyF,WAAW,KAAI,SAAS,aAAa;UAElFG,YAAY,EAAE,GAAG,CAAA5F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwF,UAAU,KAAI,CAAC,YAAY,CAAAxF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyF,WAAW,KAAI,SAAS,aAAa;UAE/FhE,eAAe,EAAE,GAAGzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,eAAe,cAAc,IAAI,SAAS;UACrEvE,QAAQ,EAAE,CAAAtB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkF,QAAQ,KAAI,WAAW;UACxCY,MAAM,EAAE,CAAA9F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+F,MAAM,KAAI;QAC1B,CAAE;QACF7E,EAAE,EAAC,aAAa;QAChBpD,GAAG,EAAE3C,aAAc;QAAAiH,QAAA,GAIrBxG,SAAS,CAAG0G,GAAG,CAAC,CAACC,GAAQ,EAAEnE,QAAgB,kBAC3CpF,OAAA,CAACnB,GAAG;UAAgBsK,SAAS,EAAC,WAAW;UAAAC,QAAA,GACtCG,GAAG,CAACD,GAAG,CAAC,CAAC0D,QAAa,EAAE3D,aAAqB,kBAC5CrJ,OAAA,CAACnB,GAAG;YAAqBsK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAC1DpJ,OAAA;cACEmJ,SAAS,EAAC,iBAAiB;cAC3BnC,KAAK,EAAE;gBACLyB,eAAe,EACrB,CAACuE,QAAQ,CAACtI,IAAI,KAAK,KAAK,IAAIsI,QAAQ,CAACtI,IAAI,KAAK,WAAW,KAAKrC,YAAY,GAAGA,YAAY,GAAG;cACxF,CAAE;cAAA+G,QAAA,EAEP4D,QAAQ,CAACrI;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACH8H,QAAQ,CAACtI,IAAI,KAAK,QAAQ,iBACzB1E,OAAA,CAACpB,UAAU;cACTuK,SAAS,EAAC,eAAe;cACzB8D,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMzG,cAAc,CAACrB,QAAQ,EAAEiE,aAAa,CAAE;cAAAD,QAAA,eAEvDpJ,OAAA,CAACb,yBAAyB;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACb;UAAA,GAlBKmE,aAAa;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBlB,CACN,CAAC,eAGAlF,OAAA,CAACnB,GAAG;YAACuM,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAAAjC,QAAA,GACzCG,GAAG,CAAChE,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC,gBAC9C1E,OAAA,CAACH,OAAO;cAACsN,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,QAAQ;cAAAhE,QAAA,eACzDpJ,OAAA;gBAAAoJ,QAAA,eACEpJ,OAAA,CAACpB,UAAU;kBACTsO,OAAO,EAAEA,CAAA,KAAM;oBACb7F,cAAc,CAAC,IAAI,CAAC;oBACpBE,kBAAkB,CAACnC,QAAQ,CAAC;kBAC9B,CAAE;kBACF+D,SAAS,EAAC,eAAe;kBACzB8D,IAAI,EAAC,OAAO;kBACrBI,QAAQ,EAAE,IAAK;kBAAAjE,QAAA,eAENpJ,OAAA,CAACd,OAAO;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,gBAEVlF,OAAA,CAACpB,UAAU;cACXsO,OAAO,EAAEA,CAAA,KAAM;gBACb7F,cAAc,CAAC,IAAI,CAAC;gBACpBE,kBAAkB,CAACnC,QAAQ,CAAC;cAC9B,CAAE;cACF+D,SAAS,EAAC,eAAe;cACzB8D,IAAI,EAAC,OAAO;cACZI,QAAQ,EAAE9D,GAAG,CAAChE,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAE;cAAA0E,QAAA,eAE1DpJ,OAAA,CAACd,OAAO;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACb,eAEIlF,OAAA,CAACpB,UAAU;cACTmM,EAAE,EAAE;gBACR;gBACAC,OAAO,EAAC,KAAK;gBACPsB,SAAS,EAAE,iCAAiC;gBAC5CgB,UAAU,EAAE,KAAK;gBACjBC,UAAU,EAAE,iBAAiB;gBAC7BC,MAAM,EAAE,gBAAgB;gBACxBV,MAAM,EAAC,QAAQ;gBACf1B,OAAO,EAAEhJ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEqL,UAAU,GAAG,MAAM,GAAG,MAAM,CAAE;cACtD,CAAE;cAAArE,QAAA,eAEPpJ,OAAA,CAACN,SAAS;gBAAEqL,EAAE,EAAE;kBAAC2C,IAAI,EAAC,GAAG;kBAAC/E,KAAK,EAAC;gBAAO;cAAE;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACR1D,gBAAgB,IAAIC,WAAW,iBAChCzB,OAAA,CAACF,UAAU;YACT2B,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BC,WAAW,EAAEA;UAAY;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CACD,EACCkC,WAAW,IAAIE,eAAe,KAAKlC,QAAQ,iBAC1CpF,OAAA,CAACnB,GAAG;YACF8O,YAAY,EAAEA,CAAA,KAAMtG,cAAc,CAAC,IAAI,CAAE;YACzCuG,YAAY,EAAEA,CAAA,KAAMvG,cAAc,CAAC,KAAK,CAAE;YAC1C8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAE9BpJ,OAAA,CAACnB,GAAG;cAACsK,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCpJ,OAAA,CAACnB,GAAG;gBACFuM,OAAO,EAAC,MAAM;gBACdyC,aAAa,EAAC,KAAK;gBACnBxC,UAAU,EAAC,QAAQ;gBACnBN,EAAE,EAAE;kBAAE+C,MAAM,EAAE,SAAS;kBAACvC,GAAG,EAAG,MAAM;kBAACD,YAAY,EAAC,QAAQ;kBAACI,KAAK,EAAC;gBAAO,CAAE;gBAE1EwB,OAAO,EAAEA,CAAA,KAAMtF,oBAAoB,CAACxC,QAAQ,EAAE,QAAQ,CAAE;gBAAAgE,QAAA,gBAEhEpJ,OAAA,CAACZ,IAAI;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTlF,OAAA,CAAClB,UAAU;kBAAC+L,OAAO,EAAC,SAAS;kBAAAzB,QAAA,EAAC;gBAAM;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAlGOE,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGb,CACN,CAAC,EACD,CAAC,CAAAtD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmM,SAAS,MAAK,MAAM,IAAI,CAAAnM,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmM,SAAS,MAAK,cAAc,kBAEtF/N,OAAA,CAACnB,GAAG;UACJkM,EAAE,EAAE;YACF,IAAIT,gBAAgB,KAAK,QAAQ,IAAI;cACtCc,OAAO,EAAE,MAAM;cACfE,YAAY,EAAE,QAAQ;cACtBD,UAAU,EAAC;YACV,CAAC;UACH,CAAE;UAAAjC,QAAA,EAECnF,KAAK,CAAC0B,MAAM,IAAI,CAAC,gBACA3F,OAAA,CAAAE,SAAA;YAAAkJ,QAAA,EACKmB,cAAc,CAAC;UAAC,gBACnB,CAAC,GAEH;QACH;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGJsC,eAAe,iBAAIxH,OAAA,CAACP,WAAW;UAACuO,YAAY,EAAEA,CAAA,KAAM,CAAC;QAAE;UAAAjJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EACL3C,cAAc,iBAAKvC,OAAA,CAACJ,cAAc;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAmBrC,CAAC;AAEL,CAAC;AAACpD,EAAA,CA/wBI3B,MAAM;EAAA,QA4FPR,cAAc,EA4NKA,cAAc;AAAA;AAAAsO,EAAA,GAxThC9N,MAAM;AAixBZ,eAAeA,MAAM;AAAC,IAAA8N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}