{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{Box,Button,Typography,IconButton,DialogActions}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BannerEndUser=_ref=>{var _initialGuideData$Gui,_initialGuideData$Gui2,_initialGuideData$Gui3,_initialGuideData$Gui4,_initialGuideData$Gui5,_initialGuideData$Gui6,_initialGuideData$Gui7,_initialGuideData$Gui8,_initialGuideData$Gui9,_initialGuideData$Gui13,_initialGuideData$Gui14,_initialGuideData$Gui15,_initialGuideData$Gui16,_initialGuideData$Gui17,_initialGuideData$Gui18,_Modal$DismissOption,_initialGuideData$Gui19,_initialGuideData$Gui20,_initialGuideData$Gui21,_initialGuideData$Gui22,_initialGuideData$Gui23,_initialGuideData$Gui24,_initialGuideData$Gui25,_initialGuideData$Gui26,_initialGuideData$Gui27,_initialGuideData$Gui28,_initialGuideData$Gui29,_initialGuideData$Gui30;let{showBannerenduser,setShowBannerenduser,initialGuideData,backgroundC,currentStep}=_ref;const{btnBgColor,btnTextColor,btnBorderColor}=useDrawerStore(state=>state);const[showBanner,setShowBanner]=useState(true);const{setImageSrc,imageSrc,htmlContent,sectionColor}=useDrawerStore(state=>state);const Teext=initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui=initialGuideData.GuideStep)===null||_initialGuideData$Gui===void 0?void 0:(_initialGuideData$Gui2=_initialGuideData$Gui[0])===null||_initialGuideData$Gui2===void 0?void 0:_initialGuideData$Gui2.TextFieldProperties.Text;// const bannerSteps = initialGuideData.GuideStep.filter((step:any) => step.StepType === \"Banner\");\nconst renderHtmlSnippet=snippet=>{if(!snippet)return\"Sample Text...\";// Return an empty string if snippet is null or undefined.\nreturn snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;});};const image=imageSrc;const isBase64=url=>url.startsWith(\"data:image/\");const textField=(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui3=initialGuideData.GuideStep)===null||_initialGuideData$Gui3===void 0?void 0:(_initialGuideData$Gui4=_initialGuideData$Gui3[0])===null||_initialGuideData$Gui4===void 0?void 0:(_initialGuideData$Gui5=_initialGuideData$Gui4.TextFieldProperties)===null||_initialGuideData$Gui5===void 0?void 0:_initialGuideData$Gui5[0])||{};const{Text:textFieldText,Alignment,Hyperlink,Emoji,TextProperties}=textField;const{Bold,Italic,TextColor}=TextProperties||{};const customButton=(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui6=initialGuideData.GuideStep)===null||_initialGuideData$Gui6===void 0?void 0:(_initialGuideData$Gui7=_initialGuideData$Gui6[0])===null||_initialGuideData$Gui7===void 0?void 0:(_initialGuideData$Gui8=_initialGuideData$Gui7.ButtonSection)===null||_initialGuideData$Gui8===void 0?void 0:(_initialGuideData$Gui9=_initialGuideData$Gui8.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_initialGuideData$Gui9===void 0?void 0:_initialGuideData$Gui9.reduce((acc,curr)=>acc.concat(curr),[]))||[];const handleButtonClick=action=>{if(action.Action===\"open-url\"||action.Action===\"openurl\"||action.Action===\"open\"){if(action.ActionValue===\"same-tab\"){window.location.href=action.TargetUrl;}else{window.open(action.TargetUrl,\"_blank\",\"noopener noreferrer\");}//onContinue();\n}else if(action.Action===\"start-interaction\"){// onContinue();\n// setOverlayValue(false);\n}else if(action.Action===\"close\"){// onClose();\n// setOverlayValue(false);\n}else if(action.Action===\"Next\"||action.Action===\"next\"){var _initialGuideData$Gui10;// Handle next step navigation\nif(typeof currentStep==='number'&&(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui10=initialGuideData.GuideStep)===null||_initialGuideData$Gui10===void 0?void 0:_initialGuideData$Gui10.length)>currentStep){// Navigate to next step logic would go here\n}}else if(action.Action===\"Previous\"||action.Action===\"previous\"){// Handle previous step navigation\nif(typeof currentStep==='number'&&currentStep>1){// Navigate to previous step logic would go here\n}}else if(action.Action===\"Restart\"){var _initialGuideData$Gui11;// Reset to the first step\n// Navigate to first step logic would go here\nif((initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui11=initialGuideData.GuideStep)===null||_initialGuideData$Gui11===void 0?void 0:_initialGuideData$Gui11.length)>0){var _initialGuideData$Gui12;// Reset to step 1\n// If there's a specific URL for the first step, navigate to it\nif((_initialGuideData$Gui12=initialGuideData.GuideStep[0])!==null&&_initialGuideData$Gui12!==void 0&&_initialGuideData$Gui12.ElementPath){const firstStepElement=document.evaluate(initialGuideData.GuideStep[0].ElementPath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(firstStepElement){firstStepElement.scrollIntoView({behavior:'smooth'});}}}}else if(action===undefined||null){// onClose();\n// setOverlayValue(false);\n}else{// onClose();\n// setOverlayValue(false);\n}};const designProps=(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui13=initialGuideData.GuideStep)===null||_initialGuideData$Gui13===void 0?void 0:(_initialGuideData$Gui14=_initialGuideData$Gui13[0])===null||_initialGuideData$Gui14===void 0?void 0:_initialGuideData$Gui14.Design)||{};const IconColor=designProps.IconColor||\"#000\";const IconOpacity=designProps.QuietIcon?0.5:1.0;const canvas=(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui15=initialGuideData.GuideStep)===null||_initialGuideData$Gui15===void 0?void 0:(_initialGuideData$Gui16=_initialGuideData$Gui15[0])===null||_initialGuideData$Gui16===void 0?void 0:_initialGuideData$Gui16.Canvas)||{};const BackgroundColor=(canvas===null||canvas===void 0?void 0:canvas.BackgroundColor)||\"#f1f1f7\";const Width=canvas.Width||\"100%\";const Radius=canvas.Radius||\"0\";const Padding=canvas.Padding||\"10\";const BorderSize=canvas.BorderSize||\"2\";const BorderColor=canvas.BorderColor||\"#f1f1f7\";const Position=\"absolute\";const zindex=canvas.Zindex||\"999999\";const Modal=initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui17=initialGuideData.GuideStep)===null||_initialGuideData$Gui17===void 0?void 0:(_initialGuideData$Gui18=_initialGuideData$Gui17[0])===null||_initialGuideData$Gui18===void 0?void 0:_initialGuideData$Gui18.Modal;const isCloseDisabled=(_Modal$DismissOption=Modal===null||Modal===void 0?void 0:Modal.DismissOption)!==null&&_Modal$DismissOption!==void 0?_Modal$DismissOption:false;const Design=(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui19=initialGuideData.GuideStep)===null||_initialGuideData$Gui19===void 0?void 0:(_initialGuideData$Gui20=_initialGuideData$Gui19[0])===null||_initialGuideData$Gui20===void 0?void 0:_initialGuideData$Gui20.Design)||{};const htmlSnippet=(initialGuideData===null||initialGuideData===void 0?void 0:(_initialGuideData$Gui21=initialGuideData.GuideStep)===null||_initialGuideData$Gui21===void 0?void 0:(_initialGuideData$Gui22=_initialGuideData$Gui21[0])===null||_initialGuideData$Gui22===void 0?void 0:_initialGuideData$Gui22.HtmlSnippet)||\"\";// Apply overflow hidden to body when canvas position is \"Cover Top\"\nuseEffect(()=>{if((canvas===null||canvas===void 0?void 0:canvas.Position)===\"Cover Top\"){document.body.style.overflow=\"hidden\";}else{document.body.style.overflow=\"\";}// Cleanup function to restore overflow when component unmounts\nreturn()=>{document.body.style.overflow=\"\";};},[canvas===null||canvas===void 0?void 0:canvas.Position]);// Re-run when canvas position changes\nconst countLinesFromHtml=html=>{const paragraphCount=(html.match(/<p>/g)||[]).length;const brCount=(html.match(/<br\\s*\\/?>/g)||[]).length;return paragraphCount>0?paragraphCount-1:0;};// const renderHtmlSnippet = (snippet: string) => {\n// \treturn parse(snippet, {\n// \t\treplace: (domNode: any) => {\n// \t\t\tif (domNode.name === \"font\") {\n// \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\n// \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\n// \t\t\t\treturn (\n// \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\n// \t\t\t\t\t\t{domToReact(domNode.children)}\n// \t\t\t\t\t</span>\n// \t\t\t\t);\n// \t\t\t}\n// \t\t\treturn undefined;\n// \t\t},\n// \t});\n// };\nreturn/*#__PURE__*/_jsx(Box,{sx:{// position: \"relative\",\n// top: \"55px\"\n},className:\"qadpt-container\",children:showBanner&&/*#__PURE__*/_jsx(Box,{className:\"qadpt-boxpre\",id:\"guide-popup\",sx:{// position: \"relative\",\n// top: \"55px\",\n// ...BannerWrapper,\n//top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\nleft:\"50%\",height:\"auto\",marginTop:`${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(customButton&&customButton.length>0?4:0)+countLinesFromHtml(textFieldText)*10}px`,//\"29px\",\ntransform:\"translate(-50%, -50%)\",backgroundColor:sectionColor,//width: Width,\nmaxWidth:\"100%\",//borderRadius: Radius,\npadding:`${Padding}px`,//borderWidth: \"2px\",\n//border: `${BorderSize}px solid ${BorderColor}`,\nboxShadow:Object.keys(canvas).length==0?\"0px 1px 15px rgba(0, 0, 0, 0.7)\":(canvas===null||canvas===void 0?void 0:canvas.Position)==\"Cover Top\"?\"0px 1px 15px rgba(0, 0, 0, 0.7)\":\"none\",//(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\nposition:Position,zIndex:zindex,background:`${BackgroundColor?BackgroundColor:'#f1f1f7'} !important`,// border: `${BorderSize}px solid ${BorderColor}`,\nborderTop:`${BorderSize}px solid ${BorderColor} !important`,borderRight:`${BorderSize}px solid ${BorderColor} !important`,borderLeft:`${BorderSize}px solid ${BorderColor} !important`,borderBottom:`${BorderSize}px solid ${BorderColor} !important`},children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-row\",sx:{display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsxs(Box,{sx:{// margin: \"8px \",\n//bottom: \"45px\",\nposition:\"relative\",display:\"flex\",alignItems:\"center\",placeContent:\"center\",width:\"100%\",\"& .MuiTypography-root\":{width:\"100%\",margin:\"0\"}},children:[Hyperlink?/*#__PURE__*/_jsx(Typography,{component:\"a\",href:Hyperlink,target:\"_blank\"// Open link in a new tab\n,rel:\"noopener noreferrer\"// Security measure when using target=\"_blank\"\n,sx:{color:TextColor,padding:\"5px 2px\",textAlign:Alignment||\"center\",marginTop:1,textDecoration:\"underline\"},dangerouslySetInnerHTML:{__html:renderHtmlSnippet(htmlSnippet)}}):/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview qadpt-rte\",sx:{color:TextColor,textAlign:Alignment,marginTop:1,whiteSpace:\"pre-wrap\",padding:\"5px 2px\",wordBreak:\"break-word\",\"& p\":{margin:\"0\"}},dangerouslySetInnerHTML:{__html:renderHtmlSnippet(textFieldText)}}),Emoji&&/*#__PURE__*/_jsx(Typography,{component:\"span\",sx:{fontWeight:Bold?\"bold\":\"normal\",padding:\"5px 2px\",fontStyle:Italic?\"italic\":\"normal\",color:TextColor,textAlign:Alignment?Alignment:\"center\",mt:1},children:Emoji}),customButton&&customButton.some(button=>button.ButtonName&&button.ButtonName.trim()!==\"\")&&/*#__PURE__*/_jsx(DialogActions,{sx:{justifyContent:\"center\",padding:\"0 !important\",height:\"40px\"},children:customButton.map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonClick(button.ButtonAction),variant:\"contained\",style:{backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#5F9EA0\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#ffffff\",border:(_button$ButtonPropert3=button.ButtonProperties)!==null&&_button$ButtonPropert3!==void 0&&_button$ButtonPropert3.ButtonBorderColor?`2px solid ${button.ButtonProperties.ButtonBorderColor}`:\"none\",margin:\"0 5px\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||15,width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:\"6px 16px\",textTransform:\"none\",borderRadius:\"20px\",lineHeight:\"22px\"},sx:{\"&:hover\":{filter:\"brightness(1.2)\"}},children:button.ButtonName},button.Id||`button-${index}`);})}),Hyperlink&&/*#__PURE__*/_jsx(Typography,{component:\"a\",href:Hyperlink,target:\"_blank\",rel:\"noopener noreferrer\",sx:{color:TextColor,textAlign:Alignment||\"left\",mt:1,textDecoration:\"underline\"},children:renderHtmlSnippet(textFieldText)}),image&&/*#__PURE__*/_jsx(Box,{component:\"a\",href:((_initialGuideData$Gui23=initialGuideData.GuideStep[0])===null||_initialGuideData$Gui23===void 0?void 0:(_initialGuideData$Gui24=_initialGuideData$Gui23.ImageProperties)===null||_initialGuideData$Gui24===void 0?void 0:_initialGuideData$Gui24.Hyperlink)||\"#\",target:\"_blank\",rel:\"noopener noreferrer\",sx:{textAlign:Alignment||\"center\"},children:/*#__PURE__*/_jsx(Box,{component:\"img\",src:isBase64(image)?image:image,sx:{maxHeight:((_initialGuideData$Gui25=initialGuideData.GuideStep[0])===null||_initialGuideData$Gui25===void 0?void 0:(_initialGuideData$Gui26=_initialGuideData$Gui25.ImageProperties)===null||_initialGuideData$Gui26===void 0?void 0:_initialGuideData$Gui26.MaxImageHeight)||\"auto\",objectFit:((_initialGuideData$Gui27=initialGuideData.GuideStep[0])===null||_initialGuideData$Gui27===void 0?void 0:(_initialGuideData$Gui28=_initialGuideData$Gui27.ImageProperties)===null||_initialGuideData$Gui28===void 0?void 0:(_initialGuideData$Gui29=_initialGuideData$Gui28.UploadedImages)===null||_initialGuideData$Gui29===void 0?void 0:(_initialGuideData$Gui30=_initialGuideData$Gui29[0])===null||_initialGuideData$Gui30===void 0?void 0:_initialGuideData$Gui30.Fit)||\"contain\",display:\"block\",margin:\"0 auto\"}})})]}),\" \",isCloseDisabled&&/*#__PURE__*/_jsx(IconButton,{sx:{// position: \"fixed\",\nboxShadow:\"rgba(0, 0, 0, 0.15) 0px 4px 8px\",marginLeft:\"2px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"3px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:\"1\",color:\"#000\"}})})]})})});};export default BannerEndUser;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Typography", "IconButton", "DialogActions", "CloseIcon", "useDrawerStore", "jsx", "_jsx", "jsxs", "_jsxs", "BannerEndUser", "_ref", "_initialGuideData$Gui", "_initialGuideData$Gui2", "_initialGuideData$Gui3", "_initialGuideData$Gui4", "_initialGuideData$Gui5", "_initialGuideData$Gui6", "_initialGuideData$Gui7", "_initialGuideData$Gui8", "_initialGuideData$Gui9", "_initialGuideData$Gui13", "_initialGuideData$Gui14", "_initialGuideData$Gui15", "_initialGuideData$Gui16", "_initialGuideData$Gui17", "_initialGuideData$Gui18", "_Modal$DismissOption", "_initialGuideData$Gui19", "_initialGuideData$Gui20", "_initialGuideData$Gui21", "_initialGuideData$Gui22", "_initialGuideData$Gui23", "_initialGuideData$Gui24", "_initialGuideData$Gui25", "_initialGuideData$Gui26", "_initialGuideData$Gui27", "_initialGuideData$Gui28", "_initialGuideData$Gui29", "_initialGuideData$Gui30", "showBanneren<PERSON>er", "setShowBannerenduser", "initialGuideData", "backgroundC", "currentStep", "btnBgColor", "btnTextColor", "btnBorderColor", "state", "showBanner", "setShowBanner", "setImageSrc", "imageSrc", "htmlContent", "sectionColor", "Teext", "GuideStep", "TextFieldProperties", "Text", "renderHtmlSnippet", "snippet", "replace", "match", "p1", "p2", "p3", "image", "isBase64", "url", "startsWith", "textField", "textFieldText", "Alignment", "Hyperlink", "<PERSON><PERSON><PERSON>", "TextProperties", "Bold", "Italic", "TextColor", "customButton", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "handleButtonClick", "action", "Action", "ActionValue", "window", "location", "href", "TargetUrl", "open", "_initialGuideData$Gui10", "length", "_initialGuideData$Gui11", "_initialGuideData$Gui12", "<PERSON>ement<PERSON><PERSON>", "firstStepElement", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "singleNodeValue", "scrollIntoView", "behavior", "undefined", "designProps", "Design", "IconColor", "IconOpacity", "QuietIcon", "canvas", "<PERSON><PERSON>", "BackgroundColor", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Padding", "BorderSize", "BorderColor", "Position", "zindex", "Zindex", "Modal", "isCloseDisabled", "DismissOption", "htmlSnippet", "HtmlSnippet", "body", "style", "overflow", "countLinesFromHtml", "html", "paragraphCount", "brCount", "sx", "className", "children", "id", "left", "height", "marginTop", "parseInt", "transform", "backgroundColor", "max<PERSON><PERSON><PERSON>", "padding", "boxShadow", "Object", "keys", "position", "zIndex", "background", "borderTop", "borderRight", "borderLeft", "borderBottom", "display", "alignItems", "place<PERSON><PERSON>nt", "width", "margin", "component", "target", "rel", "color", "textAlign", "textDecoration", "dangerouslySetInnerHTML", "__html", "whiteSpace", "wordBreak", "fontWeight", "fontStyle", "mt", "some", "ButtonName", "trim", "justifyContent", "index", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "onClick", "ButtonAction", "variant", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "border", "ButtonBorderColor", "fontSize", "FontSize", "textTransform", "borderRadius", "lineHeight", "filter", "ImageProperties", "src", "maxHeight", "MaxImageHeight", "objectFit", "UploadedImages", "Fit", "marginLeft", "zoom"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Bannerspreview/Banner.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Button, Typography, IconButton, DialogActions } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { CustomIconButton } from \"./Button\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport parse, { domToReact } from \"html-react-parser\";\r\nimport { Element } from \"domhandler\";\r\nimport { IconButtonSX } from \"./Banner.style\";\r\n\r\nconst BannerEndUser = ({ showBannerenduser, setShowBannerenduser, initialGuideData, backgroundC,currentStep }: any) => {\r\n\tconst {\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [showBanner, setShowBanner] = useState(true);\r\n\tconst { setImageSrc, imageSrc, htmlContent, sectionColor } = useDrawerStore((state: any) => state);\r\n\tconst Teext = initialGuideData?.GuideStep?.[0]?.TextFieldProperties.Text;\r\n\t// const bannerSteps = initialGuideData.GuideStep.filter((step:any) => step.StepType === \"Banner\");\r\n\r\n\tconst renderHtmlSnippet = (snippet: string | undefined | null) => {\r\n\t\tif (!snippet) return \"Sample Text...\"; // Return an empty string if snippet is null or undefined.\r\n\t\treturn snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\r\n\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t});\r\n\t};\r\n\tconst image = imageSrc;\r\n\tconst isBase64 = (url: string) => url.startsWith(\"data:image/\");\r\n\tconst textField = initialGuideData?.GuideStep?.[0]?.TextFieldProperties?.[0] || {};\r\n\tconst { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;\r\n\tconst { Bold, Italic, TextColor } = TextProperties || {};\r\n\tconst customButton =\r\n    initialGuideData?.GuideStep?.[0]?.ButtonSection\r\n        ?.map((section: any) =>\r\n            section.CustomButtons.map((button: any) => ({\r\n                ...button,\r\n                ContainerId: section.Id, // Attach the container ID for grouping\r\n            }))\r\n        )\r\n        ?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [];\r\n\r\n\tconst handleButtonClick = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"openurl\" || action.Action === \"open\") {\r\n\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\twindow.location.href = action.TargetUrl;\r\n\t\t} else {\r\n\t\twindow.open(action.TargetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t}\r\n\t\t//onContinue();\r\n\t\t} else if (action.Action === \"start-interaction\") {\r\n\t\t\t// onContinue();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else if (action.Action === \"close\") {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else if (action.Action === \"Next\" || action.Action === \"next\") {\r\n\t\t\t// Handle next step navigation\r\n\t\t\tif (typeof currentStep === 'number' && initialGuideData?.GuideStep?.length > currentStep) {\r\n\t\t\t\t// Navigate to next step logic would go here\r\n\t\t\t}\r\n\t\t} else if (action.Action === \"Previous\" || action.Action === \"previous\") {\r\n\t\t\t// Handle previous step navigation\r\n\t\t\tif (typeof currentStep === 'number' && currentStep > 1) {\r\n\t\t\t\t// Navigate to previous step logic would go here\r\n\t\t\t}\r\n\t\t} else if (action.Action === \"Restart\") {\r\n\t\t\t// Reset to the first step\r\n\t\t\t// Navigate to first step logic would go here\r\n\t\t\tif (initialGuideData?.GuideStep?.length > 0) {\r\n\t\t\t\t// Reset to step 1\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (initialGuideData.GuideStep[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = document.evaluate(\r\n\t\t\t\t\t\tinitialGuideData.GuideStep[0].ElementPath,\r\n\t\t\t\t\t\tdocument,\r\n\t\t\t\t\t\tnull,\r\n\t\t\t\t\t\tXPathResult.FIRST_ORDERED_NODE_TYPE,\r\n\t\t\t\t\t\tnull\r\n\t\t\t\t\t).singleNodeValue;\r\n\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\t(firstStepElement as HTMLElement).scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else if (action === undefined || null) {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t}\r\n\t};\r\n\tconst designProps = initialGuideData?.GuideStep?.[0]?.Design || {};\r\n\tconst IconColor = designProps.IconColor || \"#000\";\r\n\tconst IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;\r\n\tconst canvas = initialGuideData?.GuideStep?.[0]?.Canvas || {};\r\n\tconst BackgroundColor = canvas?.BackgroundColor\t|| \"#f1f1f7\";\r\n\tconst Width = canvas.Width || \"100%\";\r\n\tconst Radius = canvas.Radius || \"0\";\r\n\tconst Padding = canvas.Padding || \"10\";\r\n\tconst BorderSize = canvas.BorderSize || \"2\";\r\n\tconst BorderColor = canvas.BorderColor || \"#f1f1f7\";\r\n\tconst Position =  \"absolute\";\r\n\tconst zindex = canvas.Zindex || \"999999\";\r\n\tconst Modal = initialGuideData?.GuideStep?.[0]?.Modal;\r\n\tconst isCloseDisabled = Modal?.DismissOption ?? false;\r\n\tconst Design = initialGuideData?.GuideStep?.[0]?.Design || {};\r\n\tconst htmlSnippet = initialGuideData?.GuideStep?.[0]?.HtmlSnippet || \"\";\r\n\r\n\t// Apply overflow hidden to body when canvas position is \"Cover Top\"\r\n\tuseEffect(() => {\r\n\t\tif (canvas?.Position === \"Cover Top\") {\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t} else {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t}\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t};\r\n\t}, [canvas?.Position]); // Re-run when canvas position changes\r\n\r\n\tconst countLinesFromHtml = (html: string) => {\r\n\t\tconst paragraphCount = (html.match(/<p>/g) || []).length;\r\n\r\n\t\tconst brCount = (html.match(/<br\\s*\\/?>/g) || []).length;\r\n\r\n\t\treturn paragraphCount>0 ? paragraphCount-1 : 0;\r\n\t};\r\n\r\n\t// const renderHtmlSnippet = (snippet: string) => {\r\n\t// \treturn parse(snippet, {\r\n\t// \t\treplace: (domNode: any) => {\r\n\t// \t\t\tif (domNode.name === \"font\") {\r\n\t// \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\r\n\t// \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\r\n\t// \t\t\t\treturn (\r\n\t// \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\r\n\t// \t\t\t\t\t\t{domToReact(domNode.children)}\r\n\t// \t\t\t\t\t</span>\r\n\t// \t\t\t\t);\r\n\t// \t\t\t}\r\n\t// \t\t\treturn undefined;\r\n\t// \t\t},\r\n\t// \t});\r\n\t// };\r\n\r\n\treturn (\r\n\t\t<Box sx={{\r\n\t\t\t// position: \"relative\",\r\n\t\t\t// top: \"55px\"\r\n\t\t}} className=\"qadpt-container\">\r\n\t\t\t{showBanner && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-boxpre\"\r\n\t\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t// position: \"relative\",\r\n\t\t\t\t\t\t// top: \"55px\",\r\n\t\t\t\t\t\t// ...BannerWrapper,\r\n\t\t\t\t\t\t//top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\r\n\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmarginTop: `${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(customButton && customButton.length > 0 ? 4 : 0)+(countLinesFromHtml(textFieldText)*10)}px`,//\"29px\",\r\n\t\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor,\r\n\t\t\t\t\t\t//width: Width,\r\n\t\t\t\t\t\tmaxWidth:\"100%\",\r\n\t\t\t\t\t\t//borderRadius: Radius,\r\n\t\t\t\t\t\tpadding: `${Padding}px`,\r\n\t\t\t\t\t\t//borderWidth: \"2px\",\r\n\t\t\t\t\t\t//border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tboxShadow: Object.keys(canvas).length == 0? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : canvas?.Position == \"Cover Top\" ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",//(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\r\n\t\t\t\t\t\tposition: Position,\r\n\t\t\t\t\t\tzIndex: zindex,\r\n\r\n\t\t\t\t\t\tbackground:  `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important` ,\r\n\t\t\t\t\t\t// border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t  `${BorderSize}px solid ${BorderColor} !important`,\r\n\r\n\t\t\t\t\tborderBottom: `${BorderSize}px solid ${BorderColor} !important`,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box className=\"qadpt-row\" sx={{  display: \"flex\", alignItems: \"center\"}}>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// margin: \"8px \",\r\n\t\t\t\t\t\t\t//bottom: \"45px\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\r\n\r\n\t\t\t\t\t\t\t\"& .MuiTypography-root\": {\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\tmargin: \"0\",\r\n\t\t\t\t\t\t\t},\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Display hyperlink  */}\r\n\t\t\t\t\t\t{Hyperlink ? (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\" // Open link in a new tab\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\" // Security measure when using target=\"_blank\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: renderHtmlSnippet(htmlSnippet) }}\r\n\t\t\t\t\t\t\t></Typography>\r\n\t\t\t\t\t\t\t) : (\r\n\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t  className=\"qadpt-preview qadpt-rte\"\r\n\t\t\t\t\t\t\t\t  sx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment,\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\"& p\": {\r\n\t\t\t\t\t\t\t\t\t  margin: \"0\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t  dangerouslySetInnerHTML={{ __html: renderHtmlSnippet(textFieldText) }}\r\n\t\t\t\t\t\t\t\t/>\r\n\r\n\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{Emoji && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"span\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontWeight: Bold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\tfontStyle: Italic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment ? Alignment : \"center\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{Emoji}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{customButton &&\r\n\t\t\t\t\t\t\tcustomButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== \"\") && (\r\n\t\t\t\t\t\t\t\t<DialogActions\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{customButton.map((button: any, index: any) => (\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tkey={button.Id || `button-${index}`}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${button.ButtonProperties.ButtonBorderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || 15,\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"22px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfilter: \"brightness(1.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</DialogActions>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{/* {CustomButton && CustomButton.ButtonName && (\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontSize: CustomButton.ButtonProperties?.FontSize || 14,\r\n\t\t\t\t\t\t\t\t\twidth: CustomButton.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\tpadding: CustomButton.ButtonProperties?.Padding || \"10px\",\r\n\t\t\t\t\t\t\t\t\tcolor: CustomButton.ButtonProperties?.ButtonTextColor,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: CustomButton.ButtonProperties?.ButtonBackgroundColor,\r\n\t\t\t\t\t\t\t\t\t//textAlign: CustomButton.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t//display: \"block\",\r\n\t\t\t\t\t\t\t\t\tmargin: \"8px\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"0px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\tmx: CustomButton.Alignment === \"center\" ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={handleButtonClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{CustomButton.ButtonName}\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t)} */}\r\n\r\n\t\t\t\t\t\t{Hyperlink && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"left\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{renderHtmlSnippet(textFieldText)}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{image && (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={initialGuideData.GuideStep[0]?.ImageProperties?.Hyperlink || \"#\"}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{ textAlign: Alignment || \"center\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc={isBase64(image) ? image : image}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tmaxHeight: initialGuideData.GuideStep[0]?.ImageProperties?.MaxImageHeight || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\tobjectFit: initialGuideData.GuideStep[0]?.ImageProperties?.UploadedImages?.[0]?.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 auto\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>{\" \"}\r\n\t\t\t\t\t{isCloseDisabled && (\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n\t\t\t\t\tmarginLeft: \"2px\",\r\n\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\tzIndex:\"999999\",\r\n\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\tpadding:\"3px !important\"\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\"}}   />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</Box>\r\n\t);\r\n};\r\n\r\nexport default BannerEndUser;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,UAAU,CAAEC,aAAa,KAAQ,eAAe,CAClF,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAEjD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMrD,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAAiG,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,oBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IAAhG,CAAEC,iBAAiB,CAAEC,oBAAoB,CAAEC,gBAAgB,CAAEC,WAAW,CAACC,WAAiB,CAAC,CAAAjC,IAAA,CACjH,KAAM,CACLkC,UAAU,CACVC,YAAY,CACZC,cACD,CAAC,CAAG1C,cAAc,CAAE2C,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAAEsD,WAAW,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAGjD,cAAc,CAAE2C,KAAU,EAAKA,KAAK,CAAC,CAClG,KAAM,CAAAO,KAAK,CAAGb,gBAAgB,SAAhBA,gBAAgB,kBAAA9B,qBAAA,CAAhB8B,gBAAgB,CAAEc,SAAS,UAAA5C,qBAAA,kBAAAC,sBAAA,CAA3BD,qBAAA,CAA8B,CAAC,CAAC,UAAAC,sBAAA,iBAAhCA,sBAAA,CAAkC4C,mBAAmB,CAACC,IAAI,CACxE;AAEA,KAAM,CAAAC,iBAAiB,CAAIC,OAAkC,EAAK,CACjE,GAAI,CAACA,OAAO,CAAE,MAAO,gBAAgB,CAAE;AACvC,MAAO,CAAAA,OAAO,CAACC,OAAO,CAAC,qCAAqC,CAAE,CAACC,KAAK,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACpF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CAAC,CACH,CAAC,CACD,KAAM,CAAAC,KAAK,CAAGd,QAAQ,CACtB,KAAM,CAAAe,QAAQ,CAAIC,GAAW,EAAKA,GAAG,CAACC,UAAU,CAAC,aAAa,CAAC,CAC/D,KAAM,CAAAC,SAAS,CAAG,CAAA5B,gBAAgB,SAAhBA,gBAAgB,kBAAA5B,sBAAA,CAAhB4B,gBAAgB,CAAEc,SAAS,UAAA1C,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA8B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAAhCD,sBAAA,CAAkC0C,mBAAmB,UAAAzC,sBAAA,iBAArDA,sBAAA,CAAwD,CAAC,CAAC,GAAI,CAAC,CAAC,CAClF,KAAM,CAAE0C,IAAI,CAAEa,aAAa,CAAEC,SAAS,CAAEC,SAAS,CAAEC,KAAK,CAAEC,cAAe,CAAC,CAAGL,SAAS,CACtF,KAAM,CAAEM,IAAI,CAAEC,MAAM,CAAEC,SAAU,CAAC,CAAGH,cAAc,EAAI,CAAC,CAAC,CACxD,KAAM,CAAAI,YAAY,CACf,CAAArC,gBAAgB,SAAhBA,gBAAgB,kBAAAzB,sBAAA,CAAhByB,gBAAgB,CAAEc,SAAS,UAAAvC,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA8B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAAhCD,sBAAA,CAAkC8D,aAAa,UAAA7D,sBAAA,kBAAAC,sBAAA,CAA/CD,sBAAA,CACM8D,GAAG,CAAEC,OAAY,EACfA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CACxC,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC7B,CAAC,CAAC,CACN,CAAC,UAAAlE,sBAAA,iBANLA,sBAAA,CAOMmE,MAAM,CAAC,CAACC,GAAU,CAAEC,IAAW,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EAAE,CAExE,KAAM,CAAAE,iBAAiB,CAAIC,MAAW,EAAK,CAC1C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,CAAE,CAC7F,GAAID,MAAM,CAACE,WAAW,GAAK,UAAU,CAAE,CACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAGL,MAAM,CAACM,SAAS,CACvC,CAAC,IAAM,CACPH,MAAM,CAACI,IAAI,CAACP,MAAM,CAACM,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CAC9D,CACA;AACA,CAAC,IAAM,IAAIN,MAAM,CAACC,MAAM,GAAK,mBAAmB,CAAE,CACjD;AACA;AAAA,CACA,IAAM,IAAID,MAAM,CAACC,MAAM,GAAK,OAAO,CAAE,CACrC;AACA;AAAA,CACA,IAAM,IAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,CAAE,KAAAO,uBAAA,CAChE;AACA,GAAI,MAAO,CAAAxD,WAAW,GAAK,QAAQ,EAAI,CAAAF,gBAAgB,SAAhBA,gBAAgB,kBAAA0D,uBAAA,CAAhB1D,gBAAgB,CAAEc,SAAS,UAAA4C,uBAAA,iBAA3BA,uBAAA,CAA6BC,MAAM,EAAGzD,WAAW,CAAE,CACzF;AAAA,CAEF,CAAC,IAAM,IAAIgD,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,UAAU,CAAE,CACxE;AACA,GAAI,MAAO,CAAAjD,WAAW,GAAK,QAAQ,EAAIA,WAAW,CAAG,CAAC,CAAE,CACvD;AAAA,CAEF,CAAC,IAAM,IAAIgD,MAAM,CAACC,MAAM,GAAK,SAAS,CAAE,KAAAS,uBAAA,CACvC;AACA;AACA,GAAI,CAAA5D,gBAAgB,SAAhBA,gBAAgB,kBAAA4D,uBAAA,CAAhB5D,gBAAgB,CAAEc,SAAS,UAAA8C,uBAAA,iBAA3BA,uBAAA,CAA6BD,MAAM,EAAG,CAAC,CAAE,KAAAE,uBAAA,CAC5C;AACA;AACA,IAAAA,uBAAA,CAAI7D,gBAAgB,CAACc,SAAS,CAAC,CAAC,CAAC,UAAA+C,uBAAA,WAA7BA,uBAAA,CAA+BC,WAAW,CAAE,CAC/C,KAAM,CAAAC,gBAAgB,CAAGC,QAAQ,CAACC,QAAQ,CACzCjE,gBAAgB,CAACc,SAAS,CAAC,CAAC,CAAC,CAACgD,WAAW,CACzCE,QAAQ,CACR,IAAI,CACJE,WAAW,CAACC,uBAAuB,CACnC,IACD,CAAC,CAACC,eAAe,CAEjB,GAAIL,gBAAgB,CAAE,CACpBA,gBAAgB,CAAiBM,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACzE,CACD,CACD,CACD,CAAC,IAAM,IAAIpB,MAAM,GAAKqB,SAAS,EAAI,IAAI,CAAE,CACxC;AACA;AAAA,CACA,IAAM,CACN;AACA;AAAA,CAEF,CAAC,CACD,KAAM,CAAAC,WAAW,CAAG,CAAAxE,gBAAgB,SAAhBA,gBAAgB,kBAAArB,uBAAA,CAAhBqB,gBAAgB,CAAEc,SAAS,UAAAnC,uBAAA,kBAAAC,uBAAA,CAA3BD,uBAAA,CAA8B,CAAC,CAAC,UAAAC,uBAAA,iBAAhCA,uBAAA,CAAkC6F,MAAM,GAAI,CAAC,CAAC,CAClE,KAAM,CAAAC,SAAS,CAAGF,WAAW,CAACE,SAAS,EAAI,MAAM,CACjD,KAAM,CAAAC,WAAW,CAAGH,WAAW,CAACI,SAAS,CAAG,GAAG,CAAG,GAAG,CACrD,KAAM,CAAAC,MAAM,CAAG,CAAA7E,gBAAgB,SAAhBA,gBAAgB,kBAAAnB,uBAAA,CAAhBmB,gBAAgB,CAAEc,SAAS,UAAAjC,uBAAA,kBAAAC,uBAAA,CAA3BD,uBAAA,CAA8B,CAAC,CAAC,UAAAC,uBAAA,iBAAhCA,uBAAA,CAAkCgG,MAAM,GAAI,CAAC,CAAC,CAC7D,KAAM,CAAAC,eAAe,CAAG,CAAAF,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEE,eAAe,GAAI,SAAS,CAC5D,KAAM,CAAAC,KAAK,CAAGH,MAAM,CAACG,KAAK,EAAI,MAAM,CACpC,KAAM,CAAAC,MAAM,CAAGJ,MAAM,CAACI,MAAM,EAAI,GAAG,CACnC,KAAM,CAAAC,OAAO,CAAGL,MAAM,CAACK,OAAO,EAAI,IAAI,CACtC,KAAM,CAAAC,UAAU,CAAGN,MAAM,CAACM,UAAU,EAAI,GAAG,CAC3C,KAAM,CAAAC,WAAW,CAAGP,MAAM,CAACO,WAAW,EAAI,SAAS,CACnD,KAAM,CAAAC,QAAQ,CAAI,UAAU,CAC5B,KAAM,CAAAC,MAAM,CAAGT,MAAM,CAACU,MAAM,EAAI,QAAQ,CACxC,KAAM,CAAAC,KAAK,CAAGxF,gBAAgB,SAAhBA,gBAAgB,kBAAAjB,uBAAA,CAAhBiB,gBAAgB,CAAEc,SAAS,UAAA/B,uBAAA,kBAAAC,uBAAA,CAA3BD,uBAAA,CAA8B,CAAC,CAAC,UAAAC,uBAAA,iBAAhCA,uBAAA,CAAkCwG,KAAK,CACrD,KAAM,CAAAC,eAAe,EAAAxG,oBAAA,CAAGuG,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEE,aAAa,UAAAzG,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACrD,KAAM,CAAAwF,MAAM,CAAG,CAAAzE,gBAAgB,SAAhBA,gBAAgB,kBAAAd,uBAAA,CAAhBc,gBAAgB,CAAEc,SAAS,UAAA5B,uBAAA,kBAAAC,uBAAA,CAA3BD,uBAAA,CAA8B,CAAC,CAAC,UAAAC,uBAAA,iBAAhCA,uBAAA,CAAkCsF,MAAM,GAAI,CAAC,CAAC,CAC7D,KAAM,CAAAkB,WAAW,CAAG,CAAA3F,gBAAgB,SAAhBA,gBAAgB,kBAAAZ,uBAAA,CAAhBY,gBAAgB,CAAEc,SAAS,UAAA1B,uBAAA,kBAAAC,uBAAA,CAA3BD,uBAAA,CAA8B,CAAC,CAAC,UAAAC,uBAAA,iBAAhCA,uBAAA,CAAkCuG,WAAW,GAAI,EAAE,CAEvE;AACAxI,SAAS,CAAC,IAAM,CACf,GAAI,CAAAyH,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEQ,QAAQ,IAAK,WAAW,CAAE,CACrCrB,QAAQ,CAAC6B,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACxC,CAAC,IAAM,CACN/B,QAAQ,CAAC6B,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,EAAE,CAClC,CAEA;AACA,MAAO,IAAM,CACZ/B,QAAQ,CAAC6B,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,EAAE,CAClC,CAAC,CACF,CAAC,CAAE,CAAClB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEQ,QAAQ,CAAC,CAAC,CAAE;AAExB,KAAM,CAAAW,kBAAkB,CAAIC,IAAY,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAG,CAACD,IAAI,CAAC7E,KAAK,CAAC,MAAM,CAAC,EAAI,EAAE,EAAEuC,MAAM,CAExD,KAAM,CAAAwC,OAAO,CAAG,CAACF,IAAI,CAAC7E,KAAK,CAAC,aAAa,CAAC,EAAI,EAAE,EAAEuC,MAAM,CAExD,MAAO,CAAAuC,cAAc,CAAC,CAAC,CAAGA,cAAc,CAAC,CAAC,CAAG,CAAC,CAC/C,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,mBACCrI,IAAA,CAACR,GAAG,EAAC+I,EAAE,CAAE,CACR;AACA;AAAA,CACC,CAACC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC5B/F,UAAU,eACV1C,IAAA,CAACR,GAAG,EACHgJ,SAAS,CAAC,cAAc,CACxBE,EAAE,CAAC,aAAa,CAChBH,EAAE,CAAE,CACH;AACA;AACA;AACA;AACAI,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,GAAG,EAAE,CAACC,QAAQ,CAACzB,OAAO,EAAE,CAAC,CAAC,CAACyB,QAAQ,CAACxB,UAAU,EAAE,CAAC,CAAC,EAAE9C,YAAY,EAAIA,YAAY,CAACsB,MAAM,CAAG,CAAC,CAAG,CAAC,CAAG,CAAC,CAAC,CAAEqC,kBAAkB,CAACnE,aAAa,CAAC,CAAC,EAAG,IAAI,CAAC;AAC3J+E,SAAS,CAAE,uBAAuB,CAClCC,eAAe,CAAEjG,YAAY,CAC7B;AACAkG,QAAQ,CAAC,MAAM,CACf;AACAC,OAAO,CAAE,GAAG7B,OAAO,IAAI,CACvB;AACA;AACA8B,SAAS,CAAEC,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAAClB,MAAM,EAAI,CAAC,CAAE,iCAAiC,CAAG,CAAAkB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEQ,QAAQ,GAAI,WAAW,CAAG,iCAAiC,CAAG,MAAM,CAAC;AAC7J8B,QAAQ,CAAE9B,QAAQ,CAClB+B,MAAM,CAAE9B,MAAM,CAEd+B,UAAU,CAAG,GAAGtC,eAAe,CAAGA,eAAe,CAAG,SAAS,aAAa,CAC1E;AACAuC,SAAS,CACP,GAAGnC,UAAU,YAAYC,WAAW,aAAa,CAEjDmC,WAAW,CACV,GAAGpC,UAAU,YAAYC,WAAW,aAAa,CAElDoC,UAAU,CACV,GAAGrC,UAAU,YAAYC,WAAW,aAAa,CAEpDqC,YAAY,CAAE,GAAGtC,UAAU,YAAYC,WAAW,aAClD,CAAE,CAAAkB,QAAA,cAEFvI,KAAA,CAACV,GAAG,EAACgJ,SAAS,CAAC,WAAW,CAACD,EAAE,CAAE,CAAGsB,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE,CAAArB,QAAA,eACzEvI,KAAA,CAACV,GAAG,EACH+I,EAAE,CAAE,CACH;AACA;AACAe,QAAQ,CAAE,UAAU,CACpBO,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,QAAQ,CACrBC,KAAK,CAAE,MAAM,CAGd,uBAAuB,CAAE,CACxBA,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,GACT,CAED,CAAE,CAAAxB,QAAA,EAGDvE,SAAS,cACTlE,IAAA,CAACN,UAAU,EACVwK,SAAS,CAAC,GAAG,CACbxE,IAAI,CAAExB,SAAU,CAChBiG,MAAM,CAAC,QAAS;AAAA,CAChBC,GAAG,CAAC,qBAAsB;AAAA,CAC1B7B,EAAE,CAAE,CACH8B,KAAK,CAAE9F,SAAS,CAChB2E,OAAO,CAAE,SAAS,CAClBoB,SAAS,CAAErG,SAAS,EAAI,QAAQ,CAChC4E,SAAS,CAAE,CAAC,CACZ0B,cAAc,CAAE,WACjB,CAAE,CACFC,uBAAuB,CAAE,CAAEC,MAAM,CAAErH,iBAAiB,CAAC0E,WAAW,CAAE,CAAE,CACxD,CAAC,cAGb9H,IAAA,CAACN,UAAU,EACT8I,SAAS,CAAC,yBAAyB,CACnCD,EAAE,CAAE,CACL8B,KAAK,CAAE9F,SAAS,CAChB+F,SAAS,CAAErG,SAAS,CACpB4E,SAAS,CAAE,CAAC,CACZ6B,UAAU,CAAE,UAAU,CACtBxB,OAAO,CAAE,SAAS,CAClByB,SAAS,CAAE,YAAY,CACvB,KAAK,CAAE,CACLV,MAAM,CAAE,GACV,CACC,CAAE,CACFO,uBAAuB,CAAE,CAAEC,MAAM,CAAErH,iBAAiB,CAACY,aAAa,CAAE,CAAE,CACvE,CAGF,CACAG,KAAK,eACLnE,IAAA,CAACN,UAAU,EACVwK,SAAS,CAAC,MAAM,CAChB3B,EAAE,CAAE,CACHqC,UAAU,CAAEvG,IAAI,CAAG,MAAM,CAAG,QAAQ,CACpC6E,OAAO,CAAE,SAAS,CAClB2B,SAAS,CAAEvG,MAAM,CAAG,QAAQ,CAAG,QAAQ,CACvC+F,KAAK,CAAE9F,SAAS,CAChB+F,SAAS,CAAErG,SAAS,CAAGA,SAAS,CAAG,QAAQ,CAC3C6G,EAAE,CAAE,CACL,CAAE,CAAArC,QAAA,CAEDtE,KAAK,CACK,CACZ,CACAK,YAAY,EACZA,YAAY,CAACuG,IAAI,CAAElG,MAAW,EAAKA,MAAM,CAACmG,UAAU,EAAInG,MAAM,CAACmG,UAAU,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,eACvFjL,IAAA,CAACJ,aAAa,EACb2I,EAAE,CAAE,CACJ2C,cAAc,CAAE,QAAQ,CACvBhC,OAAO,CAAE,cAAc,CACxBN,MAAM,CAAE,MACR,CAAE,CAAAH,QAAA,CAEDjE,YAAY,CAACE,GAAG,CAAC,CAACG,MAAW,CAAEsG,KAAU,QAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBACzCxL,IAAA,CAACP,MAAM,EAENgM,OAAO,CAAEA,CAAA,GAAMrG,iBAAiB,CAACP,MAAM,CAAC6G,YAAY,CAAE,CACtDC,OAAO,CAAC,WAAW,CACnB1D,KAAK,CAAE,CACNe,eAAe,CAAE,EAAAoC,qBAAA,CAAAvG,MAAM,CAAC+G,gBAAgB,UAAAR,qBAAA,iBAAvBA,qBAAA,CAAyBS,qBAAqB,GAAI,SAAS,CAC5ExB,KAAK,CAAE,EAAAgB,sBAAA,CAAAxG,MAAM,CAAC+G,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBS,eAAe,GAAI,SAAS,CAC5DC,MAAM,CAAE,CAAAT,sBAAA,CAAAzG,MAAM,CAAC+G,gBAAgB,UAAAN,sBAAA,WAAvBA,sBAAA,CAAyBU,iBAAiB,CAC/C,aAAanH,MAAM,CAAC+G,gBAAgB,CAACI,iBAAiB,EAAE,CACxD,MAAM,CACT/B,MAAM,CAAE,OAAO,CACfgC,QAAQ,CAAE,EAAAV,sBAAA,CAAA1G,MAAM,CAAC+G,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyBW,QAAQ,GAAI,EAAE,CACjDlC,KAAK,CAAE,EAAAwB,sBAAA,CAAA3G,MAAM,CAAC+G,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyBrE,KAAK,GAAI,MAAM,CAC/C+B,OAAO,CAAE,UAAU,CACnBiD,aAAa,CAAE,MAAM,CACrBC,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAE,MACb,CAAE,CACF9D,EAAE,CAAE,CACH,SAAS,CAAE,CACV+D,MAAM,CAAE,iBACT,CACD,CAAE,CAAA7D,QAAA,CAED5D,MAAM,CAACmG,UAAU,EAvBbnG,MAAM,CAACE,EAAE,EAAI,UAAUoG,KAAK,EAwB1B,CAAC,EACT,CAAC,CACY,CACf,CAwBDjH,SAAS,eACTlE,IAAA,CAACN,UAAU,EACVwK,SAAS,CAAC,GAAG,CACbxE,IAAI,CAAExB,SAAU,CAChBiG,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB7B,EAAE,CAAE,CACH8B,KAAK,CAAE9F,SAAS,CAChB+F,SAAS,CAAErG,SAAS,EAAI,MAAM,CAC9B6G,EAAE,CAAE,CAAC,CACLP,cAAc,CAAE,WACjB,CAAE,CAAA9B,QAAA,CAEDrF,iBAAiB,CAACY,aAAa,CAAC,CACtB,CACZ,CAEAL,KAAK,eACL3D,IAAA,CAACR,GAAG,EACH0K,SAAS,CAAC,GAAG,CACbxE,IAAI,CAAE,EAAAjE,uBAAA,CAAAU,gBAAgB,CAACc,SAAS,CAAC,CAAC,CAAC,UAAAxB,uBAAA,kBAAAC,uBAAA,CAA7BD,uBAAA,CAA+B8K,eAAe,UAAA7K,uBAAA,iBAA9CA,uBAAA,CAAgDwC,SAAS,GAAI,GAAI,CACvEiG,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB7B,EAAE,CAAE,CAAE+B,SAAS,CAAErG,SAAS,EAAI,QAAS,CAAE,CAAAwE,QAAA,cAEzCzI,IAAA,CAACR,GAAG,EACH0K,SAAS,CAAC,KAAK,CACfsC,GAAG,CAAE5I,QAAQ,CAACD,KAAK,CAAC,CAAGA,KAAK,CAAGA,KAAM,CACrC4E,EAAE,CAAE,CACHkE,SAAS,CAAE,EAAA9K,uBAAA,CAAAQ,gBAAgB,CAACc,SAAS,CAAC,CAAC,CAAC,UAAAtB,uBAAA,kBAAAC,uBAAA,CAA7BD,uBAAA,CAA+B4K,eAAe,UAAA3K,uBAAA,iBAA9CA,uBAAA,CAAgD8K,cAAc,GAAI,MAAM,CACnFC,SAAS,CAAE,EAAA9K,uBAAA,CAAAM,gBAAgB,CAACc,SAAS,CAAC,CAAC,CAAC,UAAApB,uBAAA,kBAAAC,uBAAA,CAA7BD,uBAAA,CAA+B0K,eAAe,UAAAzK,uBAAA,kBAAAC,uBAAA,CAA9CD,uBAAA,CAAgD8K,cAAc,UAAA7K,uBAAA,kBAAAC,uBAAA,CAA9DD,uBAAA,CAAiE,CAAC,CAAC,UAAAC,uBAAA,iBAAnEA,uBAAA,CAAqE6K,GAAG,GAAI,SAAS,CAChGhD,OAAO,CAAE,OAAO,CAChBI,MAAM,CAAE,QACT,CAAE,CACF,CAAC,CACE,CACL,EACG,CAAC,CAAC,GAAG,CACTrC,eAAe,eACf5H,IAAA,CAACL,UAAU,EACX4I,EAAE,CAAE,CACH;AACFY,SAAS,CAAE,iCAAiC,CAC5C2D,UAAU,CAAE,KAAK,CACjBtD,UAAU,CAAE,iBAAiB,CAC7BuC,MAAM,CAAE,gBAAgB,CACxBxC,MAAM,CAAC,QAAQ,CACd6C,YAAY,CAAE,MAAM,CACrBlD,OAAO,CAAC,gBAEP,CAAE,CAAAT,QAAA,cAEDzI,IAAA,CAACH,SAAS,EAAE0I,EAAE,CAAE,CAACwE,IAAI,CAAC,GAAG,CAAC1C,KAAK,CAAC,MAAM,CAAE,CAAI,CAAC,CAClC,CACX,EACG,CAAC,CACF,CACL,CACG,CAAC,CAER,CAAC,CAED,cAAe,CAAAlK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}