.highlight-border {
  outline: 2px solid red;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
  z-index: 9998; /* Below hotspot and popup */
  pointer-events: auto; /* Prevent interaction with the backdrop */
}

.highlighted-hotspot {
  position: absolute;
  z-index: 9999; /* Above the backdrop */
}

.popup {
  position: absolute;
  z-index: 10000; /* Above both the backdrop and hotspot */
}

/* Element Selection Mode Styles */
.quickadopt-element-selection-mode {
  /* Additional styles for when in element selection mode */
}

.quickadopt-element-selection-mode * {
  /* Ensure all elements show the custom cursor when in selection mode */
  cursor: inherit !important;
}

/* Pulse animation for the custom cursor overlay */
@keyframes quickadopt-cursor-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.quickadopt-overlay {
  animation: quickadopt-cursor-pulse 2s infinite;
}
