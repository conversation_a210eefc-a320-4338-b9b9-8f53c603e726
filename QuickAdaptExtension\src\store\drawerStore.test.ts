import { create } from 'zustand';
import { DrawerState } from './drawerStore';

// Mock crypto.randomUUID for testing
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'test-uuid-' + Math.random().toString(36).substr(2, 9)
  }
});

// Create a test store instance
const createTestStore = () => {
  return create<DrawerState>((set, get) => ({
    // Mock minimal state needed for testing
    selectedTemplate: "Tour",
    selectedTemplateTour: "Banner",
    currentStep: 1,
    buttonsContainer: [],
    savedGuideData: {
      GuideStep: [
        {
          StepType: "Banner",
          ButtonSection: [
            {
              Id: "test-section-id",
              BackgroundColor: "transparent",
              CustomButtons: [
                {
                  ButtonId: "test-button-id",
                  ButtonName: "Test Button",
                  Alignment: "center",
                  ButtonStyle: "primary",
                  ButtonProperties: {
                    ButtonBackgroundColor: "#5F9EA0",
                    ButtonBorderColor: "#70afaf",
                    ButtonTextColor: "#ffffff"
                  },
                  ButtonAction: {
                    Action: "close",
                    TargetUrl: "",
                    ActionValue: "same-tab"
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    isReturningFromPreview: false,
    createWithAI: false,

    // Mock functions needed for testing
    syncBannerButtonDataFromSavedGuide: () => {
      set((state) => {
        const currentStepIndex = state.currentStep - 1;
        
        // Get button data from savedGuideData for the current step
        const currentStep = state.savedGuideData?.GuideStep?.[currentStepIndex];
        if (!currentStep || !currentStep.ButtonSection || currentStep.ButtonSection.length === 0) {
          // Clear buttonsContainer if no button data exists
          state.buttonsContainer = [];
          console.log("No banner button data found in savedGuideData, clearing buttonsContainer");
          return;
        }

        // Convert ButtonSection data to buttonsContainer format
        state.buttonsContainer = currentStep.ButtonSection.map((buttonSection: any) => ({
          id: buttonSection.Id || crypto.randomUUID(),
          buttons: (buttonSection.CustomButtons || []).map((button: any) => ({
            id: button.ButtonId || button.Id || crypto.randomUUID(),
            name: button.ButtonName || "Button",
            position: button.Alignment || "center",
            type: button.ButtonStyle || "primary",
            isEditing: false,
            index: 0,
            style: {
              backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#5F9EA0",
              borderColor: button.ButtonProperties?.ButtonBorderColor || "#5F9EA0",
              color: button.ButtonProperties?.ButtonTextColor || "#ffffff",
            },
            actions: {
              value: button.ButtonAction?.Action || "close",
              targetURL: button.ButtonAction?.TargetUrl || "",
              tab: button.ButtonAction?.ActionValue || "same-tab",
              interaction: null,
            },
            survey: null,
          })),
          style: {
            backgroundColor: buttonSection.BackgroundColor || "transparent",
          },
          type: "button",
        }));

        console.log("Synced banner button data from savedGuideData to buttonsContainer", {
          stepIndex: currentStepIndex,
          buttonSections: currentStep.ButtonSection.length,
          buttonContainers: state.buttonsContainer.length,
          totalButtons: state.buttonsContainer.reduce((total: number, container: any) => total + container.buttons.length, 0)
        });
      });
    },

    ensureBannerButtonContainer: () => {
      set((state) => {
        // Only for banner steps in tours
        if (state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Banner") {
          // If buttonsContainer is empty, initialize it with a default container
          if (state.buttonsContainer.length === 0) {
            state.buttonsContainer = [
              {
                id: crypto.randomUUID(),
                buttons: [
                  {
                    id: crypto.randomUUID(),
                    name: "Got It",
                    position: "center",
                    type: "primary",
                    isEditing: false,
                    index: 0,
                    style: {
                      backgroundColor: "#5F9EA0",
                      borderColor: "#70afaf",
                      color: "#ffffff",
                    },
                    actions: {
                      value: "close",
                      targetURL: "",
                      tab: "same-tab",
                      interaction: null,
                    },
                    survey: null,
                  },
                ],
                style: {
                  backgroundColor: "transparent",
                },
                type: "button",
              },
            ];

            console.log("Initialized banner button container for step", state.currentStep);
          }
        }
      });
    },

    // Add other required properties with default values
    setCurrentStep: () => {},
    setSelectedTemplate: () => {},
    setSelectedTemplateTour: () => {},
    setIsReturningFromPreview: () => {},
    setCreateWithAI: () => {},
  }));
};

describe('Banner Button Data Synchronization', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
  });

  test('should sync banner button data from savedGuideData to buttonsContainer', () => {
    const state = store.getState();
    
    // Initially buttonsContainer should be empty
    expect(state.buttonsContainer).toHaveLength(0);
    
    // Call the sync function
    state.syncBannerButtonDataFromSavedGuide();
    
    // Check that buttonsContainer is now populated
    const updatedState = store.getState();
    expect(updatedState.buttonsContainer).toHaveLength(1);
    expect(updatedState.buttonsContainer[0].buttons).toHaveLength(1);
    expect(updatedState.buttonsContainer[0].buttons[0].name).toBe("Test Button");
    expect(updatedState.buttonsContainer[0].buttons[0].actions.value).toBe("close");
  });

  test('should ensure banner button container exists when empty', () => {
    const state = store.getState();
    
    // Initially buttonsContainer should be empty
    expect(state.buttonsContainer).toHaveLength(0);
    
    // Call the ensure function
    state.ensureBannerButtonContainer();
    
    // Check that buttonsContainer is now initialized
    const updatedState = store.getState();
    expect(updatedState.buttonsContainer).toHaveLength(1);
    expect(updatedState.buttonsContainer[0].buttons).toHaveLength(1);
    expect(updatedState.buttonsContainer[0].buttons[0].name).toBe("Got It");
  });

  test('should clear buttonsContainer when no button data exists in savedGuideData', () => {
    // First populate buttonsContainer
    store.setState({
      buttonsContainer: [{
        id: 'test-id',
        buttons: [],
        style: {},
        type: 'button'
      }]
    });
    
    // Clear savedGuideData button section
    store.setState({
      savedGuideData: {
        GuideStep: [
          {
            StepType: "Banner",
            ButtonSection: []
          }
        ]
      }
    });
    
    const state = store.getState();
    state.syncBannerButtonDataFromSavedGuide();
    
    // Check that buttonsContainer is cleared
    const updatedState = store.getState();
    expect(updatedState.buttonsContainer).toHaveLength(0);
  });
});
