[{"E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "5", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "6", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "7", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "9", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "10", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "11", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "12", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "13", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "14", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "15", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "16", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "17", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "18", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "19", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "20", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "21", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "22", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "32", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "33", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "37", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "38", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "39", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "40", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "41", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "42", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "43", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "44", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "45", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "46", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "47", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "48", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "49", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "50", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "51", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "52", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "53", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "54", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "55", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "56", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "57", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "58", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "59", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "60", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "61", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "62", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "63", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "64", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "65", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "66", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "68", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "69", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "70", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "71", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "72", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "73", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "74", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "75", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "78", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "79", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "80", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "81", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "82", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "83", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "84", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "85", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "86", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "87", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "88", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89"}, {"size": 604, "mtime": 1753074278572, "results": "90", "hashOfConfig": "91"}, {"size": 440, "mtime": 1753074278578, "results": "92", "hashOfConfig": "91"}, {"size": 1161, "mtime": 1753074278337, "results": "93", "hashOfConfig": "91"}, {"size": 2631, "mtime": 1753074278589, "results": "94", "hashOfConfig": "91"}, {"size": 6057, "mtime": 1753074357625, "results": "95", "hashOfConfig": "91"}, {"size": 636, "mtime": 1753074278561, "results": "96", "hashOfConfig": "91"}, {"size": 237987, "mtime": 1753093748724, "results": "97", "hashOfConfig": "91"}, {"size": 2997, "mtime": 1753074357688, "results": "98", "hashOfConfig": "91"}, {"size": 193, "mtime": 1753074278498, "results": "99", "hashOfConfig": "91"}, {"size": 392608, "mtime": 1753093654156, "results": "100", "hashOfConfig": "91"}, {"size": 3927, "mtime": 1753074357719, "results": "101", "hashOfConfig": "91"}, {"size": 6144, "mtime": 1753074357719, "results": "102", "hashOfConfig": "91"}, {"size": 1898, "mtime": 1753074357704, "results": "103", "hashOfConfig": "91"}, {"size": 26975, "mtime": 1753074357704, "results": "104", "hashOfConfig": "91"}, {"size": 1954, "mtime": 1753074357704, "results": "105", "hashOfConfig": "91"}, {"size": 7516, "mtime": 1753074357704, "results": "106", "hashOfConfig": "91"}, {"size": 40264, "mtime": 1753074357672, "results": "107", "hashOfConfig": "91"}, {"size": 5065, "mtime": 1753074278539, "results": "108", "hashOfConfig": "91"}, {"size": 12174, "mtime": 1753074357688, "results": "109", "hashOfConfig": "91"}, {"size": 3584, "mtime": 1753074357657, "results": "110", "hashOfConfig": "91"}, {"size": 12086, "mtime": 1753074357688, "results": "111", "hashOfConfig": "91"}, {"size": 296570, "mtime": 1753074357594, "results": "112", "hashOfConfig": "91"}, {"size": 9056, "mtime": 1753074357625, "results": "113", "hashOfConfig": "91"}, {"size": 29952, "mtime": 1753074357625, "results": "114", "hashOfConfig": "91"}, {"size": 2766, "mtime": 1753074357641, "results": "115", "hashOfConfig": "91"}, {"size": 2448, "mtime": 1753074357594, "results": "116", "hashOfConfig": "91"}, {"size": 33001, "mtime": 1753074357672, "results": "117", "hashOfConfig": "91"}, {"size": 23141, "mtime": 1753090913096, "results": "118", "hashOfConfig": "91"}, {"size": 13526, "mtime": 1753090913080, "results": "119", "hashOfConfig": "91"}, {"size": 26424, "mtime": 1753074357594, "results": "120", "hashOfConfig": "91"}, {"size": 49705, "mtime": 1753074357594, "results": "121", "hashOfConfig": "91"}, {"size": 30010, "mtime": 1753093632418, "results": "122", "hashOfConfig": "91"}, {"size": 10169, "mtime": 1753074357704, "results": "123", "hashOfConfig": "91"}, {"size": 10365, "mtime": 1753074357688, "results": "124", "hashOfConfig": "91"}, {"size": 24200, "mtime": 1753074357688, "results": "125", "hashOfConfig": "91"}, {"size": 4880, "mtime": 1753074357610, "results": "126", "hashOfConfig": "91"}, {"size": 743, "mtime": 1753074278497, "results": "127", "hashOfConfig": "91"}, {"size": 9238, "mtime": 1753074357704, "results": "128", "hashOfConfig": "91"}, {"size": 2608, "mtime": 1753074357704, "results": "129", "hashOfConfig": "91"}, {"size": 1297, "mtime": 1753074357704, "results": "130", "hashOfConfig": "91"}, {"size": 1248, "mtime": 1753074278585, "results": "131", "hashOfConfig": "91"}, {"size": 2595, "mtime": 1753074357641, "results": "132", "hashOfConfig": "91"}, {"size": 3279, "mtime": 1753074278540, "results": "133", "hashOfConfig": "91"}, {"size": 2802, "mtime": 1753074278535, "results": "134", "hashOfConfig": "91"}, {"size": 850, "mtime": 1753074357594, "results": "135", "hashOfConfig": "91"}, {"size": 19014, "mtime": 1753074357688, "results": "136", "hashOfConfig": "91"}, {"size": 14238, "mtime": 1753074357719, "results": "137", "hashOfConfig": "91"}, {"size": 24357, "mtime": 1753074357672, "results": "138", "hashOfConfig": "91"}, {"size": 7584, "mtime": 1753074278548, "results": "139", "hashOfConfig": "91"}, {"size": 1962, "mtime": 1753074278501, "results": "140", "hashOfConfig": "91"}, {"size": 28404, "mtime": 1753074357625, "results": "141", "hashOfConfig": "91"}, {"size": 25734, "mtime": 1753074357641, "results": "142", "hashOfConfig": "91"}, {"size": 526, "mtime": 1753074357641, "results": "143", "hashOfConfig": "91"}, {"size": 2237, "mtime": 1753074357641, "results": "144", "hashOfConfig": "91"}, {"size": 13646, "mtime": 1753074357641, "results": "145", "hashOfConfig": "91"}, {"size": 28241, "mtime": 1753074357672, "results": "146", "hashOfConfig": "91"}, {"size": 15787, "mtime": 1753093654141, "results": "147", "hashOfConfig": "91"}, {"size": 6245, "mtime": 1753074278542, "results": "148", "hashOfConfig": "91"}, {"size": 18698, "mtime": 1753074357672, "results": "149", "hashOfConfig": "91"}, {"size": 20034, "mtime": 1753074357672, "results": "150", "hashOfConfig": "91"}, {"size": 6431, "mtime": 1753074357672, "results": "151", "hashOfConfig": "91"}, {"size": 2848, "mtime": 1753074278531, "results": "152", "hashOfConfig": "91"}, {"size": 3236, "mtime": 1753074278529, "results": "153", "hashOfConfig": "91"}, {"size": 14963, "mtime": 1753074357625, "results": "154", "hashOfConfig": "91"}, {"size": 29707, "mtime": 1753074357594, "results": "155", "hashOfConfig": "91"}, {"size": 1486, "mtime": 1753074357594, "results": "156", "hashOfConfig": "91"}, {"size": 13866, "mtime": 1753074357641, "results": "157", "hashOfConfig": "91"}, {"size": 32112, "mtime": 1753074357610, "results": "158", "hashOfConfig": "91"}, {"size": 16126, "mtime": 1753090913111, "results": "159", "hashOfConfig": "91"}, {"size": 10580, "mtime": 1753074357657, "results": "160", "hashOfConfig": "91"}, {"size": 8222, "mtime": 1753074278527, "results": "161", "hashOfConfig": "91"}, {"size": 15164, "mtime": 1753074357688, "results": "162", "hashOfConfig": "91"}, {"size": 15337, "mtime": 1753074357657, "results": "163", "hashOfConfig": "91"}, {"size": 25982, "mtime": 1753074357625, "results": "164", "hashOfConfig": "91"}, {"size": 60402, "mtime": 1753074357610, "results": "165", "hashOfConfig": "91"}, {"size": 5003, "mtime": 1753074278550, "results": "166", "hashOfConfig": "91"}, {"size": 883, "mtime": 1753074278558, "results": "167", "hashOfConfig": "91"}, {"size": 5325, "mtime": 1753074357641, "results": "168", "hashOfConfig": "91"}, {"size": 33004, "mtime": 1753074357641, "results": "169", "hashOfConfig": "91"}, {"size": 2120, "mtime": 1753074357641, "results": "170", "hashOfConfig": "91"}, {"size": 491, "mtime": 1753074357704, "results": "171", "hashOfConfig": "91"}, {"size": 36745, "mtime": 1753074357641, "results": "172", "hashOfConfig": "91"}, {"size": 7943, "mtime": 1753074357704, "results": "173", "hashOfConfig": "91"}, {"size": 26156, "mtime": 1753074357610, "results": "174", "hashOfConfig": "91"}, {"size": 17048, "mtime": 1753074357610, "results": "175", "hashOfConfig": "91"}, {"size": 10494, "mtime": 1753074357625, "results": "176", "hashOfConfig": "91"}, {"size": 2669, "mtime": 1753074278525, "results": "177", "hashOfConfig": "91"}, {"size": 2931, "mtime": 1753074357657, "results": "178", "hashOfConfig": "91"}, {"size": 14292, "mtime": 1753074357610, "results": "179", "hashOfConfig": "91"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "n0kw7i", {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 218, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["447", "448", "449"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["450"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["451", "452", "453", "454", "455", "456"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["675", "676"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["697"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["698"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["699", "700", "701", "702"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["703", "704", "705"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["759"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["783", "784", "785", "786", "787", "788", "789", "790"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["817", "818", "819", "820"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1081", "1082", "1083", "1084", "1085", "1086"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1087", "1088", "1089", "1090"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1091", "1092"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1093", "1094", "1095"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1096", "1097", "1098"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1099"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1100", "1101"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1112", "1113", "1114"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", ["1180"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1238", "1239", "1240"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1268", "1269", "1270", "1271"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1292", "1293", "1294", "1295", "1296", "1297"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1298", "1299"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1300", "1301", "1302"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1349", "1350", "1351"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1595", "1596"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1804", "1805"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819"], [], "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832"], [], {"ruleId": "1833", "severity": 1, "message": "1834", "line": 2, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1837", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1838", "line": 9, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1839", "line": 1, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1840", "line": 2, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "1841", "line": 7, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1842", "line": 7, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "1843", "line": 93, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 93, "endColumn": 25}, {"ruleId": "1844", "severity": 1, "message": "1845", "line": 98, "column": 6, "nodeType": "1846", "endLine": 98, "endColumn": 8, "suggestions": "1847"}, {"ruleId": "1833", "severity": 1, "message": "1848", "line": 120, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 120, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1849", "line": 1, "column": 58, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 65}, {"ruleId": "1833", "severity": 1, "message": "1850", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1851", "line": 5, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1852", "line": 6, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "1853", "line": 10, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 40}, {"ruleId": "1833", "severity": 1, "message": "1854", "line": 14, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "1855", "line": 19, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1856", "line": 20, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1857", "line": 21, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1858", "line": 22, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1859", "line": 23, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1860", "line": 24, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1861", "line": 25, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 25, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1862", "line": 26, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1863", "line": 27, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 27, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "1864", "line": 28, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 28, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1865", "line": 29, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1866", "line": 30, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 30, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1867", "line": 31, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 31, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1868", "line": 32, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1869", "line": 34, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 34, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1870", "line": 35, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 35, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1871", "line": 36, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 36, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1872", "line": 37, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 37, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 41, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1874", "line": 47, "column": 20, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 57, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1876", "line": 58, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 58, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1877", "line": 65, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 65, "endColumn": 6}, {"ruleId": "1833", "severity": 1, "message": "1878", "line": 66, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 66, "endColumn": 6}, {"ruleId": "1833", "severity": 1, "message": "1879", "line": 73, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 73, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1880", "line": 75, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 75, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1881", "line": 76, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 76, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "1882", "line": 76, "column": 30, "nodeType": "1835", "messageId": "1836", "endLine": 76, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "1883", "line": 79, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 79, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1884", "line": 80, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1885", "line": 81, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1886", "line": 85, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 85, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1887", "line": 87, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1888", "line": 92, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 92, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1889", "line": 99, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 99, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1890", "line": 102, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1891", "line": 103, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "1892", "line": 108, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 108, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "1893", "line": 108, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 108, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "1894", "line": 111, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 111, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1895", "line": 118, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 118, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1896", "line": 130, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 130, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1897", "line": 193, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 193, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1898", "line": 210, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 210, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1899", "line": 218, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 218, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "1900", "line": 374, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 374, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1901", "line": 409, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 409, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1902", "line": 411, "column": 6, "nodeType": "1835", "messageId": "1836", "endLine": 411, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1903", "line": 413, "column": 6, "nodeType": "1835", "messageId": "1836", "endLine": 413, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "1904", "line": 426, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 426, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1905", "line": 427, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 427, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1906", "line": 429, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 429, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "1907", "line": 432, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 432, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1908", "line": 436, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 436, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1909", "line": 437, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 437, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1910", "line": 448, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 448, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1911", "line": 449, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 449, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1912", "line": 450, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 450, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1913", "line": 452, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 452, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1914", "line": 452, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 452, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "1915", "line": 457, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 457, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1916", "line": 457, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 457, "endColumn": 38}, {"ruleId": "1833", "severity": 1, "message": "1917", "line": 459, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 459, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1918", "line": 459, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 459, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "1919", "line": 462, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 462, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1920", "line": 462, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 462, "endColumn": 40}, {"ruleId": "1833", "severity": 1, "message": "1921", "line": 463, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 463, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "1922", "line": 468, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 468, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "1923", "line": 468, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 468, "endColumn": 50}, {"ruleId": "1833", "severity": 1, "message": "1924", "line": 475, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 475, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1925", "line": 475, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 475, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1926", "line": 477, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 477, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "1927", "line": 477, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 477, "endColumn": 41}, {"ruleId": "1833", "severity": 1, "message": "1928", "line": 479, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 479, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1929", "line": 479, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 479, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1930", "line": 492, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 492, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "1931", "line": 493, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 493, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "1932", "line": 493, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 493, "endColumn": 58}, {"ruleId": "1833", "severity": 1, "message": "1933", "line": 496, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 496, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1934", "line": 496, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 496, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "1935", "line": 497, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 497, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1936", "line": 497, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 497, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1937", "line": 498, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 498, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "1938", "line": 498, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 498, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "1939", "line": 507, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 507, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "1940", "line": 508, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 508, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1941", "line": 514, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 514, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1942", "line": 519, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 519, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1943", "line": 519, "column": 20, "nodeType": "1835", "messageId": "1836", "endLine": 519, "endColumn": 32}, {"ruleId": "1844", "severity": 1, "message": "1944", "line": 558, "column": 5, "nodeType": "1846", "endLine": 558, "endColumn": 27, "suggestions": "1945"}, {"ruleId": "1833", "severity": 1, "message": "1946", "line": 568, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 568, "endColumn": 6}, {"ruleId": "1833", "severity": 1, "message": "1947", "line": 569, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 569, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 570, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 570, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1949", "line": 572, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 572, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1950", "line": 573, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 573, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "1951", "line": 578, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 578, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1952", "line": 579, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 579, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1953", "line": 614, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 614, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1954", "line": 615, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 615, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1955", "line": 616, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 616, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1956", "line": 624, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 624, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1957", "line": 626, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 626, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1958", "line": 627, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 627, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1959", "line": 628, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 628, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1960", "line": 629, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 629, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1961", "line": 634, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 634, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1962", "line": 636, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 636, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1963", "line": 638, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 638, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1964", "line": 648, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 648, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1965", "line": 649, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 649, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1966", "line": 652, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 652, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1967", "line": 656, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 656, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1968", "line": 658, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 658, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "1969", "line": 659, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 659, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1970", "line": 661, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 661, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1971", "line": 668, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 668, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1972", "line": 669, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 669, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1973", "line": 674, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 674, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1974", "line": 675, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 675, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1975", "line": 676, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 676, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "1976", "line": 686, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 686, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1977", "line": 690, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 690, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1978", "line": 694, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 694, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1979", "line": 696, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 696, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1980", "line": 698, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 698, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "1981", "line": 699, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 699, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1982", "line": 704, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 704, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1983", "line": 705, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 705, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1984", "line": 716, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 716, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "1985", "line": 723, "column": 18, "nodeType": "1835", "messageId": "1836", "endLine": 723, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "1986", "line": 724, "column": 18, "nodeType": "1835", "messageId": "1836", "endLine": 724, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "1987", "line": 728, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 728, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1988", "line": 740, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 740, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "1989", "line": 765, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 765, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1990", "line": 776, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 776, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1991", "line": 781, "column": 25, "nodeType": "1835", "messageId": "1836", "endLine": 781, "endColumn": 42}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 786, "column": 22, "nodeType": "1994", "messageId": "1995", "endLine": 786, "endColumn": 24}, {"ruleId": "1844", "severity": 1, "message": "1996", "line": 872, "column": 5, "nodeType": "1846", "endLine": 872, "endColumn": 46, "suggestions": "1997"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 872, "column": 6, "nodeType": "1999", "endLine": 872, "endColumn": 29}, {"ruleId": "1844", "severity": 1, "message": "2000", "line": 890, "column": 5, "nodeType": "1846", "endLine": 890, "endColumn": 18, "suggestions": "2001"}, {"ruleId": "1833", "severity": 1, "message": "2002", "line": 892, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 892, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2003", "line": 893, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 893, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2004", "line": 914, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 914, "endColumn": 24}, {"ruleId": "1844", "severity": 1, "message": "2005", "line": 938, "column": 8, "nodeType": "2006", "endLine": 940, "endColumn": 3}, {"ruleId": "1844", "severity": 1, "message": "2007", "line": 975, "column": 5, "nodeType": "1846", "endLine": 983, "endColumn": 3, "suggestions": "2008"}, {"ruleId": "1844", "severity": 1, "message": "2009", "line": 1011, "column": 5, "nodeType": "1846", "endLine": 1034, "endColumn": 3, "suggestions": "2010"}, {"ruleId": "1844", "severity": 1, "message": "2011", "line": 1152, "column": 5, "nodeType": "1846", "endLine": 1152, "endColumn": 39, "suggestions": "2012"}, {"ruleId": "1833", "severity": 1, "message": "2013", "line": 1262, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 1262, "endColumn": 24}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1353, "column": 25, "nodeType": "1994", "messageId": "1995", "endLine": 1353, "endColumn": 27}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1360, "column": 25, "nodeType": "1994", "messageId": "1995", "endLine": 1360, "endColumn": 27}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1360, "column": 53, "nodeType": "1994", "messageId": "1995", "endLine": 1360, "endColumn": 55}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1363, "column": 26, "nodeType": "1994", "messageId": "1995", "endLine": 1363, "endColumn": 28}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1363, "column": 58, "nodeType": "1994", "messageId": "1995", "endLine": 1363, "endColumn": 60}, {"ruleId": "1833", "severity": 1, "message": "2015", "line": 1494, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 1494, "endColumn": 33}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1571, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 1571, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2016", "line": 1717, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 1717, "endColumn": 30}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 1978, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 1978, "endColumn": 21}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 2150, "column": 25, "nodeType": "1994", "messageId": "1995", "endLine": 2150, "endColumn": 27}, {"ruleId": "1844", "severity": 1, "message": "2017", "line": 2182, "column": 5, "nodeType": "1846", "endLine": 2182, "endColumn": 18, "suggestions": "2018"}, {"ruleId": "1833", "severity": 1, "message": "2019", "line": 2239, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 2239, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "2020", "line": 2246, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 2246, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2021", "line": 2249, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 2249, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2022", "line": 2249, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 2249, "endColumn": 48}, {"ruleId": "1833", "severity": 1, "message": "2023", "line": 2641, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 2641, "endColumn": 27}, {"ruleId": "1844", "severity": 1, "message": "2024", "line": 2676, "column": 5, "nodeType": "1846", "endLine": 2676, "endColumn": 38, "suggestions": "2025"}, {"ruleId": "1833", "severity": 1, "message": "2026", "line": 2693, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 2693, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2027", "line": 2727, "column": 6, "nodeType": "1835", "messageId": "1836", "endLine": 2727, "endColumn": 18}, {"ruleId": "1844", "severity": 1, "message": "2028", "line": 3111, "column": 4, "nodeType": "1846", "endLine": 3111, "endColumn": 18, "suggestions": "2029"}, {"ruleId": "1833", "severity": 1, "message": "2030", "line": 3448, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3448, "endColumn": 33}, {"ruleId": "1844", "severity": 1, "message": "2031", "line": 3522, "column": 16, "nodeType": "1999", "endLine": 3522, "endColumn": 37}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 3525, "column": 49, "nodeType": "1994", "messageId": "1995", "endLine": 3525, "endColumn": 51}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 3529, "column": 50, "nodeType": "1994", "messageId": "1995", "endLine": 3529, "endColumn": 52}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 3535, "column": 51, "nodeType": "1994", "messageId": "1995", "endLine": 3535, "endColumn": 53}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 3542, "column": 51, "nodeType": "1994", "messageId": "1995", "endLine": 3542, "endColumn": 53}, {"ruleId": "1833", "severity": 1, "message": "2032", "line": 3763, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3763, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 3771, "column": 30, "nodeType": "1994", "messageId": "1995", "endLine": 3771, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2033", "line": 3784, "column": 12, "nodeType": "1835", "messageId": "1836", "endLine": 3784, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2034", "line": 3798, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 3798, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2035", "line": 3798, "column": 30, "nodeType": "1835", "messageId": "1836", "endLine": 3798, "endColumn": 52}, {"ruleId": "1833", "severity": 1, "message": "2036", "line": 3912, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3912, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2037", "line": 3914, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 3914, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2038", "line": 3918, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3918, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2039", "line": 3937, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 3937, "endColumn": 26}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 3958, "column": 66, "nodeType": "1994", "messageId": "1995", "endLine": 3958, "endColumn": 68}, {"ruleId": "1844", "severity": 1, "message": "2040", "line": 3965, "column": 5, "nodeType": "1846", "endLine": 3972, "endColumn": 3, "suggestions": "2041"}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 4206, "column": 17, "nodeType": "1994", "messageId": "1995", "endLine": 4206, "endColumn": 19}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 4460, "column": 21, "nodeType": "1994", "messageId": "1995", "endLine": 4460, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 4468, "column": 21, "nodeType": "1994", "messageId": "1995", "endLine": 4468, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 4481, "column": 15, "nodeType": "1994", "messageId": "1995", "endLine": 4481, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2042", "line": 4778, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 4778, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2043", "line": 4789, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 4789, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2044", "line": 4790, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 4790, "endColumn": 20}, {"ruleId": "1844", "severity": 1, "message": "2045", "line": 4796, "column": 5, "nodeType": "1846", "endLine": 4796, "endColumn": 62, "suggestions": "2046"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 4796, "column": 6, "nodeType": "2047", "endLine": 4796, "endColumn": 48}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 4819, "column": 25, "nodeType": "1994", "messageId": "1995", "endLine": 4819, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2048", "line": 4823, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4823, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2049", "line": 4846, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 4846, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 4921, "column": 25, "nodeType": "1994", "messageId": "1995", "endLine": 4921, "endColumn": 27}, {"ruleId": "1844", "severity": 1, "message": "2050", "line": 4954, "column": 5, "nodeType": "1846", "endLine": 4954, "endColumn": 22, "suggestions": "2051"}, {"ruleId": "1833", "severity": 1, "message": "2052", "line": 4956, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4956, "endColumn": 18}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 4958, "column": 40, "nodeType": "1994", "messageId": "1995", "endLine": 4958, "endColumn": 42}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 5023, "column": 69, "nodeType": "1994", "messageId": "1995", "endLine": 5023, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2053", "line": 5073, "column": 12, "nodeType": "1835", "messageId": "1836", "endLine": 5073, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2054", "line": 5074, "column": 12, "nodeType": "1835", "messageId": "1836", "endLine": 5074, "endColumn": 22}, {"ruleId": "1844", "severity": 1, "message": "2055", "line": 5104, "column": 5, "nodeType": "1846", "endLine": 5104, "endColumn": 38, "suggestions": "2056"}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 5107, "column": 40, "nodeType": "1994", "messageId": "1995", "endLine": 5107, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2053", "line": 5113, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5113, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2054", "line": 5114, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5114, "endColumn": 20}, {"ruleId": "1844", "severity": 1, "message": "2057", "line": 5120, "column": 5, "nodeType": "1846", "endLine": 5120, "endColumn": 106, "suggestions": "2058"}, {"ruleId": "1844", "severity": 1, "message": "2059", "line": 5233, "column": 5, "nodeType": "1846", "endLine": 5233, "endColumn": 17, "suggestions": "2060"}, {"ruleId": "2061", "severity": 1, "message": "2062", "line": 5826, "column": 80, "nodeType": "2063", "messageId": "2064", "endLine": 5826, "endColumn": 81, "suggestions": "2065"}, {"ruleId": "1833", "severity": 1, "message": "2066", "line": 6039, "column": 25, "nodeType": "1835", "messageId": "1836", "endLine": 6039, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2067", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2068", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2069", "line": 3, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2070", "line": 8, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2071", "line": 9, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2072", "line": 13, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 46}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 2400, "column": 5, "nodeType": "2075", "messageId": "1995", "endLine": 2400, "endColumn": 17}, {"ruleId": "2073", "severity": 1, "message": "2076", "line": 2401, "column": 5, "nodeType": "2075", "messageId": "1995", "endLine": 2401, "endColumn": 20}, {"ruleId": "2073", "severity": 1, "message": "2077", "line": 2732, "column": 5, "nodeType": "2075", "messageId": "1995", "endLine": 2732, "endColumn": 24}, {"ruleId": "2073", "severity": 1, "message": "2078", "line": 2909, "column": 5, "nodeType": "2075", "messageId": "1995", "endLine": 2909, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2079", "line": 3613, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 3613, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2079", "line": 3812, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 3812, "endColumn": 28}, {"ruleId": "2073", "severity": 1, "message": "2080", "line": 5247, "column": 5, "nodeType": "2075", "messageId": "1995", "endLine": 5247, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2081", "line": 5310, "column": 14, "nodeType": "1835", "messageId": "1836", "endLine": 5310, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2082", "line": 6401, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 6401, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2082", "line": 6427, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 6427, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2082", "line": 6433, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 6433, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2082", "line": 6448, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 6448, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2081", "line": 7225, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 7225, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2081", "line": 7469, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 7469, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2081", "line": 7640, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 7640, "endColumn": 16}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 8305, "column": 66, "nodeType": "1994", "messageId": "1995", "endLine": 8305, "endColumn": 68}, {"ruleId": "1833", "severity": 1, "message": "2083", "line": 70, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 70, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2084", "line": 1, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2085", "line": 226, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 226, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2086", "line": 296, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 296, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2033", "line": 681, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 681, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2087", "line": 686, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 686, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2088", "line": 1, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2089", "line": 3, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2090", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2091", "line": 2, "column": 44, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 56}, {"ruleId": "1833", "severity": 1, "message": "2092", "line": 18, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2093", "line": 19, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 20, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2095", "line": 21, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "2096", "line": 24, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2097", "line": 25, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 25, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2098", "line": 26, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2099", "line": 31, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 31, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2100", "line": 38, "column": 49, "nodeType": "1835", "messageId": "1836", "endLine": 38, "endColumn": 55}, {"ruleId": "1833", "severity": 1, "message": "2101", "line": 38, "column": 63, "nodeType": "1835", "messageId": "1836", "endLine": 38, "endColumn": 70}, {"ruleId": "1833", "severity": 1, "message": "2102", "line": 46, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2103", "line": 48, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2104", "line": 90, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 90, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2105", "line": 91, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 91, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2106", "line": 97, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 97, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2107", "line": 98, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 98, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2108", "line": 102, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2109", "line": 106, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 106, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2110", "line": 110, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 110, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2111", "line": 111, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 111, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2112", "line": 112, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 112, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2113", "line": 113, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 113, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2114", "line": 114, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 114, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2115", "line": 117, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 117, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2116", "line": 118, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 118, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2117", "line": 160, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 160, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2118", "line": 170, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 170, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2119", "line": 178, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 178, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2120", "line": 179, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 179, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2121", "line": 180, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 180, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2122", "line": 181, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 181, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2123", "line": 182, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 182, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2124", "line": 203, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 203, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2125", "line": 207, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 207, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2126", "line": 453, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 453, "endColumn": 26}, {"ruleId": "1844", "severity": 1, "message": "2127", "line": 545, "column": 5, "nodeType": "1846", "endLine": 545, "endColumn": 60, "suggestions": "2128"}, {"ruleId": "1833", "severity": 1, "message": "2129", "line": 559, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 559, "endColumn": 22}, {"ruleId": "1844", "severity": 1, "message": "2130", "line": 579, "column": 5, "nodeType": "1846", "endLine": 579, "endColumn": 60, "suggestions": "2131"}, {"ruleId": "1844", "severity": 1, "message": "2132", "line": 596, "column": 4, "nodeType": "1846", "endLine": 596, "endColumn": 6, "suggestions": "2133"}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "1883", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1884", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1881", "line": 5, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "1882", "line": 5, "column": 46, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 55}, {"ruleId": "1833", "severity": 1, "message": "2135", "line": 6, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1893", "line": 11, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2136", "line": 18, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2137", "line": 21, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2138", "line": 22, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2139", "line": 23, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1926", "line": 32, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1927", "line": 32, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1858", "line": 6, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2141", "line": 9, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "2142", "line": 12, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2143", "line": 46, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2144", "line": 47, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1961", "line": 48, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2145", "line": 49, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2146", "line": 50, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1978", "line": 51, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2147", "line": 52, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 52, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2148", "line": 53, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 53, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1979", "line": 54, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2149", "line": 55, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2150", "line": 56, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 56, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2151", "line": 57, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2152", "line": 58, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 58, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2153", "line": 59, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 59, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2111", "line": 60, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 60, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2114", "line": 61, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 61, "endColumn": 23}, {"ruleId": "1844", "severity": 1, "message": "2154", "line": 73, "column": 5, "nodeType": "1846", "endLine": 73, "endColumn": 7, "suggestions": "2155"}, {"ruleId": "1844", "severity": 1, "message": "2156", "line": 90, "column": 5, "nodeType": "1846", "endLine": 90, "endColumn": 28, "suggestions": "2157"}, {"ruleId": "1844", "severity": 1, "message": "2158", "line": 101, "column": 5, "nodeType": "1846", "endLine": 101, "endColumn": 48, "suggestions": "2159"}, {"ruleId": "1833", "severity": 1, "message": "2160", "line": 180, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 180, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1849", "line": 2, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2161", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2162", "line": 6, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2163", "line": 6, "column": 44, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 59}, {"ruleId": "1833", "severity": 1, "message": "2164", "line": 8, "column": 34, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 57}, {"ruleId": "1833", "severity": 1, "message": "2165", "line": 8, "column": 59, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 79}, {"ruleId": "1833", "severity": 1, "message": "2166", "line": 57, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2167", "line": 122, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 122, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1849", "line": 1, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2168", "line": 8, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2162", "line": 9, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2169", "line": 77, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 77, "endColumn": 58}, {"ruleId": "1844", "severity": 1, "message": "2170", "line": 83, "column": 8, "nodeType": "2006", "endLine": 87, "endColumn": 12}, {"ruleId": "1844", "severity": 1, "message": "2171", "line": 83, "column": 8, "nodeType": "2006", "endLine": 87, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2172", "line": 89, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 89, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2173", "line": 89, "column": 25, "nodeType": "1835", "messageId": "1836", "endLine": 89, "endColumn": 42}, {"ruleId": "1844", "severity": 1, "message": "2174", "line": 134, "column": 5, "nodeType": "1846", "endLine": 134, "endColumn": 38, "suggestions": "2175"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 134, "column": 6, "nodeType": "1994", "endLine": 134, "endColumn": 37}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 134, "column": 33, "nodeType": "1994", "messageId": "1995", "endLine": 134, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2166", "line": 136, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 136, "endColumn": 24}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 154, "column": 56, "nodeType": "1994", "messageId": "1995", "endLine": 154, "endColumn": 58}, {"ruleId": "1833", "severity": 1, "message": "2176", "line": 161, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 161, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2177", "line": 162, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 162, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2178", "line": 285, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 285, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2166", "line": 769, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 769, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2179", "line": 804, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 804, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2180", "line": 804, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 804, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2181", "line": 805, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 805, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2182", "line": 805, "column": 22, "nodeType": "1835", "messageId": "1836", "endLine": 805, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "2021", "line": 806, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 806, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2022", "line": 806, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 806, "endColumn": 48}, {"ruleId": "1833", "severity": 1, "message": "2183", "line": 807, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 807, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2184", "line": 807, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 807, "endColumn": 46}, {"ruleId": "1833", "severity": 1, "message": "1940", "line": 808, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 808, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2185", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2186", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2187", "line": 29, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2188", "line": 30, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 30, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2189", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "1853", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2190", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 8, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 10, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 11, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2192", "line": 13, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2098", "line": 15, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2193", "line": 17, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 18, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2195", "line": 19, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 20, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2197", "line": 21, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 22, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2199", "line": 23, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2200", "line": 24, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "1878", "line": 3, "column": 65, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 69}, {"ruleId": "1833", "severity": 1, "message": "2201", "line": 6, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2202", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2203", "line": 20, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2180", "line": 81, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2204", "line": 82, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 82, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2205", "line": 82, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 82, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2206", "line": 83, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2207", "line": 83, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2208", "line": 87, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2209", "line": 87, "column": 45, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 62}, {"ruleId": "1833", "severity": 1, "message": "2210", "line": 90, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 90, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2211", "line": 91, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 91, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1933", "line": 92, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 92, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1934", "line": 93, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 93, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1935", "line": 94, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 94, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "1936", "line": 95, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 95, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1937", "line": 96, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 96, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1938", "line": 97, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 97, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2212", "line": 98, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 98, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 99, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 99, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2214", "line": 100, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 100, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 101, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 101, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2215", "line": 102, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2216", "line": 104, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 104, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2217", "line": 105, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 105, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2218", "line": 112, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 112, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2219", "line": 113, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 113, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2220", "line": 115, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 115, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2221", "line": 116, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 116, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2222", "line": 117, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 117, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2223", "line": 118, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 118, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2224", "line": 119, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 119, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2225", "line": 120, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 120, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2226", "line": 129, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 129, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2227", "line": 134, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 134, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2105", "line": 136, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 136, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2112", "line": 137, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 137, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2228", "line": 138, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 138, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2229", "line": 141, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 141, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2110", "line": 142, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 142, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2230", "line": 143, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 143, "endColumn": 20}, {"ruleId": "1844", "severity": 1, "message": "2231", "line": 167, "column": 5, "nodeType": "1846", "endLine": 167, "endColumn": 45, "suggestions": "2232"}, {"ruleId": "1833", "severity": 1, "message": "2233", "line": 218, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 218, "endColumn": 29}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 238, "column": 24, "nodeType": "1994", "messageId": "1995", "endLine": 238, "endColumn": 26}, {"ruleId": "1844", "severity": 1, "message": "2234", "line": 312, "column": 7, "nodeType": "1846", "endLine": 312, "endColumn": 42, "suggestions": "2235"}, {"ruleId": "1833", "severity": 1, "message": "2236", "line": 336, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 336, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2237", "line": 337, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 337, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2238", "line": 485, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 485, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2239", "line": 488, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 488, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2240", "line": 497, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 497, "endColumn": 31}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 812, "column": 26, "nodeType": "1994", "messageId": "1995", "endLine": 812, "endColumn": 28}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 848, "column": 26, "nodeType": "1994", "messageId": "1995", "endLine": 848, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2241", "line": 1032, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1032, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2242", "line": 1036, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1036, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2243", "line": 1040, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1040, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2244", "line": 1044, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1044, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2245", "line": 1048, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1048, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2246", "line": 1052, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1052, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2247", "line": 1056, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1056, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2248", "line": 1060, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1060, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2249", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2250", "line": 13, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "2251", "line": 15, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2252", "line": 78, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 78, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2253", "line": 80, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2254", "line": 81, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2255", "line": 82, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 82, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2256", "line": 83, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1956", "line": 87, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2257", "line": 88, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 88, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2258", "line": 90, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 90, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1958", "line": 91, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 91, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1959", "line": 92, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 92, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1957", "line": 93, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 93, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2115", "line": 103, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 19}, {"ruleId": "1844", "severity": 1, "message": "2259", "line": 208, "column": 7, "nodeType": "1846", "endLine": 208, "endColumn": 9, "suggestions": "2260"}, {"ruleId": "1844", "severity": 1, "message": "2261", "line": 243, "column": 7, "nodeType": "1846", "endLine": 243, "endColumn": 29, "suggestions": "2262"}, {"ruleId": "1844", "severity": 1, "message": "2263", "line": 248, "column": 7, "nodeType": "1846", "endLine": 248, "endColumn": 18, "suggestions": "2264"}, {"ruleId": "1844", "severity": 1, "message": "2265", "line": 291, "column": 7, "nodeType": "1846", "endLine": 291, "endColumn": 72, "suggestions": "2266"}, {"ruleId": "1833", "severity": 1, "message": "2215", "line": 330, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 330, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2267", "line": 333, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 333, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2268", "line": 462, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 462, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2269", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2270", "line": 6, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2271", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2272", "line": 7, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2273", "line": 8, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2274", "line": 9, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1958", "line": 13, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1959", "line": 14, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1957", "line": 15, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2275", "line": 17, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2276", "line": 18, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2277", "line": 18, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2278", "line": 19, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2279", "line": 96, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 96, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2280", "line": 97, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 97, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2281", "line": 100, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 100, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2282", "line": 101, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 101, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2283", "line": 109, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 109, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2284", "line": 129, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 129, "endColumn": 16}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 176, "column": 45, "nodeType": "1994", "messageId": "1995", "endLine": 176, "endColumn": 47}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 176, "column": 104, "nodeType": "1994", "messageId": "1995", "endLine": 176, "endColumn": 106}, {"ruleId": "1833", "severity": 1, "message": "2099", "line": 2, "column": 60, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 73}, {"ruleId": "1833", "severity": 1, "message": "2269", "line": 3, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2285", "line": 8, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2286", "line": 9, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2287", "line": 133, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 133, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2288", "line": 134, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 134, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2289", "line": 135, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 135, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2290", "line": 135, "column": 30, "nodeType": "1835", "messageId": "1836", "endLine": 135, "endColumn": 52}, {"ruleId": "1833", "severity": 1, "message": "2115", "line": 137, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 137, "endColumn": 19}, {"ruleId": "1844", "severity": 1, "message": "2291", "line": 163, "column": 8, "nodeType": "1846", "endLine": 163, "endColumn": 10, "suggestions": "2292"}, {"ruleId": "1833", "severity": 1, "message": "2293", "line": 288, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 288, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2294", "line": 331, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 331, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2295", "line": 332, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 332, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2296", "line": 333, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 333, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2297", "line": 335, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 335, "endColumn": 20}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 454, "column": 22, "nodeType": "1994", "messageId": "1995", "endLine": 454, "endColumn": 24}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 454, "column": 53, "nodeType": "1994", "messageId": "1995", "endLine": 454, "endColumn": 55}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 454, "column": 89, "nodeType": "1994", "messageId": "1995", "endLine": 454, "endColumn": 91}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 454, "column": 125, "nodeType": "1994", "messageId": "1995", "endLine": 454, "endColumn": 127}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 456, "column": 29, "nodeType": "1994", "messageId": "1995", "endLine": 456, "endColumn": 31}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 456, "column": 56, "nodeType": "1994", "messageId": "1995", "endLine": 456, "endColumn": 58}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 456, "column": 88, "nodeType": "1994", "messageId": "1995", "endLine": 456, "endColumn": 90}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 456, "column": 120, "nodeType": "1994", "messageId": "1995", "endLine": 456, "endColumn": 122}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 458, "column": 29, "nodeType": "1994", "messageId": "1995", "endLine": 458, "endColumn": 31}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 458, "column": 64, "nodeType": "1994", "messageId": "1995", "endLine": 458, "endColumn": 66}, {"ruleId": "1833", "severity": 1, "message": "2298", "line": 111, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 111, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2225", "line": 152, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 152, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1970", "line": 153, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 153, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2299", "line": 159, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 159, "endColumn": 23}, {"ruleId": "2300", "severity": 1, "message": "2301", "line": 225, "column": 25, "nodeType": "1835", "messageId": "2302", "endLine": 225, "endColumn": 34, "suggestions": "2303"}, {"ruleId": "1844", "severity": 1, "message": "2304", "line": 231, "column": 5, "nodeType": "1846", "endLine": 231, "endColumn": 12, "suggestions": "2305"}, {"ruleId": "1844", "severity": 1, "message": "2306", "line": 237, "column": 5, "nodeType": "1846", "endLine": 237, "endColumn": 21, "suggestions": "2307"}, {"ruleId": "1844", "severity": 1, "message": "2308", "line": 472, "column": 5, "nodeType": "1846", "endLine": 472, "endColumn": 70, "suggestions": "2309"}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 547, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 547, "endColumn": 21}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 548, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 548, "endColumn": 21}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 549, "column": 24, "nodeType": "1994", "messageId": "1995", "endLine": 549, "endColumn": 26}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 550, "column": 24, "nodeType": "1994", "messageId": "1995", "endLine": 550, "endColumn": 26}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 554, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 554, "endColumn": 21}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 555, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 555, "endColumn": 21}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 556, "column": 24, "nodeType": "1994", "messageId": "1995", "endLine": 556, "endColumn": 26}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 557, "column": 24, "nodeType": "1994", "messageId": "1995", "endLine": 557, "endColumn": 26}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 561, "column": 19, "nodeType": "1994", "messageId": "1995", "endLine": 561, "endColumn": 21}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 562, "column": 24, "nodeType": "1994", "messageId": "1995", "endLine": 562, "endColumn": 26}, {"ruleId": "1844", "severity": 1, "message": "2310", "line": 582, "column": 5, "nodeType": "1846", "endLine": 582, "endColumn": 64, "suggestions": "2311"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 582, "column": 6, "nodeType": "2047", "endLine": 582, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2312", "line": 591, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 591, "endColumn": 21}, {"ruleId": "1844", "severity": 1, "message": "2313", "line": 605, "column": 5, "nodeType": "1846", "endLine": 605, "endColumn": 47, "suggestions": "2314"}, {"ruleId": "1833", "severity": 1, "message": "2312", "line": 614, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 614, "endColumn": 21}, {"ruleId": "1844", "severity": 1, "message": "2313", "line": 627, "column": 5, "nodeType": "1846", "endLine": 627, "endColumn": 47, "suggestions": "2315"}, {"ruleId": "1844", "severity": 1, "message": "2316", "line": 1021, "column": 17, "nodeType": "1835", "endLine": 1021, "endColumn": 32}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 1227, "column": 43, "nodeType": "1994", "messageId": "1995", "endLine": 1227, "endColumn": 45}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 1232, "column": 78, "nodeType": "1994", "messageId": "1995", "endLine": 1232, "endColumn": 80}, {"ruleId": "1833", "severity": 1, "message": "2317", "line": 1, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 5, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 5}, {"ruleId": "1833", "severity": 1, "message": "2318", "line": 6, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2319", "line": 10, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2320", "line": 12, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 13, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2321", "line": 17, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2168", "line": 19, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2322", "line": 33, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2181", "line": 33, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 44}, {"ruleId": "2323", "severity": 1, "message": "2324", "line": 95, "column": 2, "nodeType": "2325", "messageId": "2326", "endLine": 111, "endColumn": 4}, {"ruleId": "1833", "severity": 1, "message": "2327", "line": 132, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 132, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2328", "line": 135, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 135, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 136, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 136, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 137, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 137, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2329", "line": 139, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 139, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2210", "line": 140, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 140, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2330", "line": 141, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 141, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2331", "line": 144, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 144, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2332", "line": 145, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 145, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2333", "line": 146, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 146, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2334", "line": 147, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 147, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2335", "line": 148, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 148, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2336", "line": 149, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 149, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2220", "line": 150, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 150, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2219", "line": 151, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 151, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2218", "line": 152, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 152, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2337", "line": 155, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 155, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2338", "line": 158, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 158, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2339", "line": 159, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 159, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2340", "line": 160, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 160, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2145", "line": 161, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 161, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2109", "line": 162, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 162, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2341", "line": 165, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 165, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2342", "line": 166, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 166, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2299", "line": 168, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 168, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2343", "line": 173, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 173, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1970", "line": 174, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 174, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2344", "line": 185, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 185, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2345", "line": 185, "column": 25, "nodeType": "1835", "messageId": "1836", "endLine": 185, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2346", "line": 187, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 187, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2347", "line": 187, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 187, "endColumn": 44}, {"ruleId": "2348", "severity": 1, "message": "2349", "line": 349, "column": 5, "nodeType": "2350", "messageId": "2351", "endLine": 349, "endColumn": 52}, {"ruleId": "1833", "severity": 1, "message": "2352", "line": 504, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 504, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2353", "line": 511, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 511, "endColumn": 21}, {"ruleId": "1844", "severity": 1, "message": "2354", "line": 668, "column": 5, "nodeType": "1846", "endLine": 668, "endColumn": 100, "suggestions": "2355"}, {"ruleId": "1844", "severity": 1, "message": "2356", "line": 775, "column": 5, "nodeType": "1846", "endLine": 775, "endColumn": 22, "suggestions": "2357"}, {"ruleId": "1833", "severity": 1, "message": "2358", "line": 868, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 868, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2359", "line": 875, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 875, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2360", "line": 5, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2361", "line": 6, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2362", "line": 6, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2069", "line": 7, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2152", "line": 26, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2114", "line": 29, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2363", "line": 32, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2364", "line": 145, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 145, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2365", "line": 146, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 146, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2360", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2366", "line": 1, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "2367", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2143", "line": 8, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2149", "line": 9, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2144", "line": 10, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2114", "line": 11, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2153", "line": 14, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2368", "line": 20, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2366", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2369", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2069", "line": 2, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 2, "column": 39, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2319", "line": 2, "column": 44, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 58}, {"ruleId": "1833", "severity": 1, "message": "2099", "line": 2, "column": 60, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 73}, {"ruleId": "1833", "severity": 1, "message": "2197", "line": 2, "column": 74, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 84}, {"ruleId": "1833", "severity": 1, "message": "2370", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2371", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2143", "line": 98, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 98, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2149", "line": 99, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 99, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2144", "line": 100, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 100, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "1961", "line": 101, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 101, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2145", "line": 102, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2146", "line": 103, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1978", "line": 104, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 104, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "1979", "line": 105, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 105, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2182", "line": 110, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 110, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2115", "line": 113, "column": 13, "nodeType": "1835", "messageId": "1836", "endLine": 113, "endColumn": 29}, {"ruleId": "1844", "severity": 1, "message": "2372", "line": 172, "column": 12, "nodeType": "1846", "endLine": 172, "endColumn": 35, "suggestions": "2373"}, {"ruleId": "1833", "severity": 1, "message": "1849", "line": 1, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "1842", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2090", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1891", "line": 7, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "2374", "line": 43, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 34}, {"ruleId": "1844", "severity": 1, "message": "2375", "line": 63, "column": 21, "nodeType": "2376", "endLine": 63, "endColumn": 111}, {"ruleId": "1833", "severity": 1, "message": "2377", "line": 2, "column": 25, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "2378", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "1841", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2379", "line": 11, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2380", "line": 1, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2381", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "1880", "line": 1, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2381", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2382", "line": 2, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 40}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2197", "line": 2, "column": 50, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 60}, {"ruleId": "1833", "severity": 1, "message": "2383", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2383", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2370", "line": 29, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2343", "line": 60, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 60, "endColumn": 43}, {"ruleId": "1833", "severity": 1, "message": "2384", "line": 67, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 67, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2385", "line": 69, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 69, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2386", "line": 91, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 91, "endColumn": 22}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 100, "column": 34, "nodeType": "1994", "messageId": "1995", "endLine": 100, "endColumn": 36}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 148, "column": 44, "nodeType": "1994", "messageId": "1995", "endLine": 148, "endColumn": 46}, {"ruleId": "1833", "severity": 1, "message": "2387", "line": 167, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 167, "endColumn": 21}, {"ruleId": "1844", "severity": 1, "message": "2388", "line": 291, "column": 5, "nodeType": "1846", "endLine": 291, "endColumn": 50, "suggestions": "2389"}, {"ruleId": "1844", "severity": 1, "message": "2388", "line": 307, "column": 5, "nodeType": "1846", "endLine": 307, "endColumn": 18, "suggestions": "2390"}, {"ruleId": "1833", "severity": 1, "message": "2360", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2391", "line": 59, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 59, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2392", "line": 61, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 61, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1849", "line": 5, "column": 28, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2393", "line": 8, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2172", "line": 82, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 82, "endColumn": 24}, {"ruleId": "1844", "severity": 1, "message": "2394", "line": 95, "column": 6, "nodeType": "1846", "endLine": 95, "endColumn": 8, "suggestions": "2395"}, {"ruleId": "1844", "severity": 1, "message": "2396", "line": 118, "column": 6, "nodeType": "1846", "endLine": 118, "endColumn": 32, "suggestions": "2397"}, {"ruleId": "1844", "severity": 1, "message": "2174", "line": 122, "column": 6, "nodeType": "1846", "endLine": 122, "endColumn": 40, "suggestions": "2398"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 122, "column": 7, "nodeType": "1994", "endLine": 122, "endColumn": 39}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 122, "column": 35, "nodeType": "1994", "messageId": "1995", "endLine": 122, "endColumn": 37}, {"ruleId": "1844", "severity": 1, "message": "2399", "line": 145, "column": 6, "nodeType": "1846", "endLine": 145, "endColumn": 33, "suggestions": "2400"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 145, "column": 7, "nodeType": "1999", "endLine": 145, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2401", "line": 153, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 153, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 5, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 6, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 6, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2193", "line": 6, "column": 80, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 6, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2195", "line": 6, "column": 105, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 111}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 6, "column": 113, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 121}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 6, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 6, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 6, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2405", "line": 6, "column": 168, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 180}, {"ruleId": "1833", "severity": 1, "message": "2406", "line": 6, "column": 182, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 199}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 8, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 8, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 8, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 9, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2411", "line": 17, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2412", "line": 18, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2413", "line": 19, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 20, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2414", "line": 21, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "2415", "line": 28, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 28, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2416", "line": 29, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2417", "line": 30, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 30, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2418", "line": 31, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 31, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2419", "line": 32, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2420", "line": 33, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2421", "line": 34, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 34, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2422", "line": 42, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2423", "line": 43, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2424", "line": 45, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 45, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2425", "line": 47, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2176", "line": 49, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2426", "line": 96, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 96, "endColumn": 19}, {"ruleId": "1844", "severity": 1, "message": "2427", "line": 127, "column": 5, "nodeType": "1846", "endLine": 127, "endColumn": 7, "suggestions": "2428"}, {"ruleId": "1833", "severity": 1, "message": "2429", "line": 147, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 147, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2430", "line": 164, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 164, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2431", "line": 167, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 167, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2432", "line": 172, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 172, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2433", "line": 213, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 213, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2434", "line": 216, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 216, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2435", "line": 229, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 229, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2436", "line": 230, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 230, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2437", "line": 230, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 230, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2438", "line": 231, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 231, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2439", "line": 231, "column": 20, "nodeType": "1835", "messageId": "1836", "endLine": 231, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "1928", "line": 247, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 247, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1929", "line": 247, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 247, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2440", "line": 249, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 249, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2441", "line": 263, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 263, "endColumn": 36}, {"ruleId": "1844", "severity": 1, "message": "2442", "line": 283, "column": 4, "nodeType": "1846", "endLine": 283, "endColumn": 6, "suggestions": "2443"}, {"ruleId": "1833", "severity": 1, "message": "2441", "line": 336, "column": 12, "nodeType": "1835", "messageId": "1836", "endLine": 336, "endColumn": 38}, {"ruleId": "1833", "severity": 1, "message": "2444", "line": 349, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 349, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2445", "line": 349, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 349, "endColumn": 45}, {"ruleId": "2446", "severity": 1, "message": "2447", "line": 41, "column": 11, "nodeType": "2448", "endLine": 51, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2193", "line": 2, "column": 80, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 2, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2195", "line": 2, "column": 105, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 111}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 2, "column": 113, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 121}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 2, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 2, "column": 168, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 175}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 4, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 4, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 4, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2411", "line": 8, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2412", "line": 9, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2413", "line": 10, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2414", "line": 11, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 12, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2449", "line": 13, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2450", "line": 14, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2451", "line": 15, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2452", "line": 22, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2453", "line": 27, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 27, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2164", "line": 28, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 28, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2422", "line": 31, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 31, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2423", "line": 32, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2424", "line": 34, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 34, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2454", "line": 35, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 35, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2455", "line": 36, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 36, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2456", "line": 38, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 38, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2457", "line": 39, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 39, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2458", "line": 40, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 40, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2459", "line": 41, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2460", "line": 42, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2461", "line": 43, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2462", "line": 44, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2463", "line": 45, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 45, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2464", "line": 46, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2465", "line": 47, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2384", "line": 54, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2466", "line": 56, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 56, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2426", "line": 71, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 71, "endColumn": 19}, {"ruleId": "1844", "severity": 1, "message": "2399", "line": 83, "column": 5, "nodeType": "1846", "endLine": 83, "endColumn": 45, "suggestions": "2467"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 83, "column": 6, "nodeType": "2047", "endLine": 83, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2468", "line": 102, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2469", "line": 102, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 38}, {"ruleId": "1833", "severity": 1, "message": "2470", "line": 103, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2471", "line": 103, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2472", "line": 104, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 104, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2108", "line": 105, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 105, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2473", "line": 105, "column": 18, "nodeType": "1835", "messageId": "1836", "endLine": 105, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2474", "line": 106, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 106, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2430", "line": 111, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 111, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2432", "line": 115, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 115, "endColumn": 25}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 130, "column": 11, "nodeType": "1994", "messageId": "1995", "endLine": 130, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2476", "line": 6, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 22}, {"ruleId": "1844", "severity": 1, "message": "2477", "line": 283, "column": 8, "nodeType": "1846", "endLine": 283, "endColumn": 10, "suggestions": "2478"}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 44, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 53}, {"ruleId": "1833", "severity": 1, "message": "2479", "line": 4, "column": 46, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 65}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 4, "column": 67, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 75}, {"ruleId": "1833", "severity": 1, "message": "2370", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2480", "line": 8, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2143", "line": 29, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2481", "line": 30, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 30, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "1957", "line": 33, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2482", "line": 42, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2483", "line": 43, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2484", "line": 44, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2485", "line": 45, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 45, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2486", "line": 49, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2487", "line": 49, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2488", "line": 50, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2489", "line": 51, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2490", "line": 54, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2491", "line": 55, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2492", "line": 55, "column": 20, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 32}, {"ruleId": "1844", "severity": 1, "message": "2493", "line": 63, "column": 5, "nodeType": "1846", "endLine": 63, "endColumn": 7, "suggestions": "2494"}, {"ruleId": "1833", "severity": 1, "message": "2495", "line": 91, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 91, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2496", "line": 95, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 95, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2497", "line": 122, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 122, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2498", "line": 130, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 130, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2499", "line": 134, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 134, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2500", "line": 148, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 148, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2501", "line": 151, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 151, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2502", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2503", "line": 5, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2504", "line": 10, "column": 30, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 6, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2195", "line": 9, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 10, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2193", "line": 11, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 12, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "2505", "line": 17, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2230", "line": 33, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2506", "line": 35, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 35, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2322", "line": 36, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 36, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2181", "line": 37, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 37, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2507", "line": 38, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 38, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2110", "line": 40, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 40, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2115", "line": 46, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2508", "line": 53, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 53, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2509", "line": 54, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2510", "line": 55, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2511", "line": 84, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 84, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2512", "line": 88, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 88, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2513", "line": 93, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 93, "endColumn": 33}, {"ruleId": "1844", "severity": 1, "message": "2514", "line": 193, "column": 5, "nodeType": "1846", "endLine": 193, "endColumn": 30, "suggestions": "2515"}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2405", "line": 2, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "2406", "line": 2, "column": 41, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 58}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 72, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 88}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 90, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 96}, {"ruleId": "1833", "severity": 1, "message": "2516", "line": 8, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 3, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 5}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 4, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 9, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2502", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2503", "line": 5, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2517", "line": 9, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2518", "line": 9, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2504", "line": 9, "column": 32, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 41}, {"ruleId": "1833", "severity": 1, "message": "2519", "line": 9, "column": 43, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 51}, {"ruleId": "1833", "severity": 1, "message": "2520", "line": 9, "column": 53, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 63}, {"ruleId": "1833", "severity": 1, "message": "2521", "line": 9, "column": 65, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 77}, {"ruleId": "1833", "severity": 1, "message": "2522", "line": 9, "column": 79, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 90}, {"ruleId": "1833", "severity": 1, "message": "2523", "line": 9, "column": 92, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 102}, {"ruleId": "1833", "severity": 1, "message": "2524", "line": 9, "column": 104, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 116}, {"ruleId": "1833", "severity": 1, "message": "2525", "line": 9, "column": 118, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 129}, {"ruleId": "1833", "severity": 1, "message": "2526", "line": 9, "column": 131, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2527", "line": 13, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 14, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2528", "line": 15, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2330", "line": 16, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 16, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2529", "line": 17, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 18, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2329", "line": 21, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2530", "line": 22, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2531", "line": 23, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2532", "line": 24, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2533", "line": 25, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 25, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2534", "line": 26, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2535", "line": 27, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 27, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2536", "line": 28, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 28, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2384", "line": 32, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 32, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2426", "line": 60, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 60, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2512", "line": 80, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2537", "line": 81, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2538", "line": 4, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2539", "line": 4, "column": 32, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 45}, {"ruleId": "1833", "severity": 1, "message": "2540", "line": 10, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2541", "line": 60, "column": 12, "nodeType": "1835", "messageId": "1836", "endLine": 60, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2542", "line": 60, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 60, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "2543", "line": 73, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 73, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2544", "line": 73, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 73, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "2545", "line": 149, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 149, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2546", "line": 272, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 272, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2547", "line": 288, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 288, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2548", "line": 453, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 453, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2549", "line": 454, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 454, "endColumn": 20}, {"ruleId": "1844", "severity": 1, "message": "2550", "line": 459, "column": 3, "nodeType": "1846", "endLine": 459, "endColumn": 5, "suggestions": "2551"}, {"ruleId": "1833", "severity": 1, "message": "2552", "line": 2, "column": 14, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2553", "line": 12, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2554", "line": 16, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 16, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2193", "line": 2, "column": 80, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 2, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2195", "line": 2, "column": 105, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 111}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 2, "column": 113, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 121}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 2, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 4, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 4, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 4, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2411", "line": 7, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2412", "line": 8, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2413", "line": 9, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2414", "line": 10, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2422", "line": 17, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2424", "line": 20, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2555", "line": 22, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2556", "line": 23, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2557", "line": 24, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2558", "line": 25, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 25, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2384", "line": 29, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2426", "line": 34, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 34, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2559", "line": 87, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2560", "line": 88, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 88, "endColumn": 40}, {"ruleId": "1833", "severity": 1, "message": "2429", "line": 103, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2430", "line": 137, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 137, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2561", "line": 140, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 140, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2318", "line": 4, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 8, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2563", "line": 15, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2564", "line": 15, "column": 30, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2565", "line": 15, "column": 46, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 60}, {"ruleId": "1833", "severity": 1, "message": "2566", "line": 15, "column": 62, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2567", "line": 15, "column": 80, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 96}, {"ruleId": "1833", "severity": 1, "message": "2568", "line": 17, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2569", "line": 17, "column": 35, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "2100", "line": 17, "column": 49, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 55}, {"ruleId": "1833", "severity": 1, "message": "2570", "line": 22, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2571", "line": 64, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 64, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2330", "line": 70, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 70, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2329", "line": 71, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 71, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2531", "line": 72, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 72, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 73, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 73, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2572", "line": 74, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 74, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2337", "line": 75, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 75, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2573", "line": 76, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 76, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2338", "line": 77, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 77, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2333", "line": 78, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 78, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2574", "line": 79, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 79, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2533", "line": 80, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2109", "line": 81, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2104", "line": 82, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 82, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2575", "line": 85, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 85, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2576", "line": 87, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2110", "line": 89, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 89, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2115", "line": 91, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 91, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2577", "line": 164, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 164, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2578", "line": 167, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 167, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2579", "line": 177, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 177, "endColumn": 21}, {"ruleId": "1844", "severity": 1, "message": "2580", "line": 330, "column": 5, "nodeType": "1846", "endLine": 330, "endColumn": 18, "suggestions": "2581"}, {"ruleId": "1833", "severity": 1, "message": "2340", "line": 607, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 607, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "2181", "line": 607, "column": 49, "nodeType": "1835", "messageId": "1836", "endLine": 607, "endColumn": 60}, {"ruleId": "1833", "severity": 1, "message": "2145", "line": 607, "column": 62, "nodeType": "1835", "messageId": "1836", "endLine": 607, "endColumn": 67}, {"ruleId": "1833", "severity": 1, "message": "2109", "line": 607, "column": 69, "nodeType": "1835", "messageId": "1836", "endLine": 607, "endColumn": 85}, {"ruleId": "1844", "severity": 1, "message": "2582", "line": 932, "column": 3, "nodeType": "1846", "endLine": 932, "endColumn": 5, "suggestions": "2583"}, {"ruleId": "1833", "severity": 1, "message": "2584", "line": 1004, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 1004, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2585", "line": 1013, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 1013, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2269", "line": 4, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1958", "line": 11, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1959", "line": 12, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1957", "line": 13, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2275", "line": 21, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2276", "line": 22, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2277", "line": 22, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 44}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 82, "column": 22, "nodeType": "1994", "messageId": "1995", "endLine": 82, "endColumn": 24}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 82, "column": 53, "nodeType": "1994", "messageId": "1995", "endLine": 82, "endColumn": 55}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 85, "column": 36, "nodeType": "1994", "messageId": "1995", "endLine": 85, "endColumn": 38}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 85, "column": 63, "nodeType": "1994", "messageId": "1995", "endLine": 85, "endColumn": 65}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 88, "column": 36, "nodeType": "1994", "messageId": "1995", "endLine": 88, "endColumn": 38}, {"ruleId": "1833", "severity": 1, "message": "2279", "line": 95, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 95, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2280", "line": 96, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 96, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2281", "line": 99, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 99, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2282", "line": 100, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 100, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2283", "line": 108, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 108, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2284", "line": 128, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 128, "endColumn": 16}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 268, "column": 45, "nodeType": "1994", "messageId": "1995", "endLine": 268, "endColumn": 47}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 268, "column": 104, "nodeType": "1994", "messageId": "1995", "endLine": 268, "endColumn": 106}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 3, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 3, "column": 56, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 63}, {"ruleId": "1833", "severity": 1, "message": "2111", "line": 11, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2210", "line": 12, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2211", "line": 13, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1933", "line": 14, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "1935", "line": 16, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 16, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "1936", "line": 17, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1937", "line": 18, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1938", "line": 19, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2212", "line": 20, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 21, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2214", "line": 22, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 23, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2586", "line": 35, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 35, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2252", "line": 37, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 37, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2587", "line": 39, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 39, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2588", "line": 43, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2589", "line": 47, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2590", "line": 47, "column": 25, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2591", "line": 48, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2592", "line": 48, "column": 21, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2593", "line": 49, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2594", "line": 49, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2595", "line": 50, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2596", "line": 50, "column": 30, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 52}, {"ruleId": "1833", "severity": 1, "message": "2597", "line": 51, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2598", "line": 51, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 46}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 56, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 65}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 2, "column": 67, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 75}, {"ruleId": "1833", "severity": 1, "message": "2069", "line": 2, "column": 77, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 83}, {"ruleId": "1833", "severity": 1, "message": "2479", "line": 13, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2599", "line": 44, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 48}, {"ruleId": "1833", "severity": 1, "message": "2293", "line": 56, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 56, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "2600", "line": 65, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 65, "endColumn": 41}, {"ruleId": "1833", "severity": 1, "message": "2601", "line": 71, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 71, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2360", "line": 1, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 2, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 2, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 4, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 4, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 4, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2602", "line": 21, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 21, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "1967", "line": 22, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 22, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2603", "line": 24, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "1963", "line": 25, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 25, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2604", "line": 26, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2605", "line": 27, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 27, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2237", "line": 83, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2606", "line": 3, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 8}, {"ruleId": "1833", "severity": 1, "message": "2607", "line": 4, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2608", "line": 5, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2609", "line": 6, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2369", "line": 9, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "2092", "line": 17, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2093", "line": 18, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 19, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 19, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2095", "line": 20, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "2096", "line": 23, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 23, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2097", "line": 24, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 24, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2098", "line": 25, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 25, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 26, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "2270", "line": 43, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2610", "line": 44, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2611", "line": 48, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2612", "line": 49, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2613", "line": 51, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2614", "line": 52, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 52, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2615", "line": 53, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 53, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2616", "line": 54, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2111", "line": 56, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 56, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2143", "line": 57, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2481", "line": 58, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 58, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2105", "line": 60, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 60, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2106", "line": 66, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 66, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2107", "line": 67, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 67, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2108", "line": 73, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 73, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2617", "line": 75, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 75, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "1958", "line": 78, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 78, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "1959", "line": 79, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 79, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "1957", "line": 80, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2618", "line": 81, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2619", "line": 82, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 82, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2620", "line": 83, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2121", "line": 85, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 85, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2119", "line": 87, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2123", "line": 89, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 89, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2621", "line": 90, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 90, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2116", "line": 92, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 92, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 99, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 99, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2212", "line": 99, "column": 22, "nodeType": "1835", "messageId": "1836", "endLine": 99, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 100, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 100, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2214", "line": 100, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 100, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2622", "line": 101, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 101, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2623", "line": 102, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 102, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2624", "line": 103, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2625", "line": 103, "column": 14, "nodeType": "1835", "messageId": "1836", "endLine": 103, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2117", "line": 104, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 104, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2626", "line": 104, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 104, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2297", "line": 105, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 105, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2627", "line": 105, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 105, "endColumn": 38}, {"ruleId": "1833", "severity": 1, "message": "2510", "line": 116, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 116, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2628", "line": 116, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 116, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2629", "line": 123, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 123, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2630", "line": 123, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 123, "endColumn": 44}, {"ruleId": "1844", "severity": 1, "message": "2631", "line": 146, "column": 5, "nodeType": "1846", "endLine": 146, "endColumn": 60, "suggestions": "2632"}, {"ruleId": "1833", "severity": 1, "message": "2633", "line": 149, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 149, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2634", "line": 161, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 161, "endColumn": 24}, {"ruleId": "1844", "severity": 1, "message": "2635", "line": 165, "column": 5, "nodeType": "1846", "endLine": 165, "endColumn": 60, "suggestions": "2636"}, {"ruleId": "1833", "severity": 1, "message": "2637", "line": 167, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 167, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2124", "line": 201, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 201, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2125", "line": 205, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 205, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 79}, {"ruleId": "1833", "severity": 1, "message": "1893", "line": 15, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 9}, {"ruleId": "1833", "severity": 1, "message": "2638", "line": 18, "column": 48, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 76}, {"ruleId": "1833", "severity": 1, "message": "2639", "line": 18, "column": 78, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 85}, {"ruleId": "1833", "severity": 1, "message": "2203", "line": 20, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2640", "line": 50, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2641", "line": 52, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 52, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2642", "line": 57, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2576", "line": 57, "column": 18, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2643", "line": 58, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 58, "endColumn": 46}, {"ruleId": "1833", "severity": 1, "message": "1928", "line": 59, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 59, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "1929", "line": 59, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 59, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2637", "line": 83, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2432", "line": 90, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 90, "endColumn": 25}, {"ruleId": "1844", "severity": 1, "message": "2644", "line": 181, "column": 5, "nodeType": "1846", "endLine": 181, "endColumn": 52, "suggestions": "2645"}, {"ruleId": "1844", "severity": 1, "message": "1998", "line": 181, "column": 6, "nodeType": "2047", "endLine": 181, "endColumn": 51}, {"ruleId": "1833", "severity": 1, "message": "2320", "line": 11, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2269", "line": 14, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2646", "line": 67, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 67, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2647", "line": 180, "column": 86, "nodeType": "1835", "messageId": "1836", "endLine": 180, "endColumn": 101}, {"ruleId": "1833", "severity": 1, "message": "2338", "line": 184, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 184, "endColumn": 24}, {"ruleId": "1844", "severity": 1, "message": "2648", "line": 563, "column": 5, "nodeType": "1846", "endLine": 563, "endColumn": 40, "suggestions": "2649"}, {"ruleId": "1844", "severity": 1, "message": "2650", "line": 586, "column": 6, "nodeType": "1846", "endLine": 586, "endColumn": 42, "suggestions": "2651"}, {"ruleId": "1844", "severity": 1, "message": "2652", "line": 600, "column": 6, "nodeType": "1846", "endLine": 600, "endColumn": 50, "suggestions": "2653"}, {"ruleId": "1844", "severity": 1, "message": "2654", "line": 877, "column": 5, "nodeType": "1846", "endLine": 877, "endColumn": 160, "suggestions": "2655"}, {"ruleId": "1844", "severity": 1, "message": "2656", "line": 945, "column": 5, "nodeType": "1846", "endLine": 945, "endColumn": 110, "suggestions": "2657"}, {"ruleId": "1844", "severity": 1, "message": "2658", "line": 975, "column": 5, "nodeType": "1846", "endLine": 975, "endColumn": 34, "suggestions": "2659"}, {"ruleId": "1844", "severity": 1, "message": "2660", "line": 993, "column": 5, "nodeType": "1846", "endLine": 993, "endColumn": 34, "suggestions": "2661"}, {"ruleId": "1844", "severity": 1, "message": "2660", "line": 1007, "column": 5, "nodeType": "1846", "endLine": 1007, "endColumn": 34, "suggestions": "2662"}, {"ruleId": "1844", "severity": 1, "message": "2660", "line": 1010, "column": 5, "nodeType": "1846", "endLine": 1010, "endColumn": 40, "suggestions": "2663"}, {"ruleId": "1833", "severity": 1, "message": "2664", "line": 1220, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 1220, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2665", "line": 1223, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 1223, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 92, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 102}, {"ruleId": "1833", "severity": 1, "message": "2666", "line": 74, "column": 19, "nodeType": "1835", "messageId": "1836", "endLine": 74, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2366", "line": 1, "column": 29, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 37}, {"ruleId": "1833", "severity": 1, "message": "2360", "line": 1, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "2189", "line": 1, "column": 49, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 59}, {"ruleId": "1833", "severity": 1, "message": "2317", "line": 1, "column": 61, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 67}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2069", "line": 2, "column": 56, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 62}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2193", "line": 2, "column": 80, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 2, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2195", "line": 2, "column": 105, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 111}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 2, "column": 113, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 121}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 2, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2370", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2090", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 4, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 4, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 4, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2411", "line": 7, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2412", "line": 8, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2413", "line": 9, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2414", "line": 10, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 11, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2449", "line": 12, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2667", "line": 18, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "1875", "line": 2, "column": 64, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 78}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 2, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 2, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2097", "line": 2, "column": 177, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 193}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 4, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 4, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 4, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2411", "line": 7, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2412", "line": 8, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2413", "line": 9, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2414", "line": 10, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 11, "column": 5, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2416", "line": 27, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 27, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2417", "line": 28, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 28, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2418", "line": 29, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2419", "line": 30, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 30, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2422", "line": 35, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 35, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2423", "line": 36, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 36, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2424", "line": 38, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 38, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2454", "line": 39, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 39, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2455", "line": 40, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 40, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2668", "line": 41, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2456", "line": 42, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2458", "line": 44, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2459", "line": 45, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 45, "endColumn": 33}, {"ruleId": "1833", "severity": 1, "message": "2460", "line": 46, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2462", "line": 48, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2463", "line": 49, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 49, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2464", "line": 50, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2465", "line": 51, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2384", "line": 55, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2669", "line": 149, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 149, "endColumn": 40}, {"ruleId": "1833", "severity": 1, "message": "2670", "line": 150, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 150, "endColumn": 41}, {"ruleId": "1833", "severity": 1, "message": "2671", "line": 151, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 151, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "1928", "line": 153, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 153, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2672", "line": 182, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 182, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2430", "line": 217, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 217, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2431", "line": 220, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 220, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2432", "line": 225, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 225, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2032", "line": 242, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 242, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2468", "line": 246, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 246, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2474", "line": 251, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 251, "endColumn": 20}, {"ruleId": "1844", "severity": 1, "message": "2673", "line": 260, "column": 6, "nodeType": "1846", "endLine": 260, "endColumn": 8, "suggestions": "2674"}, {"ruleId": "1833", "severity": 1, "message": "2675", "line": 295, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 295, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2369", "line": 2, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2197", "line": 2, "column": 36, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 46}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 48, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 57}, {"ruleId": "1833", "severity": 1, "message": "2196", "line": 2, "column": 59, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 67}, {"ruleId": "1833", "severity": 1, "message": "2069", "line": 2, "column": 69, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 75}, {"ruleId": "1833", "severity": 1, "message": "2198", "line": 2, "column": 77, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 84}, {"ruleId": "1833", "severity": 1, "message": "2676", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2677", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2678", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2679", "line": 8, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 18}, {"ruleId": "2680", "severity": 1, "message": "2681", "line": 67, "column": 12, "nodeType": "2448", "endLine": 67, "endColumn": 104}, {"ruleId": "1833", "severity": 1, "message": "2402", "line": 1, "column": 17, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2317", "line": 1, "column": 50, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 56}, {"ruleId": "1833", "severity": 1, "message": "2682", "line": 1, "column": 70, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 81}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2194", "line": 2, "column": 93, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 103}, {"ruleId": "1833", "severity": 1, "message": "2403", "line": 2, "column": 123, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 140}, {"ruleId": "1833", "severity": 1, "message": "2094", "line": 2, "column": 142, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 158}, {"ruleId": "1833", "severity": 1, "message": "2404", "line": 2, "column": 160, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 166}, {"ruleId": "1833", "severity": 1, "message": "2318", "line": 2, "column": 195, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 212}, {"ruleId": "1833", "severity": 1, "message": "2407", "line": 4, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 49}, {"ruleId": "1833", "severity": 1, "message": "2408", "line": 4, "column": 51, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 71}, {"ruleId": "1833", "severity": 1, "message": "2409", "line": 4, "column": 73, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 91}, {"ruleId": "1833", "severity": 1, "message": "2410", "line": 5, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2415", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2416", "line": 8, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2417", "line": 9, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2418", "line": 10, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2419", "line": 11, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 11, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2420", "line": 12, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2411", "line": 14, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 14, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2412", "line": 15, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2413", "line": 16, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 16, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2414", "line": 17, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 17, "endColumn": 7}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 18, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 18, "endColumn": 10}, {"ruleId": "1833", "severity": 1, "message": "2677", "line": 33, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2683", "line": 40, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 40, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2422", "line": 41, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2423", "line": 42, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2424", "line": 44, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2454", "line": 45, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 45, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2455", "line": 46, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2668", "line": 47, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2456", "line": 48, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2458", "line": 50, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 50, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2459", "line": 51, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 32}, {"ruleId": "1833", "severity": 1, "message": "2460", "line": 52, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 52, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2462", "line": 54, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2463", "line": 55, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2464", "line": 56, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 56, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2465", "line": 57, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2684", "line": 59, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 59, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2384", "line": 62, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 62, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2669", "line": 85, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 85, "endColumn": 39}, {"ruleId": "1833", "severity": 1, "message": "2670", "line": 86, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 86, "endColumn": 40}, {"ruleId": "1833", "severity": 1, "message": "2671", "line": 87, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 87, "endColumn": 46}, {"ruleId": "1844", "severity": 1, "message": "2685", "line": 108, "column": 5, "nodeType": "1846", "endLine": 108, "endColumn": 52, "suggestions": "2686"}, {"ruleId": "1833", "severity": 1, "message": "2430", "line": 113, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 113, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2431", "line": 116, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 116, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2432", "line": 121, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 121, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2687", "line": 131, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 131, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2032", "line": 171, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 171, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2688", "line": 179, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 179, "endColumn": 20}, {"ruleId": "1844", "severity": 1, "message": "2673", "line": 184, "column": 5, "nodeType": "1846", "endLine": 184, "endColumn": 7, "suggestions": "2689"}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 189, "column": 21, "nodeType": "1994", "messageId": "1995", "endLine": 189, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 190, "column": 21, "nodeType": "1994", "messageId": "1995", "endLine": 190, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 190, "column": 41, "nodeType": "1994", "messageId": "1995", "endLine": 190, "endColumn": 43}, {"ruleId": "1992", "severity": 1, "message": "1993", "line": 209, "column": 21, "nodeType": "1994", "messageId": "1995", "endLine": 209, "endColumn": 23}, {"ruleId": "1992", "severity": 1, "message": "2014", "line": 222, "column": 22, "nodeType": "1994", "messageId": "1995", "endLine": 222, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2690", "line": 275, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 275, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "1928", "line": 303, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 303, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2672", "line": 330, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 330, "endColumn": 24}, {"ruleId": "1844", "severity": 1, "message": "2691", "line": 367, "column": 5, "nodeType": "1846", "endLine": 367, "endColumn": 7, "suggestions": "2692"}, {"ruleId": "1833", "severity": 1, "message": "2678", "line": 5, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2679", "line": 6, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2693", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2694", "line": 26, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 26, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2695", "line": 33, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 33, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2391", "line": 54, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 54, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2392", "line": 56, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 56, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2696", "line": 78, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 78, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2697", "line": 80, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2698", "line": 80, "column": 23, "nodeType": "1835", "messageId": "1836", "endLine": 80, "endColumn": 38}, {"ruleId": "1833", "severity": 1, "message": "2699", "line": 131, "column": 7, "nodeType": "1835", "messageId": "1836", "endLine": 131, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2700", "line": 288, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 288, "endColumn": 28}, {"ruleId": "1844", "severity": 1, "message": "2701", "line": 342, "column": 5, "nodeType": "1846", "endLine": 342, "endColumn": 22, "suggestions": "2702"}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 32, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 44, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 53}, {"ruleId": "1833", "severity": 1, "message": "2479", "line": 4, "column": 46, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 65}, {"ruleId": "1833", "severity": 1, "message": "1873", "line": 4, "column": 67, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 75}, {"ruleId": "1833", "severity": 1, "message": "2703", "line": 8, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2704", "line": 29, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 29, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2227", "line": 31, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 31, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2705", "line": 41, "column": 6, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 34}, {"ruleId": "1833", "severity": 1, "message": "2706", "line": 83, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 83, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2500", "line": 93, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 93, "endColumn": 26}, {"ruleId": "1833", "severity": 1, "message": "2562", "line": 1, "column": 58, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 67}, {"ruleId": "1833", "severity": 1, "message": "1849", "line": 1, "column": 75, "nodeType": "1835", "messageId": "1836", "endLine": 1, "endColumn": 82}, {"ruleId": "1833", "severity": 1, "message": "2369", "line": 2, "column": 15, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2134", "line": 2, "column": 33, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 43}, {"ruleId": "1833", "severity": 1, "message": "2570", "line": 4, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 4, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2091", "line": 5, "column": 41, "nodeType": "1835", "messageId": "1836", "endLine": 5, "endColumn": 53}, {"ruleId": "1833", "severity": 1, "message": "2568", "line": 6, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2100", "line": 6, "column": 16, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 22}, {"ruleId": "1833", "severity": 1, "message": "2707", "line": 6, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 29}, {"ruleId": "1833", "severity": 1, "message": "2708", "line": 6, "column": 31, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2249", "line": 6, "column": 37, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 47}, {"ruleId": "1833", "severity": 1, "message": "2569", "line": 6, "column": 49, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 61}, {"ruleId": "1833", "severity": 1, "message": "2370", "line": 7, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 7, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2677", "line": 8, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 8, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2709", "line": 36, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 36, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2710", "line": 37, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 37, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2711", "line": 39, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 39, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2256", "line": 40, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 40, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2712", "line": 41, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2255", "line": 42, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 16}, {"ruleId": "1833", "severity": 1, "message": "2254", "line": 43, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 43, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2713", "line": 44, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2252", "line": 46, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 19}, {"ruleId": "1833", "severity": 1, "message": "2714", "line": 47, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2715", "line": 48, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 48, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2327", "line": 51, "column": 4, "nodeType": "1835", "messageId": "1836", "endLine": 51, "endColumn": 11}, {"ruleId": "1833", "severity": 1, "message": "2179", "line": 55, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2180", "line": 55, "column": 20, "nodeType": "1835", "messageId": "1836", "endLine": 55, "endColumn": 31}, {"ruleId": "1833", "severity": 1, "message": "2716", "line": 57, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2717", "line": 57, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 57, "endColumn": 43}, {"ruleId": "1833", "severity": 1, "message": "2718", "line": 73, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 73, "endColumn": 30}, {"ruleId": "1833", "severity": 1, "message": "2719", "line": 81, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 81, "endColumn": 29}, {"ruleId": "1844", "severity": 1, "message": "2720", "line": 114, "column": 6, "nodeType": "1846", "endLine": 114, "endColumn": 35, "suggestions": "2721"}, {"ruleId": "1833", "severity": 1, "message": "2722", "line": 3, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 3, "endColumn": 18}, {"ruleId": "1833", "severity": 1, "message": "2415", "line": 6, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 6, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2140", "line": 2, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2475", "line": 2, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "2191", "line": 2, "column": 38, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 42}, {"ruleId": "1833", "severity": 1, "message": "2213", "line": 9, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2212", "line": 9, "column": 22, "nodeType": "1835", "messageId": "1836", "endLine": 9, "endColumn": 36}, {"ruleId": "1833", "severity": 1, "message": "1948", "line": 10, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 24}, {"ruleId": "1833", "severity": 1, "message": "2214", "line": 10, "column": 26, "nodeType": "1835", "messageId": "1836", "endLine": 10, "endColumn": 44}, {"ruleId": "1833", "severity": 1, "message": "2623", "line": 12, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2643", "line": 12, "column": 27, "nodeType": "1835", "messageId": "1836", "endLine": 12, "endColumn": 46}, {"ruleId": "1833", "severity": 1, "message": "2624", "line": 13, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 12}, {"ruleId": "1833", "severity": 1, "message": "2625", "line": 13, "column": 14, "nodeType": "1835", "messageId": "1836", "endLine": 13, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2297", "line": 15, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 15, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2510", "line": 16, "column": 9, "nodeType": "1835", "messageId": "1836", "endLine": 16, "endColumn": 15}, {"ruleId": "1833", "severity": 1, "message": "2723", "line": 28, "column": 8, "nodeType": "1835", "messageId": "1836", "endLine": 28, "endColumn": 25}, {"ruleId": "1833", "severity": 1, "message": "2096", "line": 2, "column": 2, "nodeType": "1835", "messageId": "1836", "endLine": 2, "endColumn": 14}, {"ruleId": "1833", "severity": 1, "message": "2724", "line": 20, "column": 24, "nodeType": "1835", "messageId": "1836", "endLine": 20, "endColumn": 35}, {"ruleId": "1833", "severity": 1, "message": "2224", "line": 40, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 40, "endColumn": 21}, {"ruleId": "1833", "severity": 1, "message": "2322", "line": 41, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 41, "endColumn": 23}, {"ruleId": "1833", "severity": 1, "message": "2112", "line": 42, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 42, "endColumn": 28}, {"ruleId": "1833", "severity": 1, "message": "2725", "line": 44, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 44, "endColumn": 13}, {"ruleId": "1833", "severity": 1, "message": "2182", "line": 46, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 46, "endColumn": 17}, {"ruleId": "1833", "severity": 1, "message": "2227", "line": 47, "column": 3, "nodeType": "1835", "messageId": "1836", "endLine": 47, "endColumn": 20}, {"ruleId": "1833", "severity": 1, "message": "2208", "line": 58, "column": 10, "nodeType": "1835", "messageId": "1836", "endLine": 58, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2209", "line": 58, "column": 45, "nodeType": "1835", "messageId": "1836", "endLine": 58, "endColumn": 62}, {"ruleId": "1833", "severity": 1, "message": "2726", "line": 98, "column": 11, "nodeType": "1835", "messageId": "1836", "endLine": 98, "endColumn": 27}, {"ruleId": "1833", "severity": 1, "message": "2727", "line": 101, "column": 12, "nodeType": "1835", "messageId": "1836", "endLine": 101, "endColumn": 26}, {"ruleId": "1844", "severity": 1, "message": "2396", "line": 126, "column": 5, "nodeType": "1846", "endLine": 126, "endColumn": 155, "suggestions": "2728"}, "@typescript-eslint/no-unused-vars", "'logo' is defined but never used.", "Identifier", "unusedVar", "'GuidePopup' is defined but never used.", "'Rte' is defined but never used.", "'LoginUserInfo' is defined but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", "ArrayExpression", ["2729"], "'signIn' is assigned a value but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", ["2730"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2731"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2732"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "The 'initialState' object makes the dependencies of useEffect Hook (at line 1041) change on every render. To fix this, wrap the initialization of 'initialState' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2733"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2734"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2735"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'isShowIcon', 'resetALTKeywordForNewTooltip', 'setElementSelected', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2736"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2737"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2738"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "'response' is assigned a value but never used.", "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2739"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2740"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2741"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2742"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2743"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCurrentGuideId', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2744"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2745", "2746"], "'selectedStepTitle' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2747"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2748"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2749"], "'Typography' is defined but never used.", "'useAuth' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'Box' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2750"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2751"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2752"], "'handleEnableAI' is assigned a value but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 123) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 187) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2753"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'useContext' is defined but never used.", "'AccountContext' is defined but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2754"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2755"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2756"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2757"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2758"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2759"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2760"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2761"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2762"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2763"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2764"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2765"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2766"], ["2767"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2768"], "React Hook useEffect has missing dependencies: 'isALTKeywordEnabled', 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["2769"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2770"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2771"], ["2772"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2773"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2774"], ["2775"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2776"], "'toggleItemCompletion' is assigned a value but never used.", "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2777"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2778"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", "'accountId' is assigned a value but never used.", ["2779"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "React Hook useMemo has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2780"], "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2781"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2782"], "'OverlaySettingsProps' is defined but never used.", "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2783"], "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2784"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2785"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2786"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2787"], "'handlePositionClick' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2788"], "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2789"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2790"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2791"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2792"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2793"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2794"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2795"], ["2796"], ["2797"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'guideStatus' is assigned a value but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2798"], "'handleMenuScroll' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2799"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2800"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2801"], "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2802"], "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'handleTooltipRTEValue' is assigned a value but never used.", "'anchorPosition' is assigned a value but never used.", "'setAnchorPosition' is assigned a value but never used.", "'preserveCaretPosition' is assigned a value but never used.", "'restoreCaretPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'boxRef' and 'textvaluess'. Either include them or remove the dependency array. Mutable values like 'boxRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["2803"], "'FolderIcon' is defined but never used.", "'handleColorChange' is assigned a value but never used.", "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2804"], {"desc": "2805", "fix": "2806"}, {"desc": "2807", "fix": "2808"}, {"desc": "2809", "fix": "2810"}, {"desc": "2811", "fix": "2812"}, {"desc": "2813", "fix": "2814"}, {"desc": "2815", "fix": "2816"}, {"desc": "2817", "fix": "2818"}, {"desc": "2819", "fix": "2820"}, {"desc": "2821", "fix": "2822"}, {"desc": "2823", "fix": "2824"}, {"desc": "2825", "fix": "2826"}, {"desc": "2827", "fix": "2828"}, {"desc": "2829", "fix": "2830"}, {"desc": "2831", "fix": "2832"}, {"desc": "2833", "fix": "2834"}, {"desc": "2835", "fix": "2836"}, {"messageId": "2837", "fix": "2838", "desc": "2839"}, {"messageId": "2840", "fix": "2841", "desc": "2842"}, {"desc": "2843", "fix": "2844"}, {"desc": "2845", "fix": "2846"}, {"desc": "2847", "fix": "2848"}, {"desc": "2849", "fix": "2850"}, {"desc": "2851", "fix": "2852"}, {"desc": "2853", "fix": "2854"}, {"desc": "2855", "fix": "2856"}, {"desc": "2857", "fix": "2858"}, {"desc": "2859", "fix": "2860"}, {"desc": "2861", "fix": "2862"}, {"desc": "2863", "fix": "2864"}, {"desc": "2865", "fix": "2866"}, {"desc": "2867", "fix": "2868"}, {"desc": "2869", "fix": "2870"}, {"messageId": "2871", "data": "2872", "fix": "2873", "desc": "2874"}, {"desc": "2875", "fix": "2876"}, {"desc": "2877", "fix": "2878"}, {"desc": "2879", "fix": "2880"}, {"desc": "2881", "fix": "2882"}, {"desc": "2883", "fix": "2884"}, {"desc": "2885", "fix": "2886"}, {"desc": "2887", "fix": "2888"}, {"desc": "2889", "fix": "2890"}, {"desc": "2891", "fix": "2892"}, {"desc": "2893", "fix": "2894"}, {"desc": "2895", "fix": "2896"}, {"desc": "2897", "fix": "2898"}, {"desc": "2899", "fix": "2900"}, {"desc": "2855", "fix": "2901"}, {"desc": "2902", "fix": "2903"}, {"desc": "2904", "fix": "2905"}, {"desc": "2906", "fix": "2907"}, {"desc": "2902", "fix": "2908"}, {"desc": "2909", "fix": "2910"}, {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"desc": "2935", "fix": "2936"}, {"desc": "2937", "fix": "2938"}, {"desc": "2939", "fix": "2940"}, {"desc": "2939", "fix": "2941"}, {"desc": "2942", "fix": "2943"}, {"desc": "2944", "fix": "2945"}, {"desc": "2946", "fix": "2947"}, {"desc": "2944", "fix": "2948"}, {"desc": "2949", "fix": "2950"}, {"desc": "2951", "fix": "2952"}, {"desc": "2953", "fix": "2954"}, {"desc": "2955", "fix": "2956"}, "Update the dependencies array to be: [loggedOut]", {"range": "2957", "text": "2958"}, "Update the dependencies array to be: []", {"range": "2959", "text": "2960"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "2961", "text": "2962"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "2963", "text": "2964"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "2965", "text": "2966"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "2967", "text": "2968"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "2969", "text": "2970"}, "Update the dependencies array to be: [currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", {"range": "2971", "text": "2972"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "2973", "text": "2974"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "2975", "text": "2976"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "2977", "text": "2978"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "2979", "text": "2980"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "2981", "text": "2982"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "2983", "text": "2984"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "2985", "text": "2986"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "2987", "text": "2988"}, "removeEscape", {"range": "2989", "text": "2990"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2991", "text": "2992"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "2993", "text": "2994"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "2995", "text": "2996"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "2997", "text": "2998"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "2999", "text": "3000"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3001", "text": "3002"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3003", "text": "3004"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3005", "text": "3006"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3007", "text": "3008"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3009", "text": "3010"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3011", "text": "3012"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3013", "text": "3014"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3015", "text": "3016"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3017", "text": "3018"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3019", "text": "3020"}, "suggestString", {"type": "3021"}, {"range": "3022", "text": "3023"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3024", "text": "3025"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3026", "text": "3027"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3028", "text": "3029"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3030", "text": "3031"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3032", "text": "3033"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3034", "text": "3035"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", {"range": "3036", "text": "3037"}, "Update the dependencies array to be: [elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3038", "text": "3039"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3040", "text": "3041"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3042", "text": "3043"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3044", "text": "3045"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3046", "text": "3047"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3048", "text": "3049"}, {"range": "3050", "text": "3006"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3051", "text": "3052"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3053", "text": "3054"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3055", "text": "3056"}, {"range": "3057", "text": "3052"}, "Update the dependencies array to be: [handlePaste]", {"range": "3058", "text": "3059"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3060", "text": "3061"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3062", "text": "3063"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3064", "text": "3065"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3066", "text": "3067"}, "Update the dependencies array to be: [handleFocus]", {"range": "3068", "text": "3069"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3070", "text": "3071"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3072", "text": "3073"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3074", "text": "3075"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3076", "text": "3077"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3078", "text": "3079"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3080", "text": "3081"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3082", "text": "3083"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3084", "text": "3085"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3086", "text": "3087"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3088", "text": "3089"}, {"range": "3090", "text": "3089"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3091", "text": "3092"}, "Update the dependencies array to be: [fetchData]", {"range": "3093", "text": "3094"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3095", "text": "3096"}, {"range": "3097", "text": "3094"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3098", "text": "3099"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3100", "text": "3101"}, "Update the dependencies array to be: [rteBoxValue, boxRef, textvaluess]", {"range": "3102", "text": "3103"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3104", "text": "3105"}, [4291, 4293], "[loggedOut]", [17728, 17750], "[]", [26039, 26080], "[fetchGuideDetails, hotspot, hotspotClicked]", [26562, 26575], "[designPopup, setDesignPopup]", [29299, 29446], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [29924, 30284], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [34928, 34962], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [69969, 69982], "[currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", [85997, 86030], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [100629, 100643], "[createWithAI, setIsUnSavedChanges, stepCreation]", [128664, 128816], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [158438, 158495], "[currentGuide?.GuideStep, currentStep]", [163752, 163769], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [169701, 169734], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [170444, 170545], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [174622, 174634], "[isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [194990, 194991], "", [194990, 194990], "\\", [15982, 16037], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17411, 17466], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [17911, 17913], "[selectedActions.value, targetURL]", [2146, 2148], "[isExtensionClosed, setIsExtensionClosed]", [2811, 2834], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3178, 3221], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [4880, 4913], "[checkpointslistData]", [5176, 5216], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10100, 10135], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6060, 6062], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7125, 7147], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7308, 7319], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8870, 8935], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [22775, 22870], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", [26738, 26755], "[elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8410, 8455], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [8761, 8774], "[fetchAnnouncements, searchQuery]", [3407, 3409], "[checkpointslistData, completedStatus]", [4251, 4277], "[selectedItem, activeItem, createWithAI, interactionData]", [4381, 4415], [5371, 5398], "[checklistGuideMetaData]", [4786, 4788], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9542, 9544], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3075, 3115], [13207, 13209], "[handlePaste]", [2557, 2559], "[setButtonProperty]", [6784, 6809], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [16839, 16841], "[setElementSelected]", [9674, 9687], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [25889, 25891], "[handleFocus]", [4537, 4592], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [5674, 5729], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [7408, 7455], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7546, 7548], "[fetchData]", [4008, 4055], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6214, 6216], [12176, 12178], "[checklistCheckpointListProperties, icons]", [10031, 10048], "[setImageAnchorEl, tooltip.visible]", [3714, 3743], "[rteBoxV<PERSON>ue, boxRef, textvaluess]", [4152, 4302], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]"]