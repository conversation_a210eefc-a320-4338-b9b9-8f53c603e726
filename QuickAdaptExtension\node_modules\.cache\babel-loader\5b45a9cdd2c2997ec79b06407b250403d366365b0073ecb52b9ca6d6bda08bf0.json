{"ast": null, "code": "import React,{useState,useRef,useEffect}from\"react\";import{IconButton,Box,Typography,MobileStepper,Button,LinearProgress}from\"@mui/material\";import AddIcon from\"@mui/icons-material/Add\";import DeleteOutlineOutlinedIcon from\"@mui/icons-material/DeleteOutlineOutlined\";import{Link}from\"@mui/icons-material\";import RTEsection from\"../guideSetting/PopupSections/RTEsection\";import ImageSectionField from\"./selectedpopupfields/ImageSectionField\";import ButtonSection from\"../guideSetting/PopupSections/Button\";import HtmlSection from\"../guideSetting/PopupSections/HtmlSection\";import EmojiPicker from\"emoji-picker-react\";import\"./guideBanner.css\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import ButtonSettings from\"./selectedpopupfields/ButtonSettings\";import Tooltip from\"@mui/material/Tooltip\";import AlertPopup from\"../drawer/AlertPopup\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Banner=_ref=>{var _savedGuideData$Guide,_savedGuideData$Guide2,_savedGuideData$Guide3,_bannerJson$GuideStep;let{setImageSrc,imageSrc,textBoxRef,setHtmlContent,htmlContent,buttonColor,setButtonColor,setImageName,imageName,alignment,setAlignment,textvalue,setTextvalue,isBanner,overlays,setOverLays,Bposition,setBposition,bpadding,setbPadding,isUnSavedChanges,openWarning,setopenWarning,handleLeave,savedGuideData,Progress}=_ref;const guidePopUpRef=useRef(null);const{dismissData,sectionColor,setSectionColor,buttonProperty,setButtonProperty,BborderSize,Bbordercolor,backgroundC,textArray,setBannerButtonSelected,setTextArray,preview,setPreview,bannerButtonSelected,clearGuideDetails,btnBgColor,btnTextColor,btnBorderColor,buttonsContainer,setButtonsContainer,clearBannerButtonDetials,setRTEAnchorEl,deleteRTEContainer,rteAnchorEl,bannerJson,currentStep,selectedOption,ProgressColor,setProgressColor,steps,progress,createWithAI,selectedTemplate,syncAIAnnouncementDataForPreview,rtesContainer}=useDrawerStore(state=>state);const[textAreas,setTextAreas]=useState([[{name:\"Rich Text\",value:/*#__PURE__*/_jsx(RTEsection,{textBoxRef:textBoxRef,isBanner:true,index:0,handleDeleteRTESection:()=>{}// @ts-ignore\n,ref:textBoxRef,guidePopUpRef:guidePopUpRef},0)}]]);// Effect to restore textAreas state from persisted store data on component mount\nuseEffect(()=>{// Only run this restoration logic once on component mount\nconst restoreTextAreasFromStore=()=>{const rowIndex=0;let restoredTextAreas=[...textAreas];let hasChanges=false;// Check if we need to restore button state from the store\nif(bannerButtonSelected&&!textAreas[rowIndex].some(item=>item.name===\"Button\")){const newButton={name:\"Button\",value:/*#__PURE__*/_jsx(ButtonSection,{buttonColor:buttonColor,setButtonColor:setButtonColor,isBanner:true},0)};restoredTextAreas[rowIndex]=[...restoredTextAreas[rowIndex],newButton];hasChanges=true;}// Check if RTE content exists in the store and needs to be restored\n// For banners, RTE content is stored in rtesContainer\nconst hasRTEContent=rtesContainer&&rtesContainer.length>0&&rtesContainer[0].rtes&&rtesContainer[0].rtes.length>0&&rtesContainer[0].rtes[0].text&&rtesContainer[0].rtes[0].text.trim()!==\"\";// If there's RTE content in the store but the banner is not visible,\n// it means we need to restore the textAreas properly\nif(hasRTEContent){// console.log(\"Banner: Restoring RTE content from store\", {\n// \trteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + \"...\",\n// \tcurrentTextAreas: textAreas.length\n// });\n// Ensure the RTE section is properly initialized with the stored content\n// The RTEsection component will automatically pick up the content from rtesContainer\n// We just need to make sure the textAreas structure is correct\nconst hasRTEInTextAreas=restoredTextAreas[rowIndex].some(item=>item.name===\"Rich Text\");if(!hasRTEInTextAreas){// This shouldn't happen normally, but if it does, recreate the RTE section\nconst rteSection={name:\"Rich Text\",value:/*#__PURE__*/_jsx(RTEsection,{textBoxRef:textBoxRef,isBanner:true,index:0,handleDeleteRTESection:()=>{}// @ts-ignore\n,ref:textBoxRef,guidePopUpRef:guidePopUpRef},0)};restoredTextAreas[rowIndex]=[rteSection,...restoredTextAreas[rowIndex].filter(item=>item.name!==\"Rich Text\")];hasChanges=true;}}// Update textAreas if changes were made\nif(hasChanges){setTextAreas(restoredTextAreas);}};// Run restoration logic after a small delay to ensure store is fully loaded\nconst timeoutId=setTimeout(restoreTextAreasFromStore,100);return()=>clearTimeout(timeoutId);},[]);// Empty dependency array - only run once on mount\n// UseEffect to update textAreas when setBannerButtonSelected changes\nuseEffect(()=>{const rowIndex=0;// Assuming we want to update the first row\nconst existingRow=textAreas[rowIndex];if(bannerButtonSelected){// Check if Button already exists in the first row\n// If Button is not already in the row, add it\nif(!existingRow.some(item=>item.name===\"Button\")){const newButton={name:\"Button\",value:/*#__PURE__*/_jsx(ButtonSection,{buttonColor:buttonColor,setButtonColor:setButtonColor,isBanner:true},0)};const updatedTextAreas=[...textAreas];updatedTextAreas[rowIndex]=[...existingRow,newButton];setTextAreas(updatedTextAreas);}}else{// Find the button index and remove it\nconst buttonIndex=existingRow.findIndex(item=>item.name===\"Button\");if(buttonIndex!==-1){removeTextArea(rowIndex,buttonIndex);}}},[bannerButtonSelected]);// Trigger when setBannerButtonSelected changes\n// Update textArray whenever textAreas changes\nuseEffect(()=>{setTextArray(textAreas);},[textAreas]);// Sync AI announcement data on component mount to ensure progress bar is visible\nuseEffect(()=>{// Only run this for AI-created announcements\nif(!createWithAI||selectedTemplate!==\"Announcement\")return;console.log(\"Banner component mounted for AI announcement - syncing data\");// Synchronize AI announcement data to ensure progress bar and other settings are properly initialized\nsyncAIAnnouncementDataForPreview(true);// Preserve global state during banner initialization\n},[createWithAI,selectedTemplate,syncAIAnnouncementDataForPreview]);// Sync textAreas with AI-created guide data on mount and when relevant data changes\nuseEffect(()=>{// Only run this for AI-created guides\nif(!createWithAI)return;const rowIndex=0;const existingRow=textAreas[rowIndex];// Check if we need to add a button based on the AI guide data\nconst hasButtonInData=bannerButtonSelected;const hasButtonInTextAreas=existingRow.some(item=>item.name===\"Button\");if(hasButtonInData&&!hasButtonInTextAreas){// Add button to textAreas if it exists in AI data but not in local state\nconst newButton={name:\"Button\",value:/*#__PURE__*/_jsx(ButtonSection,{buttonColor:buttonColor,setButtonColor:setButtonColor,isBanner:true},0)};const updatedTextAreas=[...textAreas];updatedTextAreas[rowIndex]=[...existingRow,newButton];setTextAreas(updatedTextAreas);}},[createWithAI,bannerButtonSelected,buttonColor,setButtonColor]);// Dependencies for AI guide sync\n// Additional effect to ensure button state is preserved for AI guides\nuseEffect(()=>{if(!createWithAI)return;// Check if we have button data in the store but not in textAreas\nconst rowIndex=0;const existingRow=textAreas[rowIndex];const hasButtonInTextAreas=existingRow.some(item=>item.name===\"Button\");// If buttonsContainer has buttons but textAreas doesn't, restore the button\nif(buttonsContainer.length>0&&!hasButtonInTextAreas&&!bannerButtonSelected){setBannerButtonSelected(true);}},[createWithAI,buttonsContainer,textAreas,bannerButtonSelected,setBannerButtonSelected]);// Monitor button container changes\n// Handle overflow behavior based on banner position in creation mode\nuseEffect(()=>{// Use a small delay to ensure this runs after resetHeightofBanner\nconst timeoutId=setTimeout(()=>{// Only apply overflow hidden in creation/edit mode when position is \"Cover Top\"\n// This component is only rendered in creation mode (when !showBannerenduser)\n// Preview mode is handled by separate preview components\nif(Bposition===\"Cover Top\"){document.body.style.setProperty(\"overflow\",\"hidden\",\"important\");}else{// Restore normal scrolling for other positions\ndocument.body.style.removeProperty(\"overflow\");}},100);// Small delay to ensure it runs after resetHeightofBanner\n// Cleanup function to restore overflow when component unmounts\nreturn()=>{clearTimeout(timeoutId);document.body.style.removeProperty(\"overflow\");};},[Bposition]);// Re-run when position changes\nconst overlayEnabled=useDrawerStore(state=>state.overlayEnabled);const[showOptions,setShowOptions]=useState(false);const[focusedRowIndex,setFocusedRowIndex]=useState(null);const[showEmojiPicker,setShowEmojiPicker]=useState(false);const handleDeleteRTESection=index=>{const newTextAreas=[...textAreas];newTextAreas.splice(index,1);setTextAreas(newTextAreas);setTextArray(newTextAreas);};const addTextAreaInSameRow=(rowIndex,option)=>{if(!textAreas[rowIndex]){textAreas[rowIndex]=[];}const existingRow=textAreas[rowIndex];// Check if a Rich Text or Button already exists in the row\nif(option===\"richText\"&&existingRow.some(item=>item.name===\"Rich Text\")||option===\"button\"&&existingRow.some(item=>item.name===\"Button\")){alert(`Only one ${option===\"richText\"?\"Rich Text\":\"Button\"} is allowed per row.`);return;}let newValue;let newName=\"\";switch(option){case\"image\":newValue=/*#__PURE__*/_jsx(ImageSectionField,{setImageSrc:setImageSrc,imageSrc:imageSrc,setImageName:setImageName},rowIndex);newName=\"Image\";break;case\"richText\":// setBannerButtonSelected(false);\nnewValue=/*#__PURE__*/_jsx(RTEsection,{textBoxRef:textBoxRef,isBanner:true,index:rowIndex,handleDeleteRTESection:handleDeleteRTESection// @ts-ignore\n,ref:textBoxRef,guidePopUpRef:guidePopUpRef},rowIndex);newName=\"Rich Text\";break;case\"button\":setBannerButtonSelected(true);// CRITICAL FIX: Ensure button container exists when adding button through banner editor\n// This creates the actual button data that ButtonSection component needs to display buttons\nif(buttonsContainer.length===0){const newButtonContainer={id:crypto.randomUUID(),buttons:[{id:crypto.randomUUID(),name:\"Got It\",position:\"center\",type:\"primary\",isEditing:false,index:0,style:{backgroundColor:\"#5F9EA0\",borderColor:\"#70afaf\",color:\"#ffffff\"},actions:{value:\"close\",targetURL:\"\",tab:\"same-tab\",interaction:null},survey:null}],style:{backgroundColor:\"transparent\"}};// Add the button container to the store\nsetButtonsContainer([newButtonContainer]);console.log(\"🔄 Banner editor: Created new button container for banner tour\",{containerId:newButtonContainer.id,buttonCount:newButtonContainer.buttons.length});}newValue=/*#__PURE__*/_jsx(ButtonSection,{buttonColor:buttonColor,setButtonColor:setButtonColor,isBanner:true},rowIndex);newName=\"Button\";break;case\"html\":newValue=/*#__PURE__*/_jsx(\"div\",{className:\"htmlbanner\",children:/*#__PURE__*/_jsx(HtmlSection,{htmlContent:htmlContent,setHtmlContent:setHtmlContent,isBanner:true},rowIndex)});newName=\"HTML\";break;default:newValue=\"\";newName=\"Text\";}const newTextAreas=[...textAreas];newTextAreas[rowIndex]=[...existingRow,{name:newName,value:newValue}];setTextAreas(newTextAreas);setTextArray(newTextAreas);};const removeTextArea=(rowIndex,textAreaIndex)=>{const updatedTextAreas=textAreas.map((row,index)=>{if(index===rowIndex){const filteredRow=row.filter((_,idx)=>idx!==textAreaIndex);// Check if there are any buttons remaining in the row after removal\nconst hasButtonInRow=filteredRow.some(t=>t.name===\"Button\");setBannerButtonSelected(hasButtonInRow);return filteredRow;}return row;});if(textAreaIndex===1){clearBannerButtonDetials();}else if(textAreaIndex===0){setRTEAnchorEl({rteId:\"\",containerId:\"\",// @ts-ignore\nvalue:null});deleteRTEContainer(rteAnchorEl.containerId);}setTextAreas(updatedTextAreas);setTextArray(updatedTextAreas);};const enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[0])===null||_savedGuideData$Guide2===void 0?void 0:(_savedGuideData$Guide3=_savedGuideData$Guide2.Tooltip)===null||_savedGuideData$Guide3===void 0?void 0:_savedGuideData$Guide3.EnableProgress)||false;function getProgressTemplate(selectedOption){var _savedGuideData$Guide4,_savedGuideData$Guide5,_savedGuideData$Guide6;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide4=savedGuideData.GuideStep)===null||_savedGuideData$Guide4===void 0?void 0:(_savedGuideData$Guide5=_savedGuideData$Guide4[0])===null||_savedGuideData$Guide5===void 0?void 0:(_savedGuideData$Guide6=_savedGuideData$Guide5.Tooltip)===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{var _savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9;// Check both the global progress state and the saved guide data for progress settings\nconst enableProgressFromData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[0])===null||_savedGuideData$Guide8===void 0?void 0:(_savedGuideData$Guide9=_savedGuideData$Guide8.Tooltip)===null||_savedGuideData$Guide9===void 0?void 0:_savedGuideData$Guide9.EnableProgress;const shouldShowProgress=progress||enableProgressFromData;if(!shouldShowProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:steps.length,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",padding:\"8px 0 0 0 !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{padding:\"8px 0 0 0 !important\",display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\"},children:Array.from({length:steps.length}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'14px',height:'4px',backgroundColor:index===currentStep-1?ProgressColor:'#e0e0e0',// Active color and inactive color\nborderRadius:'100px'}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{padding:\"8px 0 0 0 !important\",display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{fontSize:\"12px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",steps.length]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{sx:{width:\"calc(50% - 410px)\",padding:\"8px 0 0 0\"},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:Progress,sx:{'& .MuiLinearProgress-bar':{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};const style=(_bannerJson$GuideStep=bannerJson.GuideStep.find(step=>step.stepName===currentStep))===null||_bannerJson$GuideStep===void 0?void 0:_bannerJson$GuideStep.Canvas;// Apply overflow hidden to body when canvas position is \"Cover Top\" in creation mode\nuseEffect(()=>{if((style===null||style===void 0?void 0:style.Position)===\"Cover Top\"){document.body.style.overflow=\"hidden\";}else{document.body.style.overflow=\"\";}// Cleanup function to restore overflow when component unmounts\nreturn()=>{document.body.style.overflow=\"\";};},[style===null||style===void 0?void 0:style.Position]);// Re-run when canvas position changes\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-container creation\",children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-box\",sx:{padding:(style===null||style===void 0?void 0:style.Padding)!==undefined&&(style===null||style===void 0?void 0:style.Padding)!==null?`${style.Padding}px`:\"10px\",boxShadow:style&&(style===null||style===void 0?void 0:style.Position)===\"Push Down\"?\"none\":\"0px 1px 15px rgba(0, 0, 0, 0.7)\",borderTop:`${(style===null||style===void 0?void 0:style.BorderSize)||2}px solid ${(style===null||style===void 0?void 0:style.BorderColor)||\"#f0f0f0\"} !important`,borderRight:`${(style===null||style===void 0?void 0:style.BorderSize)||2}px solid ${(style===null||style===void 0?void 0:style.BorderColor)||\"#f0f0f0\"} !important`,borderLeft:`${(style===null||style===void 0?void 0:style.BorderSize)||2}px solid ${(style===null||style===void 0?void 0:style.BorderColor)||\"#f0f0f0\"} !important`,borderBottom:`${(style===null||style===void 0?void 0:style.BorderSize)||2}px solid ${(style===null||style===void 0?void 0:style.BorderColor)||\"#f0f0f0\"} !important`,backgroundColor:`${style===null||style===void 0?void 0:style.BackgroundColor} !important `||\"#f0f0f0\",position:(style===null||style===void 0?void 0:style.Position)||\"Cover Top\",zIndex:(style===null||style===void 0?void 0:style.Zindex)||9999},id:\"guide-popup\",ref:guidePopUpRef,children:[textArray.map((row,rowIndex)=>/*#__PURE__*/_jsxs(Box,{className:\"qadpt-row\",children:[row.map((textArea,textAreaIndex)=>/*#__PURE__*/_jsxs(Box,{className:\"qadpt-text-area-wrapper\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-text-area\",style:{backgroundColor:(textArea.name===\"RTE\"||textArea.name===\"Rich Text\")&&sectionColor?sectionColor:\"\"},children:textArea.value}),textArea.name===\"Button\"&&/*#__PURE__*/_jsx(IconButton,{className:\"qadpt-add-btn\",size:\"large\",onClick:()=>removeTextArea(rowIndex,textAreaIndex),children:/*#__PURE__*/_jsx(DeleteOutlineOutlinedIcon,{})})]},textAreaIndex)),/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",children:[row.some(item=>item.name===\"Button\")?/*#__PURE__*/_jsx(Tooltip,{title:\"Only one button is allowed\",placement:\"bottom\",children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>{setShowOptions(true);setFocusedRowIndex(rowIndex);},className:\"qadpt-add-btn\",size:\"small\",disabled:true,children:/*#__PURE__*/_jsx(AddIcon,{})})})}):/*#__PURE__*/_jsx(IconButton,{onClick:()=>{setShowOptions(true);setFocusedRowIndex(rowIndex);},className:\"qadpt-add-btn\",size:\"small\",disabled:row.some(item=>item.name===\"Button\"),children:/*#__PURE__*/_jsx(AddIcon,{})}),/*#__PURE__*/_jsx(IconButton,{sx:{// position: \"fixed\",\npadding:\"3px\",boxShadow:\"rgba(0, 0, 0, 0.15) 0px 4px 8px\",marginLeft:\"2px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",display:dismissData!==null&&dismissData!==void 0&&dismissData.dismisssel?\"flex\":\"none\"// Show/hide based on dismisssel\n},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:\"1\",color:\"#000\"}})})]}),isUnSavedChanges&&openWarning&&/*#__PURE__*/_jsx(AlertPopup,{openWarning:openWarning,setopenWarning:setopenWarning,handleLeave:handleLeave}),showOptions&&focusedRowIndex===rowIndex&&/*#__PURE__*/_jsx(Box,{onMouseEnter:()=>setShowOptions(true),onMouseLeave:()=>setShowOptions(false),className:\"qadpt-options-menu\",children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-options-content\",children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"row\",alignItems:\"center\",sx:{cursor:\"pointer\",gap:\"10px\",placeContent:\"center\",width:\"100%\"},onClick:()=>addTextAreaInSameRow(rowIndex,\"button\"),children:[/*#__PURE__*/_jsx(Link,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",children:\"Button\"})]})})})]},rowIndex)),((savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideType)===\"Tour\"||(savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideType)===\"Announcement\")&&/*#__PURE__*/_jsx(Box,{sx:{...(progressTemplate===\"linear\"&&{display:\"flex\",placeContent:\"center\",alignItems:\"center\"})},children:steps.length>=1?/*#__PURE__*/_jsx(_Fragment,{children:renderProgress()}):null}),showEmojiPicker&&/*#__PURE__*/_jsx(EmojiPicker,{onEmojiClick:()=>{}})]})}),buttonProperty&&/*#__PURE__*/_jsx(ButtonSettings,{})]});};export default Banner;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "IconButton", "Box", "Typography", "MobileStepper", "<PERSON><PERSON>", "LinearProgress", "AddIcon", "DeleteOutlineOutlinedIcon", "Link", "RTEsection", "ImageSectionField", "ButtonSection", "HtmlSection", "EmojiPicker", "CloseIcon", "useDrawerStore", "ButtonSettings", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Banner", "_ref", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_bannerJson$GuideStep", "setImageSrc", "imageSrc", "textBoxRef", "setHtmlContent", "htmlContent", "buttonColor", "setButtonColor", "setImageName", "imageName", "alignment", "setAlignment", "textvalue", "setTextvalue", "isBanner", "overlays", "setOverLays", "Bposition", "setBposition", "bpadding", "setbPadding", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "savedGuideData", "Progress", "guidePopUpRef", "dismissData", "sectionColor", "setSectionColor", "buttonProperty", "setButtonProperty", "BborderSize", "Bbordercolor", "backgroundC", "textArray", "setBannerButtonSelected", "setTextArray", "preview", "setPreview", "bannerButtonSelected", "clearGuideDetails", "btnBgColor", "btnTextColor", "btnBorderColor", "buttonsContainer", "setButtonsContainer", "clearBannerButtonDetials", "setRTEAnchorEl", "deleteRTEContainer", "rteAnchorEl", "<PERSON><PERSON><PERSON>", "currentStep", "selectedOption", "ProgressColor", "setProgressColor", "steps", "progress", "createWithAI", "selectedTemplate", "syncAIAnnouncementDataForPreview", "rtesContainer", "state", "textAreas", "setTextAreas", "name", "value", "index", "handleDeleteRTESection", "ref", "restoreTextAreasFromStore", "rowIndex", "restoredTextAreas", "has<PERSON><PERSON><PERSON>", "some", "item", "newButton", "hasRTEContent", "length", "rtes", "text", "trim", "hasRTEInTextAreas", "rteSection", "filter", "timeoutId", "setTimeout", "clearTimeout", "existingRow", "updatedTextAreas", "buttonIndex", "findIndex", "removeTextArea", "console", "log", "hasButtonInData", "hasButtonInTextAreas", "document", "body", "style", "setProperty", "removeProperty", "overlayEnabled", "showOptions", "setShowOptions", "focusedRowIndex", "setFocusedRowIndex", "showEmojiPicker", "setShowEmojiPicker", "newTextAreas", "splice", "addTextAreaInSameRow", "option", "alert", "newValue", "newName", "newButtonContainer", "id", "crypto", "randomUUID", "buttons", "position", "type", "isEditing", "backgroundColor", "borderColor", "color", "actions", "targetURL", "tab", "interaction", "survey", "containerId", "buttonCount", "className", "children", "textAreaIndex", "map", "row", "filteredRow", "_", "idx", "hasButtonInRow", "t", "rteId", "enableProgress", "GuideStep", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "ProgressTemplate", "progressTemplate", "renderProgress", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "enableProgressFromData", "shouldShowProgress", "variant", "activeStep", "sx", "padding", "backButton", "visibility", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "Array", "from", "width", "height", "borderRadius", "fontSize", "find", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Position", "overflow", "Padding", "undefined", "boxShadow", "borderTop", "BorderSize", "BorderColor", "borderRight", "borderLeft", "borderBottom", "BackgroundColor", "zIndex", "Zindex", "textArea", "size", "onClick", "title", "placement", "disabled", "marginLeft", "background", "border", "dismisssel", "zoom", "onMouseEnter", "onMouseLeave", "flexDirection", "cursor", "GuideType", "onEmojiClick"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideBanners/Banners.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { IconButton, Box, Typography, MobileStepper, Button, LinearProgress } from \"@mui/material\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport { TextFormat, Link } from \"@mui/icons-material\";\r\nimport RTEsection from \"../guideSetting/PopupSections/RTEsection\";\r\nimport ImageSectionField from \"./selectedpopupfields/ImageSectionField\";\r\nimport ButtonSection from \"../guideSetting/PopupSections/Button\";\r\nimport HtmlSection from \"../guideSetting/PopupSections/HtmlSection\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport \"./guideBanner.css\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport ButtonSettings from \"./selectedpopupfields/ButtonSettings\";\r\nimport { saveGuide } from \"../../services/GuideListServices\";\r\nimport Tooltip from \"@mui/material/Tooltip\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nconst Banner = ({\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\ttextBoxRef,\r\n\tsetHtmlContent,\r\n\thtmlContent,\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tsetImageName,\r\n\timageName,\r\n\talignment,\r\n\tsetAlignment,\r\n\ttextvalue,\r\n\tsetTextvalue,\r\n\tisBanner,\r\n\toverlays,\r\n\tsetOverLays,\r\n\tBposition,\r\n\tsetBposition,\r\n\tbpadding,\r\n\tsetbPadding,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tsavedGuideData,\r\n\tProgress\r\n}: {\r\n\tsetImageSrc: any;\r\n\timageSrc: any;\r\n\ttextBoxRef: any;\r\n\tsetHtmlContent: any;\r\n\thtmlContent: any;\r\n\tbuttonColor: any;\r\n\tsetButtonColor: any;\r\n\tsetImageName: any;\r\n\timageName: any;\r\n\talignment: any;\r\n\tsetAlignment: any;\r\n\ttextvalue: any;\r\n\tsetTextvalue: any;\r\n\tisBanner: boolean;\r\n\toverlays: boolean;\r\n\tsetOverLays: any;\r\n\tBposition: any;\r\n\tsetBposition: any;\r\n\tbpadding: any;\r\n\t\tsetbPadding: any;\r\n\t\tisUnSavedChanges: boolean;\r\n\t\topenWarning: boolean;\r\n\t\tsetopenWarning: (params: boolean) => void;\r\n\t\thandleLeave: () => void;\r\n\t\tsavedGuideData: GuideData | null;\r\n\t\tProgress: any;\r\n}) => {\r\n\tconst guidePopUpRef = useRef<HTMLDivElement | null>(null);\r\n\tconst {\r\n\t\tdismissData,\r\n\t\tsectionColor,\r\n\t\tsetSectionColor,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tBborderSize,\r\n\t\tBbordercolor,\r\n\t\tbackgroundC,\r\n\t\ttextArray,\r\n\t\tsetBannerButtonSelected,\r\n\t\tsetTextArray,\r\n\t\tpreview,\r\n\t\tsetPreview,\r\n\t\tbannerButtonSelected,\r\n\t\tclearGuideDetails,\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tbuttonsContainer,\r\n\t\tsetButtonsContainer,\r\n\t\tclearBannerButtonDetials,\r\n\t\tsetRTEAnchorEl,\r\n\t\tdeleteRTEContainer,\r\n\t\trteAnchorEl,\r\n\t\tbannerJson,\r\n\t\tcurrentStep,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tsteps,\r\n\t\tprogress,\r\n\t\tcreateWithAI,\r\n\t\tselectedTemplate,\r\n\t\tsyncAIAnnouncementDataForPreview,\r\n\t\trtesContainer\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst [textAreas, setTextAreas] = useState<{ name: string; value: string | JSX.Element }[][]>([\r\n\t\t[\r\n\t\t  {\r\n\t\t\tname: \"Rich Text\",\r\n\t\t\tvalue: (\r\n\t\t\t  <RTEsection\r\n\t\t\t\tkey={0}\r\n\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\tisBanner={true}\r\n\t\t\t\tindex={0}\r\n\t\t\t\thandleDeleteRTESection={() => {}}\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tref={textBoxRef}\r\n\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t  />\r\n\t\t\t),\r\n\t\t  },\r\n\t\t],\r\n\t  ]);\r\n\r\n\t  // Effect to restore textAreas state from persisted store data on component mount\r\n\t  useEffect(() => {\r\n\t\t// Only run this restoration logic once on component mount\r\n\t\tconst restoreTextAreasFromStore = () => {\r\n\t\t\tconst rowIndex = 0;\r\n\t\t\tlet restoredTextAreas = [...textAreas];\r\n\t\t\tlet hasChanges = false;\r\n\r\n\t\t\t// Check if we need to restore button state from the store\r\n\t\t\tif (bannerButtonSelected && !textAreas[rowIndex].some((item) => item.name === \"Button\")) {\r\n\t\t\t\tconst newButton = {\r\n\t\t\t\t\tname: \"Button\",\r\n\t\t\t\t\tvalue: (\r\n\t\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\t\tkey={0}\r\n\t\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t};\r\n\t\t\t\trestoredTextAreas[rowIndex] = [...restoredTextAreas[rowIndex], newButton];\r\n\t\t\t\thasChanges = true;\r\n\t\t\t}\r\n\r\n\t\t\t// Check if RTE content exists in the store and needs to be restored\r\n\t\t\t// For banners, RTE content is stored in rtesContainer\r\n\t\t\tconst hasRTEContent = rtesContainer && rtesContainer.length > 0 &&\r\n\t\t\t\trtesContainer[0].rtes && rtesContainer[0].rtes.length > 0 &&\r\n\t\t\t\trtesContainer[0].rtes[0].text && rtesContainer[0].rtes[0].text.trim() !== \"\";\r\n\r\n\t\t\t// If there's RTE content in the store but the banner is not visible,\r\n\t\t\t// it means we need to restore the textAreas properly\r\n\t\t\tif (hasRTEContent) {\r\n\t\t\t\t// console.log(\"Banner: Restoring RTE content from store\", {\r\n\t\t\t\t// \trteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + \"...\",\r\n\t\t\t\t// \tcurrentTextAreas: textAreas.length\r\n\t\t\t\t// });\r\n\r\n\t\t\t\t// Ensure the RTE section is properly initialized with the stored content\r\n\t\t\t\t// The RTEsection component will automatically pick up the content from rtesContainer\r\n\t\t\t\t// We just need to make sure the textAreas structure is correct\r\n\t\t\t\tconst hasRTEInTextAreas = restoredTextAreas[rowIndex].some((item) => item.name === \"Rich Text\");\r\n\r\n\t\t\t\tif (!hasRTEInTextAreas) {\r\n\t\t\t\t\t// This shouldn't happen normally, but if it does, recreate the RTE section\r\n\t\t\t\t\tconst rteSection = {\r\n\t\t\t\t\t\tname: \"Rich Text\",\r\n\t\t\t\t\t\tvalue: (\r\n\t\t\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\t\t\tkey={0}\r\n\t\t\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t\t\tindex={0}\r\n\t\t\t\t\t\t\t\thandleDeleteRTESection={() => {}}\r\n\t\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t),\r\n\t\t\t\t\t};\r\n\t\t\t\t\trestoredTextAreas[rowIndex] = [rteSection, ...restoredTextAreas[rowIndex].filter(item => item.name !== \"Rich Text\")];\r\n\t\t\t\t\thasChanges = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Update textAreas if changes were made\r\n\t\t\tif (hasChanges) {\r\n\t\t\t\tsetTextAreas(restoredTextAreas);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Run restoration logic after a small delay to ensure store is fully loaded\r\n\t\tconst timeoutId = setTimeout(restoreTextAreasFromStore, 100);\r\n\r\n\t\treturn () => clearTimeout(timeoutId);\r\n\t  }, []); // Empty dependency array - only run once on mount\r\n\r\n\t  // UseEffect to update textAreas when setBannerButtonSelected changes\r\n\tuseEffect(() => {\r\n\t\tconst rowIndex = 0; // Assuming we want to update the first row\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\tif (bannerButtonSelected) {\r\n\t\t  // Check if Button already exists in the first row\r\n\r\n\r\n\t\t  // If Button is not already in the row, add it\r\n\t\t  if (!existingRow.some((item) => item.name === \"Button\")) {\r\n\t\t\tconst newButton = {\r\n\t\t\t  name: \"Button\",\r\n\t\t\t  value: (\r\n\t\t\t\t<ButtonSection\r\n\t\t\t\t  key={0}\r\n\t\t\t\t  buttonColor={buttonColor}\r\n\t\t\t\t  setButtonColor={setButtonColor}\r\n\t\t\t\t  isBanner={true}\r\n\t\t\t\t/>\r\n\t\t\t  ),\r\n\t\t\t};\r\n\r\n\t\t\tconst updatedTextAreas = [...textAreas];\r\n\t\t\tupdatedTextAreas[rowIndex] = [...existingRow, newButton];\r\n\t\t\tsetTextAreas(updatedTextAreas);\r\n\t\t  }\r\n\t\t} else {\r\n\t\t\t// Find the button index and remove it\r\n\t\t\tconst buttonIndex = existingRow.findIndex((item) => item.name === \"Button\");\r\n\t\t\tif (buttonIndex !== -1) {\r\n\t\t\t\tremoveTextArea(rowIndex, buttonIndex);\r\n\t\t\t}\r\n\t\t}\r\n\t  }, [bannerButtonSelected]); // Trigger when setBannerButtonSelected changes\r\n\r\n\t  // Update textArray whenever textAreas changes\r\n\t  useEffect(() => {\r\n\t\tsetTextArray(textAreas);\r\n\t  }, [textAreas]);\r\n\r\n\t  // Sync AI announcement data on component mount to ensure progress bar is visible\r\n\t  useEffect(() => {\r\n\t\t// Only run this for AI-created announcements\r\n\t\tif (!createWithAI || selectedTemplate !== \"Announcement\") return;\r\n\r\n\t\tconsole.log(\"Banner component mounted for AI announcement - syncing data\");\r\n\r\n\t\t// Synchronize AI announcement data to ensure progress bar and other settings are properly initialized\r\n\t\tsyncAIAnnouncementDataForPreview(true); // Preserve global state during banner initialization\r\n\t  }, [createWithAI, selectedTemplate, syncAIAnnouncementDataForPreview]);\r\n\r\n\t  // Sync textAreas with AI-created guide data on mount and when relevant data changes\r\n\t  useEffect(() => {\r\n\t\t// Only run this for AI-created guides\r\n\t\tif (!createWithAI) return;\r\n\r\n\t\tconst rowIndex = 0;\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\r\n\t\t// Check if we need to add a button based on the AI guide data\r\n\t\tconst hasButtonInData = bannerButtonSelected;\r\n\t\tconst hasButtonInTextAreas = existingRow.some((item) => item.name === \"Button\");\r\n\r\n\t\tif (hasButtonInData && !hasButtonInTextAreas) {\r\n\t\t\t// Add button to textAreas if it exists in AI data but not in local state\r\n\t\t\tconst newButton = {\r\n\t\t\t  name: \"Button\",\r\n\t\t\t  value: (\r\n\t\t\t\t<ButtonSection\r\n\t\t\t\t  key={0}\r\n\t\t\t\t  buttonColor={buttonColor}\r\n\t\t\t\t  setButtonColor={setButtonColor}\r\n\t\t\t\t  isBanner={true}\r\n\t\t\t\t/>\r\n\t\t\t  ),\r\n\t\t\t};\r\n\r\n\t\t\tconst updatedTextAreas = [...textAreas];\r\n\t\t\tupdatedTextAreas[rowIndex] = [...existingRow, newButton];\r\n\t\t\tsetTextAreas(updatedTextAreas);\r\n\t\t}\r\n\t  }, [createWithAI, bannerButtonSelected, buttonColor, setButtonColor]); // Dependencies for AI guide sync\r\n\r\n\t  // Additional effect to ensure button state is preserved for AI guides\r\n\t  useEffect(() => {\r\n\t\tif (!createWithAI) return;\r\n\r\n\t\t// Check if we have button data in the store but not in textAreas\r\n\t\tconst rowIndex = 0;\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\tconst hasButtonInTextAreas = existingRow.some((item) => item.name === \"Button\");\r\n\r\n\t\t// If buttonsContainer has buttons but textAreas doesn't, restore the button\r\n\t\tif (buttonsContainer.length > 0 && !hasButtonInTextAreas && !bannerButtonSelected) {\r\n\t\t\tsetBannerButtonSelected(true);\r\n\t\t}\r\n\t  }, [createWithAI, buttonsContainer, textAreas, bannerButtonSelected, setBannerButtonSelected]); // Monitor button container changes\r\n\r\n\t// Handle overflow behavior based on banner position in creation mode\r\n\tuseEffect(() => {\r\n\t\t// Use a small delay to ensure this runs after resetHeightofBanner\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\t// Only apply overflow hidden in creation/edit mode when position is \"Cover Top\"\r\n\t\t\t// This component is only rendered in creation mode (when !showBannerenduser)\r\n\t\t\t// Preview mode is handled by separate preview components\r\n\t\t\tif (Bposition === \"Cover Top\") {\r\n\t\t\t\tdocument.body.style.setProperty(\"overflow\", \"hidden\", \"important\");\r\n\t\t\t} else {\r\n\t\t\t\t// Restore normal scrolling for other positions\r\n\t\t\t\tdocument.body.style.removeProperty(\"overflow\");\r\n\t\t\t}\r\n\t\t}, 100); // Small delay to ensure it runs after resetHeightofBanner\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tclearTimeout(timeoutId);\r\n\t\t\tdocument.body.style.removeProperty(\"overflow\");\r\n\t\t};\r\n\t}, [Bposition]); // Re-run when position changes\r\n\r\n\tconst overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\tconst [showOptions, setShowOptions] = useState(false);\r\n\tconst [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null);\r\n\tconst [showEmojiPicker, setShowEmojiPicker] = useState(false);\r\n\r\n\tconst handleDeleteRTESection = (index: number) => {\r\n\t\tconst newTextAreas = [...textAreas];\r\n\t\tnewTextAreas.splice(index, 1);\r\n\t\tsetTextAreas(newTextAreas);\r\n\t\tsetTextArray(newTextAreas);\r\n\r\n\t};\r\n\r\n\tconst addTextAreaInSameRow = (rowIndex: number, option: string) => {\r\n\t\tif (!textAreas[rowIndex]) {\r\n\t\t\ttextAreas[rowIndex] = [];\r\n\t\t}\r\n\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\t// Check if a Rich Text or Button already exists in the row\r\n\t\tif (\r\n\t\t\t(option === \"richText\" && existingRow.some((item) => item.name === \"Rich Text\")) ||\r\n\t\t\t(option === \"button\" && existingRow.some((item) => item.name === \"Button\"))\r\n\t\t) {\r\n\t\t\talert(`Only one ${option === \"richText\" ? \"Rich Text\" : \"Button\"} is allowed per row.`);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet newValue: JSX.Element | string;\r\n\t\tlet newName: string = \"\";\r\n\r\n\t\tswitch (option) {\r\n\t\t\tcase \"image\":\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<ImageSectionField\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\tsetImageSrc={setImageSrc}\r\n\t\t\t\t\t\timageSrc={imageSrc}\r\n\t\t\t\t\t\tsetImageName={setImageName}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Image\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"richText\":\r\n\t\t\t\t// setBannerButtonSelected(false);\r\n\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\tindex={rowIndex}\r\n\t\t\t\t\t\thandleDeleteRTESection={handleDeleteRTESection}\r\n\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Rich Text\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"button\":\r\n\t\t\t\tsetBannerButtonSelected(true);\r\n\r\n\t\t\t\t// CRITICAL FIX: Ensure button container exists when adding button through banner editor\r\n\t\t\t\t// This creates the actual button data that ButtonSection component needs to display buttons\r\n\t\t\t\tif (buttonsContainer.length === 0) {\r\n\t\t\t\t\tconst newButtonContainer = {\r\n\t\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\t\tbuttons: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\t\t\t\tname: \"Got It\",\r\n\t\t\t\t\t\t\t\tposition: \"center\" as const,\r\n\t\t\t\t\t\t\t\ttype: \"primary\" as const,\r\n\t\t\t\t\t\t\t\tisEditing: false,\r\n\t\t\t\t\t\t\t\tindex: 0,\r\n\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\tborderColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#ffffff\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tactions: {\r\n\t\t\t\t\t\t\t\t\tvalue: \"close\" as const,\r\n\t\t\t\t\t\t\t\t\ttargetURL: \"\",\r\n\t\t\t\t\t\t\t\t\ttab: \"same-tab\" as const,\r\n\t\t\t\t\t\t\t\t\tinteraction: null,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tsurvey: null,\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// Add the button container to the store\r\n\t\t\t\t\tsetButtonsContainer([newButtonContainer]);\r\n\r\n\t\t\t\t\tconsole.log(\"🔄 Banner editor: Created new button container for banner tour\", {\r\n\t\t\t\t\t\tcontainerId: newButtonContainer.id,\r\n\t\t\t\t\t\tbuttonCount: newButtonContainer.buttons.length\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Button\";\r\n\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"html\":\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<div className=\"htmlbanner\">\r\n\t\t\t\t\t\t<HtmlSection\r\n\t\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\t\thtmlContent={htmlContent}\r\n\t\t\t\t\t\t\tsetHtmlContent={setHtmlContent}\r\n\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"HTML\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tnewValue = \"\";\r\n\t\t\t\tnewName = \"Text\";\r\n\t\t}\r\n\r\n\r\n\t\tconst newTextAreas = [...textAreas];\r\n\r\n\t\tnewTextAreas[rowIndex] = [...existingRow, { name: newName, value: newValue }];\r\n\t\tsetTextAreas(newTextAreas);\r\n\t\tsetTextArray(newTextAreas);\r\n\t};\r\n\r\n\tconst removeTextArea = (rowIndex: number, textAreaIndex: number) => {\r\n\t\tconst updatedTextAreas = textAreas.map((row, index) => {\r\n\t\t\tif (index === rowIndex) {\r\n\t\t\t  const filteredRow = row.filter((_, idx) => idx !== textAreaIndex);\r\n\r\n\t\t\t  // Check if there are any buttons remaining in the row after removal\r\n\t\t\t  const hasButtonInRow = filteredRow.some((t) => t.name === \"Button\");\r\n\t\t\t  setBannerButtonSelected(hasButtonInRow);\r\n\r\n\t\t\t  return filteredRow;\r\n\t\t\t}\r\n\t\t\treturn row;\r\n\t\t});\r\n\t\tif (textAreaIndex === 1)\r\n\t\t{\r\n\t\t\tclearBannerButtonDetials();\r\n\t\t}\r\n\t\telse if (textAreaIndex === 0)\r\n\t\t{\r\n\t\t\tsetRTEAnchorEl({\r\n\t\t\t\trteId: \"\",\r\n\t\t\t\tcontainerId: \"\",\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: null,\r\n\t\t\t});\r\n\r\n\t\t\tdeleteRTEContainer(rteAnchorEl.containerId);\r\n\t\t}\r\n\t\tsetTextAreas(updatedTextAreas);\r\n\t\tsetTextArray(updatedTextAreas);\r\n\r\n\t};\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\t// Check both the global progress state and the saved guide data for progress settings\r\n\t\tconst enableProgressFromData = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress;\r\n\t\tconst shouldShowProgress = progress || enableProgressFromData;\r\n\r\n\t\tif(!shouldShowProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\", padding:\"8px 0 0 0 !important\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: steps.length }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\",\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{fontSize:\"12px\", color: ProgressColor}} >\r\n\t\t\t\t\t\t Step {currentStep} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{    width: \"calc(50% - 410px)\",\r\n\t\t\t\t\tpadding: \"8px 0 0 0\"}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={Progress}\r\n                            sx={{'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\tconst style = bannerJson.GuideStep.find((step:any) => step.stepName === currentStep)?.Canvas as\r\n\t\t| Record<string, unknown>\r\n\t\t| undefined;\r\n// Apply overflow hidden to body when canvas position is \"Cover Top\" in creation mode\r\nuseEffect(() => {\r\n\tif (style?.Position === \"Cover Top\") {\r\n\t\tdocument.body.style.overflow = \"hidden\";\r\n\t} else {\r\n\t\tdocument.body.style.overflow = \"\";\r\n\t}\r\n\r\n\t// Cleanup function to restore overflow when component unmounts\r\n\treturn () => {\r\n\t\tdocument.body.style.overflow = \"\";\r\n\t};\r\n}, [style?.Position]); // Re-run when canvas position changes\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{/* <Box\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\twidth: \"100vw\",\r\n\t\t\t\t\theight: \"17vh\",\r\n\t\t\t\t\tborderColor: (style?.BorderColor as string) || \"defaultColor\",\r\n    zIndex: overlays ? 1300 : \"\",\r\n    backgroundColor: style?.BackgroundColor as string | undefined,\r\n\t\t\t\t}}\r\n\t\t\t/> */}\r\n\r\n\t\t\t<div className=\"qadpt-container creation\">\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-box\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tpadding: style?.Padding !== undefined && style?.Padding !== null ? `${style.Padding}px` : \"10px\",\r\n\t\t\t\t\t\tboxShadow: (style && (style?.Position === \"Push Down\")) ?  \"none\" : \"0px 1px 15px rgba(0, 0, 0, 0.7)\",\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t`${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important` ,\r\n\r\n\t\t\t\t\tborderBottom: `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t,\r\n\t\t\t\t\tbackgroundColor: `${style?.BackgroundColor} !important ` || \"#f0f0f0\",\r\n\t\t\t\t\tposition: style?.Position || \"Cover Top\",\r\n\t\t\t\t\tzIndex: style?.Zindex || 9999,\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\tref={guidePopUpRef}\r\n\t\t\t>\r\n\r\n\r\n{(textArray ).map((row: any, rowIndex: number) => (\r\n  <Box key={rowIndex} className=\"qadpt-row\">\r\n    {row.map((textArea: any, textAreaIndex: number) => (\r\n      <Box key={textAreaIndex} className=\"qadpt-text-area-wrapper\">\r\n        <div\r\n          className=\"qadpt-text-area\"\r\n          style={{\r\n            backgroundColor:\r\n\t\t\t\t  (textArea.name === \"RTE\" || textArea.name === \"Rich Text\") && sectionColor ? sectionColor : \"\",\r\n          }}\r\n        >\r\n\t\t\t\t{textArea.value}\r\n        </div>\r\n          {textArea.name === \"Button\" && (\r\n            <IconButton\r\n              className=\"qadpt-add-btn\"\r\n              size=\"large\"\r\n              onClick={() => removeTextArea(rowIndex, textAreaIndex)}\r\n            >\r\n              <DeleteOutlineOutlinedIcon />\r\n            </IconButton>\r\n          )}\r\n      </Box>\r\n    ))}\r\n\r\n\r\n      <Box display=\"flex\" alignItems=\"center\">\r\n\t  {row.some((item: any) => item.name === \"Button\") ? (\r\n    \t<Tooltip title=\"Only one button is allowed\" placement=\"bottom\">\r\n          <span>\r\n            <IconButton\r\n              onClick={() => {\r\n                setShowOptions(true);\r\n                setFocusedRowIndex(rowIndex);\r\n              }}\r\n              className=\"qadpt-add-btn\"\r\n              size=\"small\"\r\n\t\t\t  disabled={true}\r\n\t\t\t\t\t>\r\n              <AddIcon />\r\n            </IconButton>\r\n          </span>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t) : (\r\n\t\t\t\t<IconButton\r\n\t\t\t\tonClick={() => {\r\n\t\t\t\t  setShowOptions(true);\r\n\t\t\t\t  setFocusedRowIndex(rowIndex);\r\n\t\t\t\t}}\r\n\t\t\t\tclassName=\"qadpt-add-btn\"\r\n\t\t\t\tsize=\"small\"\r\n\t\t\t\tdisabled={row.some((item: any) => item.name === \"Button\")}\r\n\t\t\t  >\r\n\t\t\t\t<AddIcon />\r\n\t\t\t  </IconButton>\r\n\t\t\t)}\r\n        {/* Always render the dismiss icon, but conditionally show/hide it based on dismissData.dismisssel */}\r\n        <IconButton\r\n          sx={{\r\n\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\t\tpadding:\"3px\",\r\n            boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n            marginLeft: \"2px\",\r\n            background: \"#fff !important\",\r\n            border: \"1px solid #ccc\",\r\n            zIndex:\"999999\",\r\n            display: dismissData?.dismisssel ? \"flex\" : \"none\", // Show/hide based on dismisssel\r\n          }}\r\n        >\r\n\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\" }}   />\r\n        </IconButton>\r\n      </Box>\r\n\t  {isUnSavedChanges && openWarning &&(\r\n\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n    {showOptions && focusedRowIndex === rowIndex && (\r\n      <Box\r\n        onMouseEnter={() => setShowOptions(true)}\r\n        onMouseLeave={() => setShowOptions(false)}\r\n        className=\"qadpt-options-menu\"\r\n      >\r\n        <Box className=\"qadpt-options-content\">\r\n          <Box\r\n            display=\"flex\"\r\n            flexDirection=\"row\"\r\n            alignItems=\"center\"\r\n            sx={{ cursor: \"pointer\",gap : \"10px\",placeContent:\"center\",width:\"100%\" }}\r\n\r\n            onClick={() => addTextAreaInSameRow(rowIndex, \"button\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t<Link />\r\n\t\t\t<Typography variant=\"caption\">Button</Typography>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n    )}\r\n  </Box>\r\n))}\r\n{(savedGuideData?.GuideType === \"Tour\" || savedGuideData?.GuideType === \"Announcement\") &&\r\n\r\n<Box\r\nsx={{\r\n  ...(progressTemplate === \"linear\" && {\r\n\tdisplay: \"flex\",\r\n\tplaceContent: \"center\",\r\n\talignItems:\"center\"\r\n  }),\r\n}}\r\n>\r\n\t\t{steps.length >= 1 ? (\r\n                    <>\r\n                        {renderProgress()}\r\n                    </>\r\n                ) : (\r\n                    null\r\n                )}\r\n\t\t\t\t</Box>\r\n\r\n}\r\n\t\t\t\t\t{showEmojiPicker && <EmojiPicker onEmojiClick={() => {}} />}\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t\t{buttonProperty  && <ButtonSettings />}\r\n\r\n\t\t\t{/* {dismissData?.dismisssel && (\r\n\t\t\t\t<IconButton\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\ttop: \"8%\",\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tcolor: \"red\",\r\n\t\t\t\t\t\tborderRadius: \"2px\",\r\n\t\t\t\t\t\t//padding: \"5px\",\r\n\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\tzIndex: \"999\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t</IconButton>\r\n\t\t\t)} */}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default Banner;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,UAAU,CAAEC,GAAG,CAAEC,UAAU,CAAEC,aAAa,CAAEC,MAAM,CAAEC,cAAc,KAAQ,eAAe,CAClG,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,yBAAyB,KAAM,2CAA2C,CACjF,OAAqBC,IAAI,KAAQ,qBAAqB,CACtD,MAAO,CAAAC,UAAU,KAAM,0CAA0C,CACjE,MAAO,CAAAC,iBAAiB,KAAM,yCAAyC,CACvE,MAAO,CAAAC,aAAa,KAAM,sCAAsC,CAChE,MAAO,CAAAC,WAAW,KAAM,2CAA2C,CACnE,MAAO,CAAAC,WAAW,KAAM,oBAAoB,CAC5C,MAAO,mBAAmB,CAC1B,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAiC,yBAAyB,CAC/E,MAAO,CAAAC,cAAc,KAAM,sCAAsC,CAEjE,MAAO,CAAAC,OAAO,KAAM,uBAAuB,CAC3C,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE9C,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAsDT,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,IAtDU,CACfC,WAAW,CACXC,QAAQ,CACRC,UAAU,CACVC,cAAc,CACdC,WAAW,CACXC,WAAW,CACXC,cAAc,CACdC,YAAY,CACZC,SAAS,CACTC,SAAS,CACTC,YAAY,CACZC,SAAS,CACTC,YAAY,CACZC,QAAQ,CACRC,QAAQ,CACRC,WAAW,CACXC,SAAS,CACTC,YAAY,CACZC,QAAQ,CACRC,WAAW,CACXC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,QA4BD,CAAC,CAAA9B,IAAA,CACA,KAAM,CAAA+B,aAAa,CAAG3D,MAAM,CAAwB,IAAI,CAAC,CACzD,KAAM,CACL4D,WAAW,CACXC,YAAY,CACZC,eAAe,CACfC,cAAc,CACdC,iBAAiB,CACjBC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,SAAS,CACTC,uBAAuB,CACvBC,YAAY,CACZC,OAAO,CACPC,UAAU,CACVC,oBAAoB,CACpBC,iBAAiB,CACjBC,UAAU,CACVC,YAAY,CACZC,cAAc,CACdC,gBAAgB,CAChBC,mBAAmB,CACnBC,wBAAwB,CACxBC,cAAc,CACdC,kBAAkB,CAClBC,WAAW,CACXC,UAAU,CACVC,WAAW,CACXC,cAAc,CACdC,aAAa,CACbC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,CACRC,YAAY,CACZC,gBAAgB,CAChBC,gCAAgC,CAChCC,aACD,CAAC,CAAG7E,cAAc,CAAE8E,KAAK,EAAKA,KAAK,CAAC,CAEpC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGlG,QAAQ,CAAoD,CAC7F,CACE,CACDmG,IAAI,CAAE,WAAW,CACjBC,KAAK,cACH7E,IAAA,CAACX,UAAU,EAEZwB,UAAU,CAAEA,UAAW,CACvBW,QAAQ,CAAE,IAAK,CACfsD,KAAK,CAAE,CAAE,CACTC,sBAAsB,CAAEA,CAAA,GAAM,CAAC,CAC/B;AAAA,CACAC,GAAG,CAAEnE,UAAW,CAChBwB,aAAa,CAAEA,aAAc,EAPxB,CAQH,CAEF,CAAC,CACF,CACC,CAAC,CAEF;AACA1D,SAAS,CAAC,IAAM,CACjB;AACA,KAAM,CAAAsG,yBAAyB,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAAC,QAAQ,CAAG,CAAC,CAClB,GAAI,CAAAC,iBAAiB,CAAG,CAAC,GAAGT,SAAS,CAAC,CACtC,GAAI,CAAAU,UAAU,CAAG,KAAK,CAEtB;AACA,GAAIjC,oBAAoB,EAAI,CAACuB,SAAS,CAACQ,QAAQ,CAAC,CAACG,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAC,CAAE,CACxF,KAAM,CAAAW,SAAS,CAAG,CACjBX,IAAI,CAAE,QAAQ,CACdC,KAAK,cACJ7E,IAAA,CAACT,aAAa,EAEbyB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BO,QAAQ,CAAE,IAAK,EAHV,CAIL,CAEH,CAAC,CACD2D,iBAAiB,CAACD,QAAQ,CAAC,CAAG,CAAC,GAAGC,iBAAiB,CAACD,QAAQ,CAAC,CAAEK,SAAS,CAAC,CACzEH,UAAU,CAAG,IAAI,CAClB,CAEA;AACA;AACA,KAAM,CAAAI,aAAa,CAAGhB,aAAa,EAAIA,aAAa,CAACiB,MAAM,CAAG,CAAC,EAC9DjB,aAAa,CAAC,CAAC,CAAC,CAACkB,IAAI,EAAIlB,aAAa,CAAC,CAAC,CAAC,CAACkB,IAAI,CAACD,MAAM,CAAG,CAAC,EACzDjB,aAAa,CAAC,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,EAAInB,aAAa,CAAC,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAE7E;AACA;AACA,GAAIJ,aAAa,CAAE,CAClB;AACA;AACA;AACA;AAEA;AACA;AACA;AACA,KAAM,CAAAK,iBAAiB,CAAGV,iBAAiB,CAACD,QAAQ,CAAC,CAACG,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,WAAW,CAAC,CAE/F,GAAI,CAACiB,iBAAiB,CAAE,CACvB;AACA,KAAM,CAAAC,UAAU,CAAG,CAClBlB,IAAI,CAAE,WAAW,CACjBC,KAAK,cACJ7E,IAAA,CAACX,UAAU,EAEVwB,UAAU,CAAEA,UAAW,CACvBW,QAAQ,CAAE,IAAK,CACfsD,KAAK,CAAE,CAAE,CACTC,sBAAsB,CAAEA,CAAA,GAAM,CAAC,CAC/B;AAAA,CACAC,GAAG,CAAEnE,UAAW,CAChBwB,aAAa,CAAEA,aAAc,EAPxB,CAQL,CAEH,CAAC,CACD8C,iBAAiB,CAACD,QAAQ,CAAC,CAAG,CAACY,UAAU,CAAE,GAAGX,iBAAiB,CAACD,QAAQ,CAAC,CAACa,MAAM,CAACT,IAAI,EAAIA,IAAI,CAACV,IAAI,GAAK,WAAW,CAAC,CAAC,CACpHQ,UAAU,CAAG,IAAI,CAClB,CACD,CAEA;AACA,GAAIA,UAAU,CAAE,CACfT,YAAY,CAACQ,iBAAiB,CAAC,CAChC,CACD,CAAC,CAED;AACA,KAAM,CAAAa,SAAS,CAAGC,UAAU,CAAChB,yBAAyB,CAAE,GAAG,CAAC,CAE5D,MAAO,IAAMiB,YAAY,CAACF,SAAS,CAAC,CACnC,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACFrH,SAAS,CAAC,IAAM,CACf,KAAM,CAAAuG,QAAQ,CAAG,CAAC,CAAE;AACpB,KAAM,CAAAiB,WAAW,CAAGzB,SAAS,CAACQ,QAAQ,CAAC,CACvC,GAAI/B,oBAAoB,CAAE,CACxB;AAGA;AACA,GAAI,CAACgD,WAAW,CAACd,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAC,CAAE,CAC1D,KAAM,CAAAW,SAAS,CAAG,CAChBX,IAAI,CAAE,QAAQ,CACdC,KAAK,cACN7E,IAAA,CAACT,aAAa,EAEZyB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BO,QAAQ,CAAE,IAAK,EAHV,CAIN,CAEF,CAAC,CAED,KAAM,CAAA4E,gBAAgB,CAAG,CAAC,GAAG1B,SAAS,CAAC,CACvC0B,gBAAgB,CAAClB,QAAQ,CAAC,CAAG,CAAC,GAAGiB,WAAW,CAAEZ,SAAS,CAAC,CACxDZ,YAAY,CAACyB,gBAAgB,CAAC,CAC7B,CACF,CAAC,IAAM,CACN;AACA,KAAM,CAAAC,WAAW,CAAGF,WAAW,CAACG,SAAS,CAAEhB,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAC,CAC3E,GAAIyB,WAAW,GAAK,CAAC,CAAC,CAAE,CACvBE,cAAc,CAACrB,QAAQ,CAAEmB,WAAW,CAAC,CACtC,CACD,CACC,CAAC,CAAE,CAAClD,oBAAoB,CAAC,CAAC,CAAE;AAE5B;AACAxE,SAAS,CAAC,IAAM,CACjBqE,YAAY,CAAC0B,SAAS,CAAC,CACtB,CAAC,CAAE,CAACA,SAAS,CAAC,CAAC,CAEf;AACA/F,SAAS,CAAC,IAAM,CACjB;AACA,GAAI,CAAC0F,YAAY,EAAIC,gBAAgB,GAAK,cAAc,CAAE,OAE1DkC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC,CAE1E;AACAlC,gCAAgC,CAAC,IAAI,CAAC,CAAE;AACvC,CAAC,CAAE,CAACF,YAAY,CAAEC,gBAAgB,CAAEC,gCAAgC,CAAC,CAAC,CAEtE;AACA5F,SAAS,CAAC,IAAM,CACjB;AACA,GAAI,CAAC0F,YAAY,CAAE,OAEnB,KAAM,CAAAa,QAAQ,CAAG,CAAC,CAClB,KAAM,CAAAiB,WAAW,CAAGzB,SAAS,CAACQ,QAAQ,CAAC,CAEvC;AACA,KAAM,CAAAwB,eAAe,CAAGvD,oBAAoB,CAC5C,KAAM,CAAAwD,oBAAoB,CAAGR,WAAW,CAACd,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAC,CAE/E,GAAI8B,eAAe,EAAI,CAACC,oBAAoB,CAAE,CAC7C;AACA,KAAM,CAAApB,SAAS,CAAG,CAChBX,IAAI,CAAE,QAAQ,CACdC,KAAK,cACN7E,IAAA,CAACT,aAAa,EAEZyB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BO,QAAQ,CAAE,IAAK,EAHV,CAIN,CAEF,CAAC,CAED,KAAM,CAAA4E,gBAAgB,CAAG,CAAC,GAAG1B,SAAS,CAAC,CACvC0B,gBAAgB,CAAClB,QAAQ,CAAC,CAAG,CAAC,GAAGiB,WAAW,CAAEZ,SAAS,CAAC,CACxDZ,YAAY,CAACyB,gBAAgB,CAAC,CAC/B,CACC,CAAC,CAAE,CAAC/B,YAAY,CAAElB,oBAAoB,CAAEnC,WAAW,CAAEC,cAAc,CAAC,CAAC,CAAE;AAEvE;AACAtC,SAAS,CAAC,IAAM,CACjB,GAAI,CAAC0F,YAAY,CAAE,OAEnB;AACA,KAAM,CAAAa,QAAQ,CAAG,CAAC,CAClB,KAAM,CAAAiB,WAAW,CAAGzB,SAAS,CAACQ,QAAQ,CAAC,CACvC,KAAM,CAAAyB,oBAAoB,CAAGR,WAAW,CAACd,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAC,CAE/E;AACA,GAAIpB,gBAAgB,CAACiC,MAAM,CAAG,CAAC,EAAI,CAACkB,oBAAoB,EAAI,CAACxD,oBAAoB,CAAE,CAClFJ,uBAAuB,CAAC,IAAI,CAAC,CAC9B,CACC,CAAC,CAAE,CAACsB,YAAY,CAAEb,gBAAgB,CAAEkB,SAAS,CAAEvB,oBAAoB,CAAEJ,uBAAuB,CAAC,CAAC,CAAE;AAElG;AACApE,SAAS,CAAC,IAAM,CACf;AACA,KAAM,CAAAqH,SAAS,CAAGC,UAAU,CAAC,IAAM,CAClC;AACA;AACA;AACA,GAAItE,SAAS,GAAK,WAAW,CAAE,CAC9BiF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,UAAU,CAAE,QAAQ,CAAE,WAAW,CAAC,CACnE,CAAC,IAAM,CACN;AACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC,CAC/C,CACD,CAAC,CAAE,GAAG,CAAC,CAAE;AAET;AACA,MAAO,IAAM,CACZd,YAAY,CAACF,SAAS,CAAC,CACvBY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC,CAC/C,CAAC,CACF,CAAC,CAAE,CAACrF,SAAS,CAAC,CAAC,CAAE;AAEjB,KAAM,CAAAsF,cAAc,CAAGtH,cAAc,CAAE8E,KAAK,EAAKA,KAAK,CAACwC,cAAc,CAAC,CACtE,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG1I,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC2I,eAAe,CAAEC,kBAAkB,CAAC,CAAG5I,QAAQ,CAAgB,IAAI,CAAC,CAC3E,KAAM,CAAC6I,eAAe,CAAEC,kBAAkB,CAAC,CAAG9I,QAAQ,CAAC,KAAK,CAAC,CAE7D,KAAM,CAAAsG,sBAAsB,CAAID,KAAa,EAAK,CACjD,KAAM,CAAA0C,YAAY,CAAG,CAAC,GAAG9C,SAAS,CAAC,CACnC8C,YAAY,CAACC,MAAM,CAAC3C,KAAK,CAAE,CAAC,CAAC,CAC7BH,YAAY,CAAC6C,YAAY,CAAC,CAC1BxE,YAAY,CAACwE,YAAY,CAAC,CAE3B,CAAC,CAED,KAAM,CAAAE,oBAAoB,CAAGA,CAACxC,QAAgB,CAAEyC,MAAc,GAAK,CAClE,GAAI,CAACjD,SAAS,CAACQ,QAAQ,CAAC,CAAE,CACzBR,SAAS,CAACQ,QAAQ,CAAC,CAAG,EAAE,CACzB,CAEA,KAAM,CAAAiB,WAAW,CAAGzB,SAAS,CAACQ,QAAQ,CAAC,CACvC;AACA,GACEyC,MAAM,GAAK,UAAU,EAAIxB,WAAW,CAACd,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,WAAW,CAAC,EAC9E+C,MAAM,GAAK,QAAQ,EAAIxB,WAAW,CAACd,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAE,CAC1E,CACDgD,KAAK,CAAC,YAAYD,MAAM,GAAK,UAAU,CAAG,WAAW,CAAG,QAAQ,sBAAsB,CAAC,CACvF,OACD,CAEA,GAAI,CAAAE,QAA8B,CAClC,GAAI,CAAAC,OAAe,CAAG,EAAE,CAExB,OAAQH,MAAM,EACb,IAAK,OAAO,CACXE,QAAQ,cACP7H,IAAA,CAACV,iBAAiB,EAEjBqB,WAAW,CAAEA,WAAY,CACzBC,QAAQ,CAAEA,QAAS,CACnBM,YAAY,CAAEA,YAAa,EAHtBgE,QAIL,CACD,CACD4C,OAAO,CAAG,OAAO,CACjB,MACD,IAAK,UAAU,CACd;AAEAD,QAAQ,cACP7H,IAAA,CAACX,UAAU,EAEVwB,UAAU,CAAEA,UAAW,CACvBW,QAAQ,CAAE,IAAK,CACfsD,KAAK,CAAEI,QAAS,CAChBH,sBAAsB,CAAEA,sBACxB;AAAA,CACAC,GAAG,CAAEnE,UAAW,CAChBwB,aAAa,CAAEA,aAAc,EAPxB6C,QAQL,CACD,CACD4C,OAAO,CAAG,WAAW,CACrB,MACD,IAAK,QAAQ,CACZ/E,uBAAuB,CAAC,IAAI,CAAC,CAE7B;AACA;AACA,GAAIS,gBAAgB,CAACiC,MAAM,GAAK,CAAC,CAAE,CAClC,KAAM,CAAAsC,kBAAkB,CAAG,CAC1BC,EAAE,CAAEC,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,OAAO,CAAE,CACR,CACCH,EAAE,CAAEC,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBtD,IAAI,CAAE,QAAQ,CACdwD,QAAQ,CAAE,QAAiB,CAC3BC,IAAI,CAAE,SAAkB,CACxBC,SAAS,CAAE,KAAK,CAChBxD,KAAK,CAAE,CAAC,CACRgC,KAAK,CAAE,CACNyB,eAAe,CAAE,SAAS,CAC1BC,WAAW,CAAE,SAAS,CACtBC,KAAK,CAAE,SACR,CAAC,CACDC,OAAO,CAAE,CACR7D,KAAK,CAAE,OAAgB,CACvB8D,SAAS,CAAE,EAAE,CACbC,GAAG,CAAE,UAAmB,CACxBC,WAAW,CAAE,IACd,CAAC,CACDC,MAAM,CAAE,IACT,CAAC,CACD,CACDhC,KAAK,CAAE,CACNyB,eAAe,CAAE,aAClB,CACD,CAAC,CAED;AACA9E,mBAAmB,CAAC,CAACsE,kBAAkB,CAAC,CAAC,CAEzCvB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAE,CAC7EsC,WAAW,CAAEhB,kBAAkB,CAACC,EAAE,CAClCgB,WAAW,CAAEjB,kBAAkB,CAACI,OAAO,CAAC1C,MACzC,CAAC,CAAC,CACH,CAEAoC,QAAQ,cACP7H,IAAA,CAACT,aAAa,EAEbyB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BO,QAAQ,CAAE,IAAK,EAHV0D,QAIL,CACD,CACD4C,OAAO,CAAG,QAAQ,CAElB,MACD,IAAK,MAAM,CACVD,QAAQ,cACP7H,IAAA,QAAKiJ,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC1BlJ,IAAA,CAACR,WAAW,EAEXuB,WAAW,CAAEA,WAAY,CACzBD,cAAc,CAAEA,cAAe,CAC/BU,QAAQ,CAAE,IAAK,EAHV0D,QAIL,CAAC,CACE,CACL,CACD4C,OAAO,CAAG,MAAM,CAChB,MACD,QACCD,QAAQ,CAAG,EAAE,CACbC,OAAO,CAAG,MAAM,CAClB,CAGA,KAAM,CAAAN,YAAY,CAAG,CAAC,GAAG9C,SAAS,CAAC,CAEnC8C,YAAY,CAACtC,QAAQ,CAAC,CAAG,CAAC,GAAGiB,WAAW,CAAE,CAAEvB,IAAI,CAAEkD,OAAO,CAAEjD,KAAK,CAAEgD,QAAS,CAAC,CAAC,CAC7ElD,YAAY,CAAC6C,YAAY,CAAC,CAC1BxE,YAAY,CAACwE,YAAY,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAjB,cAAc,CAAGA,CAACrB,QAAgB,CAAEiE,aAAqB,GAAK,CACnE,KAAM,CAAA/C,gBAAgB,CAAG1B,SAAS,CAAC0E,GAAG,CAAC,CAACC,GAAG,CAAEvE,KAAK,GAAK,CACtD,GAAIA,KAAK,GAAKI,QAAQ,CAAE,CACtB,KAAM,CAAAoE,WAAW,CAAGD,GAAG,CAACtD,MAAM,CAAC,CAACwD,CAAC,CAAEC,GAAG,GAAKA,GAAG,GAAKL,aAAa,CAAC,CAEjE;AACA,KAAM,CAAAM,cAAc,CAAGH,WAAW,CAACjE,IAAI,CAAEqE,CAAC,EAAKA,CAAC,CAAC9E,IAAI,GAAK,QAAQ,CAAC,CACnE7B,uBAAuB,CAAC0G,cAAc,CAAC,CAEvC,MAAO,CAAAH,WAAW,CACpB,CACA,MAAO,CAAAD,GAAG,CACX,CAAC,CAAC,CACF,GAAIF,aAAa,GAAK,CAAC,CACvB,CACCzF,wBAAwB,CAAC,CAAC,CAC3B,CAAC,IACI,IAAIyF,aAAa,GAAK,CAAC,CAC5B,CACCxF,cAAc,CAAC,CACdgG,KAAK,CAAE,EAAE,CACTZ,WAAW,CAAE,EAAE,CACf;AACAlE,KAAK,CAAE,IACR,CAAC,CAAC,CAEFjB,kBAAkB,CAACC,WAAW,CAACkF,WAAW,CAAC,CAC5C,CACApE,YAAY,CAACyB,gBAAgB,CAAC,CAC9BpD,YAAY,CAACoD,gBAAgB,CAAC,CAE/B,CAAC,CACD,KAAM,CAAAwD,cAAc,CAAG,CAAAzH,cAAc,SAAdA,cAAc,kBAAA5B,qBAAA,CAAd4B,cAAc,CAAE0H,SAAS,UAAAtJ,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgCX,OAAO,UAAAY,sBAAA,iBAAvCA,sBAAA,CAAyCqJ,cAAc,GAAI,KAAK,CACvF,QAAS,CAAAC,mBAAmBA,CAAC/F,cAAmB,CAAE,KAAAgG,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACjD,GAAIlG,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IACU,IAAIA,cAAc,GAAK,CAAC,CAAE,CACpC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAA7B,cAAc,SAAdA,cAAc,kBAAA6H,sBAAA,CAAd7H,cAAc,CAAE0H,SAAS,UAAAG,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgCpK,OAAO,UAAAqK,sBAAA,iBAAvCA,sBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAAC/F,cAAc,CAAC,CAC5D,KAAM,CAAAqG,cAAc,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC5B;AACA,KAAM,CAAAC,sBAAsB,CAAGtI,cAAc,SAAdA,cAAc,kBAAAmI,sBAAA,CAAdnI,cAAc,CAAE0H,SAAS,UAAAS,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgC1K,OAAO,UAAA2K,sBAAA,iBAAvCA,sBAAA,CAAyCV,cAAc,CACtF,KAAM,CAAAY,kBAAkB,CAAGtG,QAAQ,EAAIqG,sBAAsB,CAE7D,GAAG,CAACC,kBAAkB,CAAE,MAAO,KAAI,CAEnC,GAAIN,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACCpK,IAAA,CAACjB,aAAa,EACb4L,OAAO,CAAC,MAAM,CACdxG,KAAK,CAAEA,KAAK,CAACsB,MAAO,CACpB2C,QAAQ,CAAC,QAAQ,CACjBwC,UAAU,CAAE7G,WAAW,CAAG,CAAE,CAC5B8G,EAAE,CAAE,CAAEtC,eAAe,CAAE,aAAa,CAAEuC,OAAO,CAAC,sBAAsB,CAAG,+BAA+B,CAAE,CACrFvC,eAAe,CAAEtE,aAAe;AAClC,CAAG,CAAE,CACtB8G,UAAU,cAAE/K,IAAA,CAAChB,MAAM,EAAC8H,KAAK,CAAE,CAAEkE,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAEjL,IAAA,CAAChB,MAAM,EAAC8H,KAAK,CAAE,CAAEkE,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACM,GAAIZ,gBAAgB,GAAK,aAAa,CAAE,CAC7C,mBACapK,IAAA,CAACnB,GAAG,EAACgM,EAAE,CAAE,CAACC,OAAO,CAAC,sBAAsB,CAACI,OAAO,CAAE,MAAM,CACnEC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,QAAQ,CACtBC,GAAG,CAAE,KAAK,CAAE,CAAAnC,QAAA,CAGIoC,KAAK,CAACC,IAAI,CAAC,CAAE9F,MAAM,CAAEtB,KAAK,CAACsB,MAAO,CAAC,CAAC,CAAC2D,GAAG,CAAC,CAACG,CAAC,CAAEzE,KAAK,gBACjD9E,IAAA,QAEE8G,KAAK,CAAE,CACL0E,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACblD,eAAe,CAAEzD,KAAK,GAAKf,WAAW,CAAG,CAAC,CAAGE,aAAa,CAAG,SAAS,CAAE;AACxEyH,YAAY,CAAE,OAChB,CAAE,EANG5G,KAON,CACF,CAAC,CAED,CAAC,CAEpB,CACA,GAAIsF,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCpK,IAAA,CAACnB,GAAG,EAACgM,EAAE,CAAE,CAACC,OAAO,CAAC,sBAAsB,CAACI,OAAO,CAAE,MAAM,CACxDC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,YACd,CAAE,CAAAlC,QAAA,cACDhJ,KAAA,CAACpB,UAAU,EAAC+L,EAAE,CAAE,CAACc,QAAQ,CAAC,MAAM,CAAElD,KAAK,CAAExE,aAAa,CAAE,CAAAiF,QAAA,EAAE,OACnD,CAACnF,WAAW,CAAC,MAAI,CAACI,KAAK,CAACsB,MAAM,EACzB,CAAC,CACT,CAAC,CAER,CAEA,GAAI2E,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACCpK,IAAA,CAACnB,GAAG,EAACgM,EAAE,CAAE,CAAKW,KAAK,CAAE,mBAAmB,CACvCV,OAAO,CAAE,WAAW,CAAE,CAAA5B,QAAA,cACtBlJ,IAAA,CAAClB,UAAU,EAAC6L,OAAO,CAAC,OAAO,CAAAzB,QAAA,cAC1BlJ,IAAA,CAACf,cAAc,EACd0L,OAAO,CAAC,aAAa,CACrB9F,KAAK,CAAEzC,QAAS,CACKyI,EAAE,CAAE,CAAC,0BAA0B,CAAE,CAC7BtC,eAAe,CAAEtE,aAAe;AAClC,CAAE,CAAE,CAC3B,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,KAAM,CAAA6C,KAAK,EAAApG,qBAAA,CAAGoD,UAAU,CAAC+F,SAAS,CAAC+B,IAAI,CAAEC,IAAQ,EAAKA,IAAI,CAACC,QAAQ,GAAK/H,WAAW,CAAC,UAAArD,qBAAA,iBAAtEA,qBAAA,CAAwEqL,MAE1E,CACb;AACApN,SAAS,CAAC,IAAM,CACf,GAAI,CAAAmI,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEkF,QAAQ,IAAK,WAAW,CAAE,CACpCpF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACmF,QAAQ,CAAG,QAAQ,CACxC,CAAC,IAAM,CACNrF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACmF,QAAQ,CAAG,EAAE,CAClC,CAEA;AACA,MAAO,IAAM,CACZrF,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACmF,QAAQ,CAAG,EAAE,CAClC,CAAC,CACF,CAAC,CAAE,CAACnF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEkF,QAAQ,CAAC,CAAC,CAAE;AAEtB,mBACC9L,KAAA,CAAAE,SAAA,EAAA8I,QAAA,eAcClJ,IAAA,QAAKiJ,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACxChJ,KAAA,CAACrB,GAAG,EACHoK,SAAS,CAAC,WAAW,CACrB4B,EAAE,CAAE,CACHC,OAAO,CAAE,CAAAhE,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEoF,OAAO,IAAKC,SAAS,EAAI,CAAArF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEoF,OAAO,IAAK,IAAI,CAAG,GAAGpF,KAAK,CAACoF,OAAO,IAAI,CAAG,MAAM,CAChGE,SAAS,CAAGtF,KAAK,EAAK,CAAAA,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEkF,QAAQ,IAAK,WAAY,CAAK,MAAM,CAAG,iCAAiC,CACrGK,SAAS,CACP,GAAG,CAAAvF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwF,UAAU,GAAI,CAAC,YAAY,CAAAxF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyF,WAAW,GAAI,SAAS,aAAa,CAElFC,WAAW,CACV,GAAG,CAAA1F,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwF,UAAU,GAAI,CAAC,YAAY,CAAAxF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyF,WAAW,GAAI,SAAS,aAAa,CAElFE,UAAU,CACX,GAAG,CAAA3F,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwF,UAAU,GAAI,CAAC,YAAY,CAAAxF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyF,WAAW,GAAI,SAAS,aAAa,CAElFG,YAAY,CAAE,GAAG,CAAA5F,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwF,UAAU,GAAI,CAAC,YAAY,CAAAxF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyF,WAAW,GAAI,SAAS,aAAa,CAE/FhE,eAAe,CAAE,GAAGzB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE6F,eAAe,cAAc,EAAI,SAAS,CACrEvE,QAAQ,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEkF,QAAQ,GAAI,WAAW,CACxCY,MAAM,CAAE,CAAA9F,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE+F,MAAM,GAAI,IAC1B,CAAE,CACF7E,EAAE,CAAC,aAAa,CAChBhD,GAAG,CAAE3C,aAAc,CAAA6G,QAAA,EAIrBpG,SAAS,CAAGsG,GAAG,CAAC,CAACC,GAAQ,CAAEnE,QAAgB,gBAC3ChF,KAAA,CAACrB,GAAG,EAAgBoK,SAAS,CAAC,WAAW,CAAAC,QAAA,EACtCG,GAAG,CAACD,GAAG,CAAC,CAAC0D,QAAa,CAAE3D,aAAqB,gBAC5CjJ,KAAA,CAACrB,GAAG,EAAqBoK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC1DlJ,IAAA,QACEiJ,SAAS,CAAC,iBAAiB,CAC3BnC,KAAK,CAAE,CACLyB,eAAe,CACrB,CAACuE,QAAQ,CAAClI,IAAI,GAAK,KAAK,EAAIkI,QAAQ,CAAClI,IAAI,GAAK,WAAW,GAAKrC,YAAY,CAAGA,YAAY,CAAG,EACxF,CAAE,CAAA2G,QAAA,CAEP4D,QAAQ,CAACjI,KAAK,CACN,CAAC,CACHiI,QAAQ,CAAClI,IAAI,GAAK,QAAQ,eACzB5E,IAAA,CAACpB,UAAU,EACTqK,SAAS,CAAC,eAAe,CACzB8D,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAMzG,cAAc,CAACrB,QAAQ,CAAEiE,aAAa,CAAE,CAAAD,QAAA,cAEvDlJ,IAAA,CAACb,yBAAyB,GAAE,CAAC,CACnB,CACb,GAlBKgK,aAmBL,CACN,CAAC,cAGAjJ,KAAA,CAACrB,GAAG,EAACqM,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAAAjC,QAAA,EACzCG,GAAG,CAAChE,IAAI,CAAEC,IAAS,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAC,cAC9C5E,IAAA,CAACH,OAAO,EAACoN,KAAK,CAAC,4BAA4B,CAACC,SAAS,CAAC,QAAQ,CAAAhE,QAAA,cACzDlJ,IAAA,SAAAkJ,QAAA,cACElJ,IAAA,CAACpB,UAAU,EACToO,OAAO,CAAEA,CAAA,GAAM,CACb7F,cAAc,CAAC,IAAI,CAAC,CACpBE,kBAAkB,CAACnC,QAAQ,CAAC,CAC9B,CAAE,CACF+D,SAAS,CAAC,eAAe,CACzB8D,IAAI,CAAC,OAAO,CACrBI,QAAQ,CAAE,IAAK,CAAAjE,QAAA,cAENlJ,IAAA,CAACd,OAAO,GAAE,CAAC,CACD,CAAC,CACT,CAAC,CACJ,CAAC,cAEVc,IAAA,CAACpB,UAAU,EACXoO,OAAO,CAAEA,CAAA,GAAM,CACb7F,cAAc,CAAC,IAAI,CAAC,CACpBE,kBAAkB,CAACnC,QAAQ,CAAC,CAC9B,CAAE,CACF+D,SAAS,CAAC,eAAe,CACzB8D,IAAI,CAAC,OAAO,CACZI,QAAQ,CAAE9D,GAAG,CAAChE,IAAI,CAAEC,IAAS,EAAKA,IAAI,CAACV,IAAI,GAAK,QAAQ,CAAE,CAAAsE,QAAA,cAE1DlJ,IAAA,CAACd,OAAO,GAAE,CAAC,CACE,CACb,cAEIc,IAAA,CAACpB,UAAU,EACTiM,EAAE,CAAE,CACR;AACAC,OAAO,CAAC,KAAK,CACPsB,SAAS,CAAE,iCAAiC,CAC5CgB,UAAU,CAAE,KAAK,CACjBC,UAAU,CAAE,iBAAiB,CAC7BC,MAAM,CAAE,gBAAgB,CACxBV,MAAM,CAAC,QAAQ,CACf1B,OAAO,CAAE5I,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEiL,UAAU,CAAG,MAAM,CAAG,MAAQ;AACtD,CAAE,CAAArE,QAAA,cAEPlJ,IAAA,CAACN,SAAS,EAAEmL,EAAE,CAAE,CAAC2C,IAAI,CAAC,GAAG,CAAC/E,KAAK,CAAC,MAAO,CAAE,CAAI,CAAC,CAC/B,CAAC,EACV,CAAC,CACR1G,gBAAgB,EAAIC,WAAW,eAChChC,IAAA,CAACF,UAAU,EACTkC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,WAAW,CAAEA,WAAY,CACzB,CACD,CACCgF,WAAW,EAAIE,eAAe,GAAKlC,QAAQ,eAC1ClF,IAAA,CAACnB,GAAG,EACF4O,YAAY,CAAEA,CAAA,GAAMtG,cAAc,CAAC,IAAI,CAAE,CACzCuG,YAAY,CAAEA,CAAA,GAAMvG,cAAc,CAAC,KAAK,CAAE,CAC1C8B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAE9BlJ,IAAA,CAACnB,GAAG,EAACoK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpChJ,KAAA,CAACrB,GAAG,EACFqM,OAAO,CAAC,MAAM,CACdyC,aAAa,CAAC,KAAK,CACnBxC,UAAU,CAAC,QAAQ,CACnBN,EAAE,CAAE,CAAE+C,MAAM,CAAE,SAAS,CAACvC,GAAG,CAAG,MAAM,CAACD,YAAY,CAAC,QAAQ,CAACI,KAAK,CAAC,MAAO,CAAE,CAE1EwB,OAAO,CAAEA,CAAA,GAAMtF,oBAAoB,CAACxC,QAAQ,CAAE,QAAQ,CAAE,CAAAgE,QAAA,eAEhElJ,IAAA,CAACZ,IAAI,GAAE,CAAC,cACTY,IAAA,CAAClB,UAAU,EAAC6L,OAAO,CAAC,SAAS,CAAAzB,QAAA,CAAC,QAAM,CAAY,CAAC,EACrC,CAAC,CACH,CAAC,CACH,CACN,GAlGOhE,QAmGL,CACN,CAAC,CACD,CAAC,CAAA/C,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE0L,SAAS,IAAK,MAAM,EAAI,CAAA1L,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE0L,SAAS,IAAK,cAAc,gBAEtF7N,IAAA,CAACnB,GAAG,EACJgM,EAAE,CAAE,CACF,IAAIT,gBAAgB,GAAK,QAAQ,EAAI,CACtCc,OAAO,CAAE,MAAM,CACfE,YAAY,CAAE,QAAQ,CACtBD,UAAU,CAAC,QACV,CAAC,CACH,CAAE,CAAAjC,QAAA,CAEC/E,KAAK,CAACsB,MAAM,EAAI,CAAC,cACAzF,IAAA,CAAAI,SAAA,EAAA8I,QAAA,CACKmB,cAAc,CAAC,CAAC,CACnB,CAAC,CAEH,IACH,CACR,CAAC,CAGJ/C,eAAe,eAAItH,IAAA,CAACP,WAAW,EAACqO,YAAY,CAAEA,CAAA,GAAM,CAAC,CAAE,CAAE,CAAC,EACvD,CAAC,CACF,CAAC,CACLrL,cAAc,eAAKzC,IAAA,CAACJ,cAAc,GAAE,CAAC,EAmBrC,CAAC,CAEL,CAAC,CAED,cAAe,CAAAS,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}