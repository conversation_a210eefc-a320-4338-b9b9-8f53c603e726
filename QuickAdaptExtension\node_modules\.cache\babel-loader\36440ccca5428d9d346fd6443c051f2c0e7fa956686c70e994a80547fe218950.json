{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{Box,Button,Typography,IconButton,DialogActions,MobileStepper,LinearProgress}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const BannerStepPreview=_ref=>{var _initialGuideData$Gui,_bannerSteps$Canvas2,_bannerSteps$TextFiel,_bannerSteps$ButtonSe,_bannerSteps$ButtonSe2,_Modal$DismissOption,_savedGuideData$Guide,_savedGuideData$Guide2,_savedGuideData$Guide3,_initialGuideData$Gui2,_initialGuideData$Gui3,_initialGuideData$Gui4,_initialGuideData$Gui5,_initialGuideData$Gui6,_initialGuideData$Gui7,_initialGuideData$Gui8,_initialGuideData$Gui9;let{showBannerenduser,setShowBannerenduser,initialGuideData,backgroundC,totalSteps,savedGuideData,progress,onClose}=_ref;const{btnBgColor,btnTextColor,btnBorderColor,setCurrentStep,currentStep,selectedOption,ProgressColor,setBposition}=useDrawerStore(state=>state);const[showBanner,setShowBanner]=useState(true);const{setImageSrc,imageSrc,htmlContent,sectionColor}=useDrawerStore(state=>state);const bannerSteps=(_initialGuideData$Gui=initialGuideData.GuideStep)===null||_initialGuideData$Gui===void 0?void 0:_initialGuideData$Gui[currentStep-1];// Update the position when the component mounts or when the canvas position changes\nuseEffect(()=>{var _bannerSteps$Canvas;// Get the current Canvas position from the step\nconst currentPosition=bannerSteps===null||bannerSteps===void 0?void 0:(_bannerSteps$Canvas=bannerSteps.Canvas)===null||_bannerSteps$Canvas===void 0?void 0:_bannerSteps$Canvas.Position;if(currentPosition){// Update the position in the store\nsetBposition(currentPosition);}},[bannerSteps===null||bannerSteps===void 0?void 0:(_bannerSteps$Canvas2=bannerSteps.Canvas)===null||_bannerSteps$Canvas2===void 0?void 0:_bannerSteps$Canvas2.Position,setBposition]);const renderHtmlSnippet=snippet=>{if(!snippet)return\"Sample ...\";// Return an empty string if snippet is null or undefined.\nreturn snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;});};const image=imageSrc;const isBase64=url=>url.startsWith(\"data:image/\");const textField=(bannerSteps===null||bannerSteps===void 0?void 0:(_bannerSteps$TextFiel=bannerSteps.TextFieldProperties)===null||_bannerSteps$TextFiel===void 0?void 0:_bannerSteps$TextFiel[0])||{};const{Text:textFieldText,Alignment,Hyperlink,Emoji,TextProperties}=textField;const{Bold,Italic,TextColor}=TextProperties||{};const customButton=(bannerSteps===null||bannerSteps===void 0?void 0:(_bannerSteps$ButtonSe=bannerSteps.ButtonSection)===null||_bannerSteps$ButtonSe===void 0?void 0:(_bannerSteps$ButtonSe2=_bannerSteps$ButtonSe.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_bannerSteps$ButtonSe2===void 0?void 0:_bannerSteps$ButtonSe2.reduce((acc,curr)=>acc.concat(curr),[]))||[];const handlePrevious=()=>{if(currentStep>1){setCurrentStep(currentStep-1);}};const handleContinue=()=>{if(currentStep<totalSteps){setCurrentStep(currentStep+1);}};const handleButtonClick=action=>{if(action.Action===\"open-url\"||action.Action===\"openurl\"||action.Action===\"open\"){window.open(action.TargetUrl);//onContinue();\n}else if(action.Action===\"start-interaction\"){// onContinue();\n// setOverlayValue(false);\n}else if(action.Action===\"close\"){// onClose();\n// setOverlayValue(false);\n}else{if(action.Action==\"Previous\"||action.Action==\"previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"){handleContinue();}else if(action.Action==\"Restart\"){// Reset to the first step\nsetCurrentStep(1);}}};const designProps=(bannerSteps===null||bannerSteps===void 0?void 0:bannerSteps.Design)||{};const IconColor=designProps.IconColor||\"#000\";const IconOpacity=designProps.QuietIcon?0.5:1.0;const canvas=(bannerSteps===null||bannerSteps===void 0?void 0:bannerSteps.Canvas)||{};const BackgroundColor=(canvas===null||canvas===void 0?void 0:canvas.BackgroundColor)||\"#f1f1f7\";const Width=canvas.Width||\"100%\";const Radius=canvas.Radius||\"0\";const Padding=canvas.Padding||\"10\";const BorderSize=canvas.BorderSize||\"2\";const BorderColor=canvas.BorderColor||\"#f1f1f7\";const Position=\"absolute\";const zindex=canvas.Zindex||\"999999\";const Modal=bannerSteps===null||bannerSteps===void 0?void 0:bannerSteps.Modal;const isCloseDisabled=(_Modal$DismissOption=Modal===null||Modal===void 0?void 0:Modal.DismissOption)!==null&&_Modal$DismissOption!==void 0?_Modal$DismissOption:false;const Design=(bannerSteps===null||bannerSteps===void 0?void 0:bannerSteps.Design)||{};const htmlSnippet=(bannerSteps===null||bannerSteps===void 0?void 0:bannerSteps.HtmlSnippet)||\"\";// Apply overflow hidden to body when canvas position is \"Cover Top\"\nuseEffect(()=>{if((canvas===null||canvas===void 0?void 0:canvas.Position)===\"Cover Top\"){document.body.style.overflow=\"hidden\";}else{document.body.style.overflow=\"\";}// Cleanup function to restore overflow when component unmounts\nreturn()=>{document.body.style.overflow=\"\";};},[canvas===null||canvas===void 0?void 0:canvas.Position]);// Re-run when canvas position changes\nconst countLinesFromHtml=html=>{const paragraphCount=(html.match(/<p>/g)||[]).length;const brCount=(html.match(/<br\\s*\\/?>/g)||[]).length;return paragraphCount>0?paragraphCount-1:0;};// const renderHtmlSnippet = (snippet: string) => {\n// \treturn parse(snippet, {\n// \t\treplace: (domNode: any) => {\n// \t\t\tif (domNode.name === \"font\") {\n// \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\n// \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\n// \t\t\t\treturn (\n// \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\n// \t\t\t\t\t\t{domToReact(domNode.children)}\n// \t\t\t\t\t</span>\n// \t\t\t\t);\n// \t\t\t}\n// \t\t\treturn undefined;\n// \t\t},\n// \t});\n// };\nconst enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[0])===null||_savedGuideData$Guide2===void 0?void 0:(_savedGuideData$Guide3=_savedGuideData$Guide2.Tooltip)===null||_savedGuideData$Guide3===void 0?void 0:_savedGuideData$Guide3.EnableProgress)||false;let progressTop=0;function getProgressTemplate(selectedOption){var _savedGuideData$Guide4,_savedGuideData$Guide5,_savedGuideData$Guide6;if(selectedOption===1){progressTop=10;return\"dots\";}else if(selectedOption===2){progressTop=7;return\"linear\";}else if(selectedOption===3){progressTop=7;return\"BreadCrumbs\";}else if(selectedOption===4){progressTop=14;return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide4=savedGuideData.GuideStep)===null||_savedGuideData$Guide4===void 0?void 0:(_savedGuideData$Guide5=_savedGuideData$Guide4[0])===null||_savedGuideData$Guide5===void 0?void 0:(_savedGuideData$Guide6=_savedGuideData$Guide5.Tooltip)===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:totalSteps,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",padding:\"8px 0 0 0 !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{padding:\"8px 0 0 0 !important\",display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\"},children:Array.from({length:totalSteps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'14px',height:'4px',backgroundColor:index===currentStep-1?ProgressColor:'#e0e0e0',// Active color and inactive color\nborderRadius:'100px'}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{padding:\"8px 0 0 0 !important\",display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{fontSize:\"12px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",totalSteps]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{sx:{width:\"calc(50% - 410px)\",padding:\"8px 0 0 0\"},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{'& .MuiLinearProgress-bar':{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};return/*#__PURE__*/_jsx(Box,{sx:{// position: \"relative\",\n// top: \"55px\"\n},className:\"qadpt-container\",children:showBanner&&/*#__PURE__*/_jsxs(Box,{className:\"qadpt-boxpre\",id:\"guide-popup\",sx:{// position: \"relative\",\n// top: \"55px\",\n// ...BannerWrapper,\n//top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\nleft:\"50%\",height:\"auto\",marginTop:`${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(enableProgress?progressTop:0)+(customButton&&customButton.length>0?4:0)+countLinesFromHtml(textFieldText)*10}px`,//\"29px\",\ntransform:\"translate(-50%, -50%)\",backgroundColor:sectionColor,//width: Width,\nmaxWidth:\"100%\",//borderRadius: Radius,\npadding:`${Padding}px`,//borderWidth: \"2px\",\n//border: `${BorderSize}px solid ${BorderColor}`,\nboxShadow:Object.keys(canvas).length==0?\"0px 1px 15px rgba(0, 0, 0, 0.7)\":(canvas===null||canvas===void 0?void 0:canvas.Position)==\"Cover Top\"?\"0px 1px 15px rgba(0, 0, 0, 0.7)\":\"none\",//(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\nposition:Position,zIndex:zindex,background:`${BackgroundColor?BackgroundColor:'#f1f1f7'} !important`,// border: `${BorderSize}px solid ${BorderColor}`,\nborderTop:`${BorderSize}px solid ${BorderColor} !important`,borderRight:`${BorderSize}px solid ${BorderColor} !important`,borderLeft:`${BorderSize}px solid ${BorderColor} !important`,borderBottom:`${BorderSize}px solid ${BorderColor} !important`},children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-row\",sx:{display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsxs(Box,{sx:{// margin: \"8px \",\n//bottom: \"45px\",\nposition:\"relative\",display:\"flex\",alignItems:\"center\",placeContent:\"center\",width:\"100%\",\"& .MuiTypography-root\":{width:\"100%\",margin:\"0\"}},children:[Hyperlink?/*#__PURE__*/_jsx(Typography,{component:\"a\",href:Hyperlink,target:\"_blank\"// Open link in a new tab\n,rel:\"noopener noreferrer\"// Security measure when using target=\"_blank\"\n,sx:{color:TextColor,padding:\"5px 2px\",textAlign:Alignment||\"center\",marginTop:1,textDecoration:\"underline\"},dangerouslySetInnerHTML:{__html:renderHtmlSnippet(htmlSnippet)}}):/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview qadpt-rte\",sx:{color:TextColor,textAlign:Alignment,marginTop:1,padding:\"5px 2px\",whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",\"& p\":{margin:\"0\"}},dangerouslySetInnerHTML:{__html:renderHtmlSnippet(textFieldText)}}),Emoji&&/*#__PURE__*/_jsx(Typography,{component:\"span\",sx:{fontWeight:Bold?\"bold\":\"normal\",fontStyle:Italic?\"italic\":\"normal\",color:TextColor,padding:\"5px 2px\",textAlign:Alignment?Alignment:\"center\",mt:1},children:Emoji}),customButton&&customButton.some(button=>button.ButtonName&&button.ButtonName.trim()!==\"\")&&/*#__PURE__*/_jsx(DialogActions,{sx:{justifyContent:\"center\",padding:\"0 !important\",height:\"40px\"},children:customButton.map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonClick(button.ButtonAction),variant:\"contained\",style:{backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#5F9EA0\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#ffffff\",border:(_button$ButtonPropert3=button.ButtonProperties)!==null&&_button$ButtonPropert3!==void 0&&_button$ButtonPropert3.ButtonBorderColor?`2px solid ${button.ButtonProperties.ButtonBorderColor}`:\"none\",margin:\"0 5px\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||15,width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:\"6px 16px\",textTransform:\"none\",borderRadius:\"20px\",lineHeight:\"22px\"},sx:{\"&:hover\":{filter:\"brightness(1.2)\"}},children:button.ButtonName},button.Id||`button-${index}`);})}),Hyperlink&&/*#__PURE__*/_jsx(Typography,{component:\"a\",href:Hyperlink,target:\"_blank\",rel:\"noopener noreferrer\",sx:{color:TextColor,textAlign:Alignment||\"left\",mt:1,textDecoration:\"underline\"},children:renderHtmlSnippet(textFieldText)}),image&&/*#__PURE__*/_jsx(Box,{component:\"a\",href:((_initialGuideData$Gui2=initialGuideData.GuideStep[0])===null||_initialGuideData$Gui2===void 0?void 0:(_initialGuideData$Gui3=_initialGuideData$Gui2.ImageProperties)===null||_initialGuideData$Gui3===void 0?void 0:_initialGuideData$Gui3.Hyperlink)||\"#\",target:\"_blank\",rel:\"noopener noreferrer\",sx:{textAlign:Alignment||\"center\"},children:/*#__PURE__*/_jsx(Box,{component:\"img\",src:isBase64(image)?image:image,sx:{maxHeight:((_initialGuideData$Gui4=initialGuideData.GuideStep[0])===null||_initialGuideData$Gui4===void 0?void 0:(_initialGuideData$Gui5=_initialGuideData$Gui4.ImageProperties)===null||_initialGuideData$Gui5===void 0?void 0:_initialGuideData$Gui5.MaxImageHeight)||\"auto\",objectFit:((_initialGuideData$Gui6=initialGuideData.GuideStep[0])===null||_initialGuideData$Gui6===void 0?void 0:(_initialGuideData$Gui7=_initialGuideData$Gui6.ImageProperties)===null||_initialGuideData$Gui7===void 0?void 0:(_initialGuideData$Gui8=_initialGuideData$Gui7.UploadedImages)===null||_initialGuideData$Gui8===void 0?void 0:(_initialGuideData$Gui9=_initialGuideData$Gui8[0])===null||_initialGuideData$Gui9===void 0?void 0:_initialGuideData$Gui9.Fit)||\"contain\",display:\"block\",margin:\"0 auto\"}})})]}),\" \",isCloseDisabled&&/*#__PURE__*/_jsx(IconButton,{sx:{// position: \"fixed\",\nboxShadow:\"rgba(0, 0, 0, 0.15) 0px 4px 8px\",marginLeft:\"2px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"3px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:\"1\",color:\"#000\"}})})]}),/*#__PURE__*/_jsx(Box,{sx:{...(progressTemplate===\"linear\"&&{display:\"flex\",placeContent:\"center\",alignItems:\"center\"})},children:totalSteps>=1&&enableProgress?/*#__PURE__*/_jsx(_Fragment,{children:renderProgress()}):null})]})});};export default BannerStepPreview;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Typography", "IconButton", "DialogActions", "MobileStepper", "LinearProgress", "CloseIcon", "useDrawerStore", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "BannerStepPreview", "_ref", "_initialGuideData$Gui", "_bannerSteps$Canvas2", "_bannerSteps$TextFiel", "_bannerSteps$ButtonSe", "_bannerSteps$ButtonSe2", "_Modal$DismissOption", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_initialGuideData$Gui2", "_initialGuideData$Gui3", "_initialGuideData$Gui4", "_initialGuideData$Gui5", "_initialGuideData$Gui6", "_initialGuideData$Gui7", "_initialGuideData$Gui8", "_initialGuideData$Gui9", "showBanneren<PERSON>er", "setShowBannerenduser", "initialGuideData", "backgroundC", "totalSteps", "savedGuideData", "progress", "onClose", "btnBgColor", "btnTextColor", "btnBorderColor", "setCurrentStep", "currentStep", "selectedOption", "ProgressColor", "setBposition", "state", "showBanner", "setShowBanner", "setImageSrc", "imageSrc", "htmlContent", "sectionColor", "bannerSteps", "GuideStep", "_bannerSteps$Canvas", "currentPosition", "<PERSON><PERSON>", "Position", "renderHtmlSnippet", "snippet", "replace", "match", "p1", "p2", "p3", "image", "isBase64", "url", "startsWith", "textField", "TextFieldProperties", "Text", "textFieldText", "Alignment", "Hyperlink", "<PERSON><PERSON><PERSON>", "TextProperties", "Bold", "Italic", "TextColor", "customButton", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "handlePrevious", "handleContinue", "handleButtonClick", "action", "Action", "window", "open", "TargetUrl", "designProps", "Design", "IconColor", "IconOpacity", "QuietIcon", "canvas", "BackgroundColor", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Padding", "BorderSize", "BorderColor", "zindex", "Zindex", "Modal", "isCloseDisabled", "DismissOption", "htmlSnippet", "HtmlSnippet", "document", "body", "style", "overflow", "countLinesFromHtml", "html", "paragraphCount", "length", "brCount", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "progressTop", "getProgressTemplate", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "steps", "position", "activeStep", "sx", "backgroundColor", "padding", "backButton", "visibility", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "children", "Array", "from", "_", "index", "width", "height", "borderRadius", "fontSize", "color", "value", "className", "id", "left", "marginTop", "parseInt", "transform", "max<PERSON><PERSON><PERSON>", "boxShadow", "Object", "keys", "zIndex", "background", "borderTop", "borderRight", "borderLeft", "borderBottom", "margin", "component", "href", "target", "rel", "textAlign", "textDecoration", "dangerouslySetInnerHTML", "__html", "whiteSpace", "wordBreak", "fontWeight", "fontStyle", "mt", "some", "ButtonName", "trim", "justifyContent", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "onClick", "ButtonAction", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "border", "ButtonBorderColor", "FontSize", "textTransform", "lineHeight", "filter", "ImageProperties", "src", "maxHeight", "MaxImageHeight", "objectFit", "UploadedImages", "Fit", "marginLeft", "zoom"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/tours/BannerStepPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Button, Typography, IconButton, DialogActions, MobileStepper, LinearProgress } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { CustomIconButton } from \"../Bannerspreview/Button\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\n\r\n\r\nconst BannerStepPreview = ({ showBannerenduser, setShowBannerenduser, initialGuideData, backgroundC ,totalSteps,savedGuideData,\r\n\tprogress, onClose}: any) => {\r\n\tconst {\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tsetCurrentStep,\r\n\t\tcurrentStep,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t\tsetBposition,\r\n\r\n\t} = useDrawerStore((state:any) => state);\r\n\tconst [showBanner, setShowBanner] = useState(true);\r\n\tconst { setImageSrc, imageSrc, htmlContent, sectionColor } = useDrawerStore((state: any) => state);\r\n\tconst bannerSteps = initialGuideData.GuideStep?.[currentStep - 1];\r\n\t// Update the position when the component mounts or when the canvas position changes\r\n\tuseEffect(() => {\r\n\t\t// Get the current Canvas position from the step\r\n\t\tconst currentPosition = bannerSteps?.Canvas?.Position;\r\n\t\tif (currentPosition) {\r\n\t\t\t// Update the position in the store\r\n\t\t\tsetBposition(currentPosition);\r\n\r\n\t\t}\r\n\t}, [bannerSteps?.Canvas?.Position, setBposition]);\r\n\tconst renderHtmlSnippet = (snippet: string | undefined | null) => {\r\n\t\tif (!snippet) return \"Sample ...\"; // Return an empty string if snippet is null or undefined.\r\n\t\treturn snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\r\n\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t});\r\n\t};\r\n\tconst image = imageSrc;\r\n\tconst isBase64 = (url: string) => url.startsWith(\"data:image/\");\r\n\tconst textField = bannerSteps?.TextFieldProperties?.[0] || {};\r\n\tconst { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;\r\n\tconst { Bold, Italic, TextColor } = TextProperties || {};\r\n\tconst customButton =\r\n    bannerSteps?.ButtonSection\r\n        ?.map((section: any) =>\r\n            section.CustomButtons.map((button: any) => ({\r\n                ...button,\r\n                ContainerId: section.Id, // Attach the container ID for grouping\r\n            }))\r\n        )\r\n\t\t\t?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [];\r\n\r\n\t\tconst handlePrevious = () => {\r\n\t\t\tif (currentStep > 1) {\r\n\t\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\t}\r\n\t\t};\r\n\tconst handleContinue = () =>\r\n\t{\r\n\t\tif (currentStep < totalSteps) {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\r\n\r\n\r\n\t\t}\r\n\t\t}\r\n\tconst handleButtonClick = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"openurl\" || action.Action === \"open\") {\r\n\t\t\twindow.open(action.TargetUrl);\r\n\t\t\t//onContinue();\r\n\t\t} else if (action.Action === \"start-interaction\") {\r\n\t\t\t// onContinue();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else if (action.Action === \"close\") {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tif (action.Action == \"Previous\" || action.Action == \"previous\") {\r\n                handlePrevious();\r\n            }\r\n            else if (action.Action == \"Next\" || action.Action == \"next\") {\r\n                handleContinue();\r\n            }\r\n            else if (action.Action == \"Restart\") {\r\n                // Reset to the first step\r\n                setCurrentStep(1);\r\n            }\r\n\t\t}\r\n\t};\r\n\tconst designProps = bannerSteps?.Design || {};\r\n\tconst IconColor = designProps.IconColor || \"#000\";\r\n\tconst IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;\r\n\tconst canvas = bannerSteps?.Canvas || {};\r\n\tconst BackgroundColor = canvas?.BackgroundColor || \"#f1f1f7\";\r\n\tconst Width = canvas.Width || \"100%\";\r\n\tconst Radius = canvas.Radius || \"0\";\r\n\tconst Padding = canvas.Padding || \"10\";\r\n\tconst BorderSize = canvas.BorderSize || \"2\";\r\n\tconst BorderColor = canvas.BorderColor || \"#f1f1f7\";\r\n\tconst Position = \"absolute\";\r\n\tconst zindex = canvas.Zindex || \"999999\";\r\n\tconst Modal = bannerSteps?.Modal;\r\n\tconst isCloseDisabled = Modal?.DismissOption ?? false;\r\n\tconst Design = bannerSteps?.Design || {};\r\n\tconst htmlSnippet = bannerSteps?.HtmlSnippet || \"\";\r\n\r\n\t// Apply overflow hidden to body when canvas position is \"Cover Top\"\r\n\tuseEffect(() => {\r\n\t\tif (canvas?.Position === \"Cover Top\") {\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t} else {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t}\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t};\r\n\t}, [canvas?.Position]); // Re-run when canvas position changes\r\n\r\n\tconst countLinesFromHtml = (html: string) => {\r\n\t\tconst paragraphCount = (html.match(/<p>/g) || []).length;\r\n\r\n\t\tconst brCount = (html.match(/<br\\s*\\/?>/g) || []).length;\r\n\r\n\t\treturn paragraphCount>0 ? paragraphCount-1 : 0;\r\n\t};\r\n\r\n\t// const renderHtmlSnippet = (snippet: string) => {\r\n\t// \treturn parse(snippet, {\r\n\t// \t\treplace: (domNode: any) => {\r\n\t// \t\t\tif (domNode.name === \"font\") {\r\n\t// \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\r\n\t// \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\r\n\t// \t\t\t\treturn (\r\n\t// \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\r\n\t// \t\t\t\t\t\t{domToReact(domNode.children)}\r\n\t// \t\t\t\t\t</span>\r\n\t// \t\t\t\t);\r\n\t// \t\t\t}\r\n\t// \t\t\treturn undefined;\r\n\t// \t\t},\r\n\t// \t});\r\n\t// };\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\tlet progressTop = 0;\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\tprogressTop = 10;\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\tprogressTop = 7;\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\tprogressTop = 7;\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\tprogressTop = 14;\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\", padding:\"8px 0 0 0 !important\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: totalSteps }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\",\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{fontSize:\"12px\", color: ProgressColor}}>\r\n\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{    width: \"calc(50% - 410px)\",\r\n\t\t\t\tpadding: \"8px 0 0 0\"}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n                            sx={{'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<Box sx={{\r\n\t\t\t// position: \"relative\",\r\n\t\t\t// top: \"55px\"\r\n\t\t}} className=\"qadpt-container\">\r\n\t\t\t{showBanner && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-boxpre\"\r\n\t\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t// position: \"relative\",\r\n\t\t\t\t\t\t// top: \"55px\",\r\n\t\t\t\t\t\t// ...BannerWrapper,\r\n\t\t\t\t\t\t//top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\r\n\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmarginTop: `${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(enableProgress ? progressTop : 0)+(customButton && customButton.length > 0? 4 : 0)+(countLinesFromHtml(textFieldText)*10)}px`,//\"29px\",\r\n\t\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor,\r\n\t\t\t\t\t\t//width: Width,\r\n\t\t\t\t\t\tmaxWidth:\"100%\",\r\n\t\t\t\t\t\t//borderRadius: Radius,\r\n\t\t\t\t\t\tpadding: `${Padding}px`,\r\n\t\t\t\t\t\t//borderWidth: \"2px\",\r\n\t\t\t\t\t\t//border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tboxShadow: Object.keys(canvas).length == 0? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : canvas?.Position == \"Cover Top\" ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",//(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\r\n\t\t\t\t\t\tposition: Position,\r\n\t\t\t\t\t\tzIndex: zindex,\r\n\r\n\t\t\t\t\t\tbackground:  `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important` ,\r\n\t\t\t\t\t\t// border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t  `${BorderSize}px solid ${BorderColor} !important`,\r\n\r\n\t\t\t\t\tborderBottom: `${BorderSize}px solid ${BorderColor} !important`,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box className=\"qadpt-row\" sx={{  display: \"flex\", alignItems: \"center\"}}>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// margin: \"8px \",\r\n\t\t\t\t\t\t\t//bottom: \"45px\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\r\n\r\n\t\t\t\t\t\t\t\"& .MuiTypography-root\": {\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\tmargin: \"0\",\r\n\t\t\t\t\t\t\t},\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Display hyperlink  */}\r\n\t\t\t\t\t\t{Hyperlink ? (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\" // Open link in a new tab\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\" // Security measure when using target=\"_blank\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: renderHtmlSnippet(htmlSnippet) }}\r\n\t\t\t\t\t\t\t></Typography>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<Typography \t\t\t\t\t\t\t\t  className=\"qadpt-preview qadpt-rte\"\r\n\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment,\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\"& p\": {\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0\",\r\n\t\t\t\t\t\t\t\t\t  },\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: renderHtmlSnippet(textFieldText) }}\r\n\t\t\t\t\t\t\t\t\t\t></Typography>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{Emoji && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"span\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontWeight: Bold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tfontStyle: Italic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment ? Alignment : \"center\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{Emoji}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{customButton &&\r\n\t\t\t\t\t\t\tcustomButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== \"\") && (\r\n\t\t\t\t\t\t\t\t<DialogActions\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{customButton.map((button: any, index: any) => (\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tkey={button.Id || `button-${index}`}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${button.ButtonProperties.ButtonBorderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || 15,\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"22px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfilter: \"brightness(1.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</DialogActions>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{/* {CustomButton && CustomButton.ButtonName && (\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontSize: CustomButton.ButtonProperties?.FontSize || 14,\r\n\t\t\t\t\t\t\t\t\twidth: CustomButton.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\tpadding: CustomButton.ButtonProperties?.Padding || \"10px\",\r\n\t\t\t\t\t\t\t\t\tcolor: CustomButton.ButtonProperties?.ButtonTextColor,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: CustomButton.ButtonProperties?.ButtonBackgroundColor,\r\n\t\t\t\t\t\t\t\t\t//textAlign: CustomButton.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t//display: \"block\",\r\n\t\t\t\t\t\t\t\t\tmargin: \"8px\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"0px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\tmx: CustomButton.Alignment === \"center\" ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={handleButtonClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{CustomButton.ButtonName}\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t)} */}\r\n\r\n\t\t\t\t\t\t{Hyperlink && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"left\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{renderHtmlSnippet(textFieldText)}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{image && (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={initialGuideData.GuideStep[0]?.ImageProperties?.Hyperlink || \"#\"}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{ textAlign: Alignment || \"center\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc={isBase64(image) ? image : image}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tmaxHeight: initialGuideData.GuideStep[0]?.ImageProperties?.MaxImageHeight || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\tobjectFit: initialGuideData.GuideStep[0]?.ImageProperties?.UploadedImages?.[0]?.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 auto\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>{\" \"}\r\n\t\t\t\t\t{isCloseDisabled && (\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n\t\t\t\t\tmarginLeft: \"2px\",\r\n\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\tzIndex:\"999999\",\r\n\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\tpadding:\"3px !important\"\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\"}}   />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t<Box sx={{\r\n  ...(progressTemplate === \"linear\" && {\r\n\tdisplay: \"flex\",\r\n\tplaceContent: \"center\",\r\n\talignItems:\"center\"\r\n  }),\r\n}}>\r\n\t\t{totalSteps >= 1 && enableProgress ? (\r\n                    <>\r\n                        {renderProgress()}\r\n                    </>\r\n                ) : (\r\n                    null\r\n                )}\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t)}\r\n\r\n\r\n\t\t</Box>\r\n\t);\r\n};\r\n\r\nexport default BannerStepPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,UAAU,CAAEC,aAAa,CAAEC,aAAa,CAAEC,cAAc,KAAQ,eAAe,CACjH,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAEjD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGrD,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EACG,KAAAC,qBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,IADF,CAAEC,iBAAiB,CAAEC,oBAAoB,CAAEC,gBAAgB,CAAEC,WAAW,CAAEC,UAAU,CAACC,cAAc,CAC7HC,QAAQ,CAAEC,OAAY,CAAC,CAAAzB,IAAA,CACvB,KAAM,CACL0B,UAAU,CACVC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,aAAa,CACbC,YAED,CAAC,CAAGzC,cAAc,CAAE0C,KAAS,EAAKA,KAAK,CAAC,CACxC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAAEuD,WAAW,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAGhD,cAAc,CAAE0C,KAAU,EAAKA,KAAK,CAAC,CAClG,KAAM,CAAAO,WAAW,EAAAxC,qBAAA,CAAGmB,gBAAgB,CAACsB,SAAS,UAAAzC,qBAAA,iBAA1BA,qBAAA,CAA6B6B,WAAW,CAAG,CAAC,CAAC,CACjE;AACA/C,SAAS,CAAC,IAAM,KAAA4D,mBAAA,CACf;AACA,KAAM,CAAAC,eAAe,CAAGH,WAAW,SAAXA,WAAW,kBAAAE,mBAAA,CAAXF,WAAW,CAAEI,MAAM,UAAAF,mBAAA,iBAAnBA,mBAAA,CAAqBG,QAAQ,CACrD,GAAIF,eAAe,CAAE,CACpB;AACAX,YAAY,CAACW,eAAe,CAAC,CAE9B,CACD,CAAC,CAAE,CAACH,WAAW,SAAXA,WAAW,kBAAAvC,oBAAA,CAAXuC,WAAW,CAAEI,MAAM,UAAA3C,oBAAA,iBAAnBA,oBAAA,CAAqB4C,QAAQ,CAAEb,YAAY,CAAC,CAAC,CACjD,KAAM,CAAAc,iBAAiB,CAAIC,OAAkC,EAAK,CACjE,GAAI,CAACA,OAAO,CAAE,MAAO,YAAY,CAAE;AACnC,MAAO,CAAAA,OAAO,CAACC,OAAO,CAAC,qCAAqC,CAAE,CAACC,KAAK,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACpF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CAAC,CACH,CAAC,CACD,KAAM,CAAAC,KAAK,CAAGhB,QAAQ,CACtB,KAAM,CAAAiB,QAAQ,CAAIC,GAAW,EAAKA,GAAG,CAACC,UAAU,CAAC,aAAa,CAAC,CAC/D,KAAM,CAAAC,SAAS,CAAG,CAAAjB,WAAW,SAAXA,WAAW,kBAAAtC,qBAAA,CAAXsC,WAAW,CAAEkB,mBAAmB,UAAAxD,qBAAA,iBAAhCA,qBAAA,CAAmC,CAAC,CAAC,GAAI,CAAC,CAAC,CAC7D,KAAM,CAAEyD,IAAI,CAAEC,aAAa,CAAEC,SAAS,CAAEC,SAAS,CAAEC,KAAK,CAAEC,cAAe,CAAC,CAAGP,SAAS,CACtF,KAAM,CAAEQ,IAAI,CAAEC,MAAM,CAAEC,SAAU,CAAC,CAAGH,cAAc,EAAI,CAAC,CAAC,CACxD,KAAM,CAAAI,YAAY,CACf,CAAA5B,WAAW,SAAXA,WAAW,kBAAArC,qBAAA,CAAXqC,WAAW,CAAE6B,aAAa,UAAAlE,qBAAA,kBAAAC,sBAAA,CAA1BD,qBAAA,CACMmE,GAAG,CAAEC,OAAY,EACfA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CACxC,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC7B,CAAC,CAAC,CACN,CAAC,UAAAvE,sBAAA,iBANLA,sBAAA,CAOCwE,MAAM,CAAC,CAACC,GAAU,CAAEC,IAAW,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EAAE,CAElE,KAAM,CAAAE,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAInD,WAAW,CAAG,CAAC,CAAE,CACpBD,cAAc,CAACC,WAAW,CAAG,CAAC,CAAC,CAChC,CACD,CAAC,CACF,KAAM,CAAAoD,cAAc,CAAGA,CAAA,GACvB,CACC,GAAIpD,WAAW,CAAGR,UAAU,CAAE,CAC7BO,cAAc,CAACC,WAAW,CAAG,CAAC,CAAC,CAIhC,CACA,CAAC,CACF,KAAM,CAAAqD,iBAAiB,CAAIC,MAAW,EAAK,CAC1C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,CAAE,CAC5FC,MAAM,CAACC,IAAI,CAACH,MAAM,CAACI,SAAS,CAAC,CAC7B;AACD,CAAC,IAAM,IAAIJ,MAAM,CAACC,MAAM,GAAK,mBAAmB,CAAE,CACjD;AACA;AAAA,CACA,IAAM,IAAID,MAAM,CAACC,MAAM,GAAK,OAAO,CAAE,CACrC;AACA;AAAA,CACA,IAED,CACC,GAAID,MAAM,CAACC,MAAM,EAAI,UAAU,EAAID,MAAM,CAACC,MAAM,EAAI,UAAU,CAAE,CACnDJ,cAAc,CAAC,CAAC,CACpB,CAAC,IACI,IAAIG,MAAM,CAACC,MAAM,EAAI,MAAM,EAAID,MAAM,CAACC,MAAM,EAAI,MAAM,CAAE,CACzDH,cAAc,CAAC,CAAC,CACpB,CAAC,IACI,IAAIE,MAAM,CAACC,MAAM,EAAI,SAAS,CAAE,CACjC;AACAxD,cAAc,CAAC,CAAC,CAAC,CACrB,CACV,CACD,CAAC,CACD,KAAM,CAAA4D,WAAW,CAAG,CAAAhD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEiD,MAAM,GAAI,CAAC,CAAC,CAC7C,KAAM,CAAAC,SAAS,CAAGF,WAAW,CAACE,SAAS,EAAI,MAAM,CACjD,KAAM,CAAAC,WAAW,CAAGH,WAAW,CAACI,SAAS,CAAG,GAAG,CAAG,GAAG,CACrD,KAAM,CAAAC,MAAM,CAAG,CAAArD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEI,MAAM,GAAI,CAAC,CAAC,CACxC,KAAM,CAAAkD,eAAe,CAAG,CAAAD,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEC,eAAe,GAAI,SAAS,CAC5D,KAAM,CAAAC,KAAK,CAAGF,MAAM,CAACE,KAAK,EAAI,MAAM,CACpC,KAAM,CAAAC,MAAM,CAAGH,MAAM,CAACG,MAAM,EAAI,GAAG,CACnC,KAAM,CAAAC,OAAO,CAAGJ,MAAM,CAACI,OAAO,EAAI,IAAI,CACtC,KAAM,CAAAC,UAAU,CAAGL,MAAM,CAACK,UAAU,EAAI,GAAG,CAC3C,KAAM,CAAAC,WAAW,CAAGN,MAAM,CAACM,WAAW,EAAI,SAAS,CACnD,KAAM,CAAAtD,QAAQ,CAAG,UAAU,CAC3B,KAAM,CAAAuD,MAAM,CAAGP,MAAM,CAACQ,MAAM,EAAI,QAAQ,CACxC,KAAM,CAAAC,KAAK,CAAG9D,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE8D,KAAK,CAChC,KAAM,CAAAC,eAAe,EAAAlG,oBAAA,CAAGiG,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEE,aAAa,UAAAnG,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACrD,KAAM,CAAAoF,MAAM,CAAG,CAAAjD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEiD,MAAM,GAAI,CAAC,CAAC,CACxC,KAAM,CAAAgB,WAAW,CAAG,CAAAjE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEkE,WAAW,GAAI,EAAE,CAElD;AACA5H,SAAS,CAAC,IAAM,CACf,GAAI,CAAA+G,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEhD,QAAQ,IAAK,WAAW,CAAE,CACrC8D,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACxC,CAAC,IAAM,CACNH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,EAAE,CAClC,CAEA;AACA,MAAO,IAAM,CACZH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,EAAE,CAClC,CAAC,CACF,CAAC,CAAE,CAACjB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEhD,QAAQ,CAAC,CAAC,CAAE;AAExB,KAAM,CAAAkE,kBAAkB,CAAIC,IAAY,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAG,CAACD,IAAI,CAAC/D,KAAK,CAAC,MAAM,CAAC,EAAI,EAAE,EAAEiE,MAAM,CAExD,KAAM,CAAAC,OAAO,CAAG,CAACH,IAAI,CAAC/D,KAAK,CAAC,aAAa,CAAC,EAAI,EAAE,EAAEiE,MAAM,CAExD,MAAO,CAAAD,cAAc,CAAC,CAAC,CAAGA,cAAc,CAAC,CAAC,CAAG,CAAC,CAC/C,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAAG,cAAc,CAAG,CAAA9F,cAAc,SAAdA,cAAc,kBAAAhB,qBAAA,CAAdgB,cAAc,CAAEmB,SAAS,UAAAnC,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgC8G,OAAO,UAAA7G,sBAAA,iBAAvCA,sBAAA,CAAyC8G,cAAc,GAAI,KAAK,CACvF,GAAI,CAAAC,WAAW,CAAG,CAAC,CACnB,QAAS,CAAAC,mBAAmBA,CAAC1F,cAAmB,CAAE,KAAA2F,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACjD,GAAI7F,cAAc,GAAK,CAAC,CAAE,CACzByF,WAAW,CAAG,EAAE,CAChB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIzF,cAAc,GAAK,CAAC,CAAE,CAChCyF,WAAW,CAAG,CAAC,CACf,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIzF,cAAc,GAAK,CAAC,CAAE,CAChCyF,WAAW,CAAG,CAAC,CACf,MAAO,aAAa,CACrB,CAAC,IACU,IAAIzF,cAAc,GAAK,CAAC,CAAE,CACpCyF,WAAW,CAAG,EAAE,CAChB,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAjG,cAAc,SAAdA,cAAc,kBAAAmG,sBAAA,CAAdnG,cAAc,CAAEmB,SAAS,UAAAgF,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA9BD,sBAAA,CAAgCL,OAAO,UAAAM,sBAAA,iBAAvCA,sBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAAC1F,cAAc,CAAC,CAC5D,KAAM,CAAAgG,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACV,cAAc,CAAE,MAAO,KAAI,CAEhC,GAAIS,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACCpI,IAAA,CAACL,aAAa,EACb2I,OAAO,CAAC,MAAM,CACdC,KAAK,CAAE3G,UAAW,CAClB4G,QAAQ,CAAC,QAAQ,CACjBC,UAAU,CAAErG,WAAW,CAAG,CAAE,CAC5BsG,EAAE,CAAE,CAAEC,eAAe,CAAE,aAAa,CAAEC,OAAO,CAAC,sBAAsB,CAAG,+BAA+B,CAAE,CACrFD,eAAe,CAAErG,aAAe;AAClC,CAAG,CAAE,CACtBuG,UAAU,cAAE7I,IAAA,CAACT,MAAM,EAAC6H,KAAK,CAAE,CAAE0B,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAE/I,IAAA,CAACT,MAAM,EAAC6H,KAAK,CAAE,CAAE0B,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACM,GAAIV,gBAAgB,GAAK,aAAa,CAAE,CAC7C,mBACapI,IAAA,CAACV,GAAG,EAACoJ,EAAE,CAAE,CAACE,OAAO,CAAC,sBAAsB,CAACI,OAAO,CAAE,MAAM,CACnEC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,QAAQ,CACtBC,GAAG,CAAE,KAAK,CAAE,CAAAC,QAAA,CAGIC,KAAK,CAACC,IAAI,CAAC,CAAE7B,MAAM,CAAE7F,UAAW,CAAC,CAAC,CAACiD,GAAG,CAAC,CAAC0E,CAAC,CAAEC,KAAK,gBAC/CxJ,IAAA,QAEEoH,KAAK,CAAE,CACLqC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbf,eAAe,CAAEa,KAAK,GAAKpH,WAAW,CAAG,CAAC,CAAGE,aAAa,CAAG,SAAS,CAAE;AACxEqH,YAAY,CAAE,OAChB,CAAE,EANGH,KAON,CACF,CAAC,CAED,CAAC,CAEpB,CACA,GAAIpB,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCpI,IAAA,CAACV,GAAG,EAACoJ,EAAE,CAAE,CAACE,OAAO,CAAC,sBAAsB,CAACI,OAAO,CAAE,MAAM,CACxDC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,YACd,CAAE,CAAAE,QAAA,cACDlJ,KAAA,CAACV,UAAU,EAACkJ,EAAE,CAAE,CAACkB,QAAQ,CAAC,MAAM,CAAEC,KAAK,CAAEvH,aAAa,CAAE,CAAA8G,QAAA,EAAC,OACpD,CAAChH,WAAW,CAAC,MAAI,CAACR,UAAU,EACrB,CAAC,CACT,CAAC,CAER,CAEA,GAAIwG,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACCpI,IAAA,CAACV,GAAG,EAACoJ,EAAE,CAAE,CAAKe,KAAK,CAAE,mBAAmB,CACxCb,OAAO,CAAE,WAAW,CAAE,CAAAQ,QAAA,cACrBpJ,IAAA,CAACR,UAAU,EAAC8I,OAAO,CAAC,OAAO,CAAAc,QAAA,cAC1BpJ,IAAA,CAACJ,cAAc,EACd0I,OAAO,CAAC,aAAa,CACrBwB,KAAK,CAAEhI,QAAS,CACK4G,EAAE,CAAE,CAAC,0BAA0B,CAAE,CAC7BC,eAAe,CAAErG,aAAe;AAClC,CAAE,CAAE,CAC3B,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,mBACCtC,IAAA,CAACV,GAAG,EAACoJ,EAAE,CAAE,CACR;AACA;AAAA,CACC,CAACqB,SAAS,CAAC,iBAAiB,CAAAX,QAAA,CAC5B3G,UAAU,eACVvC,KAAA,CAACZ,GAAG,EACHyK,SAAS,CAAC,cAAc,CACxBC,EAAE,CAAC,aAAa,CAChBtB,EAAE,CAAE,CACH;AACA;AACA;AACA;AACAuB,IAAI,CAAE,KAAK,CACXP,MAAM,CAAE,MAAM,CACdQ,SAAS,CAAE,GAAG,EAAE,CAACC,QAAQ,CAAC3D,OAAO,EAAE,CAAC,CAAC,CAAC2D,QAAQ,CAAC1D,UAAU,EAAE,CAAC,CAAC,EAAEkB,cAAc,CAAGG,WAAW,CAAG,CAAC,CAAC,EAAEnD,YAAY,EAAIA,YAAY,CAAC8C,MAAM,CAAG,CAAC,CAAE,CAAC,CAAG,CAAC,CAAC,CAAEH,kBAAkB,CAACnD,aAAa,CAAC,CAAC,EAAG,IAAI,CAAC;AAC7LiG,SAAS,CAAE,uBAAuB,CAClCzB,eAAe,CAAE7F,YAAY,CAC7B;AACAuH,QAAQ,CAAC,MAAM,CACf;AACAzB,OAAO,CAAE,GAAGpC,OAAO,IAAI,CACvB;AACA;AACA8D,SAAS,CAAEC,MAAM,CAACC,IAAI,CAACpE,MAAM,CAAC,CAACqB,MAAM,EAAI,CAAC,CAAE,iCAAiC,CAAG,CAAArB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEhD,QAAQ,GAAI,WAAW,CAAG,iCAAiC,CAAG,MAAM,CAAC;AAC7JoF,QAAQ,CAAEpF,QAAQ,CAClBqH,MAAM,CAAE9D,MAAM,CAEd+D,UAAU,CAAG,GAAGrE,eAAe,CAAGA,eAAe,CAAG,SAAS,aAAa,CAC1E;AACAsE,SAAS,CACP,GAAGlE,UAAU,YAAYC,WAAW,aAAa,CAEjDkE,WAAW,CACV,GAAGnE,UAAU,YAAYC,WAAW,aAAa,CAElDmE,UAAU,CACV,GAAGpE,UAAU,YAAYC,WAAW,aAAa,CAEpDoE,YAAY,CAAE,GAAGrE,UAAU,YAAYC,WAAW,aAClD,CAAE,CAAA0C,QAAA,eAEFlJ,KAAA,CAACZ,GAAG,EAACyK,SAAS,CAAC,WAAW,CAACrB,EAAE,CAAE,CAAGM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE,CAAAG,QAAA,eACzElJ,KAAA,CAACZ,GAAG,EACHoJ,EAAE,CAAE,CACH;AACA;AACAF,QAAQ,CAAE,UAAU,CACpBQ,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,QAAQ,CACrBO,KAAK,CAAE,MAAM,CAGd,uBAAuB,CAAE,CACxBA,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,GACT,CAED,CAAE,CAAA3B,QAAA,EAGD/E,SAAS,cACTrE,IAAA,CAACR,UAAU,EACVwL,SAAS,CAAC,GAAG,CACbC,IAAI,CAAE5G,SAAU,CAChB6G,MAAM,CAAC,QAAS;AAAA,CAChBC,GAAG,CAAC,qBAAsB;AAAA,CAC1BzC,EAAE,CAAE,CACHmB,KAAK,CAAEnF,SAAS,CAChBkE,OAAO,CAAE,SAAS,CAClBwC,SAAS,CAAEhH,SAAS,EAAI,QAAQ,CAChC8F,SAAS,CAAE,CAAC,CACZmB,cAAc,CAAE,WACjB,CAAE,CACFC,uBAAuB,CAAE,CAAEC,MAAM,CAAElI,iBAAiB,CAAC2D,WAAW,CAAE,CAAE,CACxD,CAAC,cAEdhH,IAAA,CAACR,UAAU,EAAWuK,SAAS,CAAC,yBAAyB,CAExDrB,EAAE,CAAE,CACHmB,KAAK,CAAEnF,SAAS,CAChB0G,SAAS,CAAEhH,SAAS,CACpB8F,SAAS,CAAE,CAAC,CACZtB,OAAO,CAAE,SAAS,CAClB4C,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvB,KAAK,CAAE,CACNV,MAAM,CAAE,GACP,CACH,CAAE,CACFO,uBAAuB,CAAE,CAAEC,MAAM,CAAElI,iBAAiB,CAACc,aAAa,CAAE,CAAE,CACvD,CAChB,CACAG,KAAK,eACLtE,IAAA,CAACR,UAAU,EACVwL,SAAS,CAAC,MAAM,CAChBtC,EAAE,CAAE,CACHgD,UAAU,CAAElH,IAAI,CAAG,MAAM,CAAG,QAAQ,CACpCmH,SAAS,CAAElH,MAAM,CAAG,QAAQ,CAAG,QAAQ,CACvCoF,KAAK,CAAEnF,SAAS,CAChBkE,OAAO,CAAE,SAAS,CAClBwC,SAAS,CAAEhH,SAAS,CAAGA,SAAS,CAAG,QAAQ,CAC3CwH,EAAE,CAAE,CACL,CAAE,CAAAxC,QAAA,CAED9E,KAAK,CACK,CACZ,CACAK,YAAY,EACZA,YAAY,CAACkH,IAAI,CAAE7G,MAAW,EAAKA,MAAM,CAAC8G,UAAU,EAAI9G,MAAM,CAAC8G,UAAU,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,eACvF/L,IAAA,CAACN,aAAa,EACbgJ,EAAE,CAAE,CACJsD,cAAc,CAAE,QAAQ,CACvBpD,OAAO,CAAE,cAAc,CACxBc,MAAM,CAAE,MACR,CAAE,CAAAN,QAAA,CAEDzE,YAAY,CAACE,GAAG,CAAC,CAACG,MAAW,CAAEwE,KAAU,QAAAyC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBACzCrM,IAAA,CAACT,MAAM,EAEN+M,OAAO,CAAEA,CAAA,GAAM7G,iBAAiB,CAACT,MAAM,CAACuH,YAAY,CAAE,CACtDjE,OAAO,CAAC,WAAW,CACnBlB,KAAK,CAAE,CACNuB,eAAe,CAAE,EAAAsD,qBAAA,CAAAjH,MAAM,CAACwH,gBAAgB,UAAAP,qBAAA,iBAAvBA,qBAAA,CAAyBQ,qBAAqB,GAAI,SAAS,CAC5E5C,KAAK,CAAE,EAAAqC,sBAAA,CAAAlH,MAAM,CAACwH,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBQ,eAAe,GAAI,SAAS,CAC5DC,MAAM,CAAE,CAAAR,sBAAA,CAAAnH,MAAM,CAACwH,gBAAgB,UAAAL,sBAAA,WAAvBA,sBAAA,CAAyBS,iBAAiB,CAC/C,aAAa5H,MAAM,CAACwH,gBAAgB,CAACI,iBAAiB,EAAE,CACxD,MAAM,CACT7B,MAAM,CAAE,OAAO,CACfnB,QAAQ,CAAE,EAAAwC,sBAAA,CAAApH,MAAM,CAACwH,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyBS,QAAQ,GAAI,EAAE,CACjDpD,KAAK,CAAE,EAAA4C,sBAAA,CAAArH,MAAM,CAACwH,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyB/F,KAAK,GAAI,MAAM,CAC/CsC,OAAO,CAAE,UAAU,CACnBkE,aAAa,CAAE,MAAM,CACrBnD,YAAY,CAAE,MAAM,CACpBoD,UAAU,CAAE,MACb,CAAE,CACFrE,EAAE,CAAE,CACH,SAAS,CAAE,CACVsE,MAAM,CAAE,iBACT,CACD,CAAE,CAAA5D,QAAA,CAEDpE,MAAM,CAAC8G,UAAU,EAvBb9G,MAAM,CAACE,EAAE,EAAI,UAAUsE,KAAK,EAwB1B,CAAC,EACT,CAAC,CACY,CACf,CAwBDnF,SAAS,eACTrE,IAAA,CAACR,UAAU,EACVwL,SAAS,CAAC,GAAG,CACbC,IAAI,CAAE5G,SAAU,CAChB6G,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBzC,EAAE,CAAE,CACHmB,KAAK,CAAEnF,SAAS,CAChB0G,SAAS,CAAEhH,SAAS,EAAI,MAAM,CAC9BwH,EAAE,CAAE,CAAC,CACLP,cAAc,CAAE,WACjB,CAAE,CAAAjC,QAAA,CAED/F,iBAAiB,CAACc,aAAa,CAAC,CACtB,CACZ,CAEAP,KAAK,eACL5D,IAAA,CAACV,GAAG,EACH0L,SAAS,CAAC,GAAG,CACbC,IAAI,CAAE,EAAAjK,sBAAA,CAAAU,gBAAgB,CAACsB,SAAS,CAAC,CAAC,CAAC,UAAAhC,sBAAA,kBAAAC,sBAAA,CAA7BD,sBAAA,CAA+BiM,eAAe,UAAAhM,sBAAA,iBAA9CA,sBAAA,CAAgDoD,SAAS,GAAI,GAAI,CACvE6G,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBzC,EAAE,CAAE,CAAE0C,SAAS,CAAEhH,SAAS,EAAI,QAAS,CAAE,CAAAgF,QAAA,cAEzCpJ,IAAA,CAACV,GAAG,EACH0L,SAAS,CAAC,KAAK,CACfkC,GAAG,CAAErJ,QAAQ,CAACD,KAAK,CAAC,CAAGA,KAAK,CAAGA,KAAM,CACrC8E,EAAE,CAAE,CACHyE,SAAS,CAAE,EAAAjM,sBAAA,CAAAQ,gBAAgB,CAACsB,SAAS,CAAC,CAAC,CAAC,UAAA9B,sBAAA,kBAAAC,sBAAA,CAA7BD,sBAAA,CAA+B+L,eAAe,UAAA9L,sBAAA,iBAA9CA,sBAAA,CAAgDiM,cAAc,GAAI,MAAM,CACnFC,SAAS,CAAE,EAAAjM,sBAAA,CAAAM,gBAAgB,CAACsB,SAAS,CAAC,CAAC,CAAC,UAAA5B,sBAAA,kBAAAC,sBAAA,CAA7BD,sBAAA,CAA+B6L,eAAe,UAAA5L,sBAAA,kBAAAC,sBAAA,CAA9CD,sBAAA,CAAgDiM,cAAc,UAAAhM,sBAAA,kBAAAC,sBAAA,CAA9DD,sBAAA,CAAiE,CAAC,CAAC,UAAAC,sBAAA,iBAAnEA,sBAAA,CAAqEgM,GAAG,GAAI,SAAS,CAChGvE,OAAO,CAAE,OAAO,CAChB+B,MAAM,CAAE,QACT,CAAE,CACF,CAAC,CACE,CACL,EACG,CAAC,CAAC,GAAG,CACTjE,eAAe,eACf9G,IAAA,CAACP,UAAU,EACXiJ,EAAE,CAAE,CACH;AACF4B,SAAS,CAAE,iCAAiC,CAC5CkD,UAAU,CAAE,KAAK,CACjB9C,UAAU,CAAE,iBAAiB,CAC7BiC,MAAM,CAAE,gBAAgB,CACxBlC,MAAM,CAAC,QAAQ,CACdd,YAAY,CAAE,MAAM,CACrBf,OAAO,CAAC,gBAEP,CAAE,CAAAQ,QAAA,cAEDpJ,IAAA,CAACH,SAAS,EAAE6I,EAAE,CAAE,CAAC+E,IAAI,CAAC,GAAG,CAAC5D,KAAK,CAAC,MAAM,CAAE,CAAI,CAAC,CAClC,CACX,EACG,CAAC,cAEN7J,IAAA,CAACV,GAAG,EAACoJ,EAAE,CAAE,CACZ,IAAIN,gBAAgB,GAAK,QAAQ,EAAI,CACtCY,OAAO,CAAE,MAAM,CACfE,YAAY,CAAE,QAAQ,CACtBD,UAAU,CAAC,QACV,CAAC,CACH,CAAE,CAAAG,QAAA,CACCxH,UAAU,EAAI,CAAC,EAAI+F,cAAc,cAChB3H,IAAA,CAAAI,SAAA,EAAAgJ,QAAA,CACKf,cAAc,CAAC,CAAC,CACnB,CAAC,CAEH,IACH,CACR,CAAC,EAED,CAEL,CAGG,CAAC,CAER,CAAC,CAED,cAAe,CAAAhI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}