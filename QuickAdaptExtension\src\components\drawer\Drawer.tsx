import React, { useState, useEffect, useContext, useRef, useMemo, ChangeEvent } from "react";
import "./Drawer.css";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { Steps } from "../../store/drawerStore";
import PopupList from "../guideSetting/guideList/PopupList";
import BUTTON_DEFAULT_VALUE from "../../store/drawerStore";
import ChecklistLauncher from "../checklist/ChecklistLauncherPreview";
import UndoRedoButtons from "../common/UndoRedoButtons";
import TrainingField from "../AI/TrainingField";
import { isScrapingActive, stopScraping, startScraping } from '../../services/ScrapingService'
import {
	openicon,
	closeicon,
	addicon,
	threedotmenu,
	closepluginicon,
	portalicon,
	backicon,
	touricon,
	ProductToursicon,
	Tooltipsicon,
	announcementicon,
	Bannersicon,
	Checklisticon,
	Hotspoticon,
	Surveyicon,
	Announcementsicon,
	bannersicon,
	tooltipicon,
	checklisticon,
	hotspotsicon,
	surveysicon,
	designicon,
	settingsicon,
	undoicon,
	redoicon,
	shareicon,
	playicon,
	PublishAndMore,
	Save,
	editicon,
	warning,
	editline,
	deletestep,
	ai
} from "../../assets/icons/icons";
import { Navigate, Outlet } from "react-router-dom";
import ChecklistApp from "../checklist/ChecklistPopup";
import {
	Popover,
	Box,
	Typography,
	Divider,
	TextField,
	Button,
	IconButton,
	InputAdornment,
	FormHelperText,
	FormControl,
	Select,
	MenuItem,
	Tooltip,
	InputLabel,
	Grid,
	List,
	Step,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogContentText,
	DialogActions,
} from "@mui/material";
import guideSetting from "../guideSetting/GuideSettings";
import DesignMenu from "../guideDesign/Design";
import JSEncrypt from "jsencrypt";
import { GetUserDetailsById, UserLogin } from "../../services/UserService";
import { LoginUserInfo } from "../../models/LoginUserInfo";
import { User } from "../../models/User";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import Visibility from "@mui/icons-material/Visibility";
import { initialsData, useAuth } from "../auth/AuthProvider";
import GuidePopUp from "../guideSetting/GuidePopUp";
import GuideSettings from "../guideSetting/GuideSettings";
import ExtensionLogin from "../login/ExtensionLogin";
import EditIcon from "@mui/icons-material/Edit";
import { saveGuide, updateGuide } from "../../services/SaveGuideService";
import TooltipUserview from "../TooltipsPreview/Tooltipspreview";
import {
	CheckGuideNameExists,
	GetAccountsList,
	GetGudeDetailsByGuideId,
	SubmitUpdateGuid,
	UpdateGuideName,
	getAllGuides,
} from "../../services/GuideListServices";
import Guidemenu from "../guideSetting/guideList/GuideMenuOptions";
import Banner from "../guideBanners/Banners";
import { AccountContext } from "../login/AccountContext";
import PageInteractions from "../guideBanners/selectedpopupfields/PageInteraction";
import CloseIcon from "@mui/icons-material/Close";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import ElementsSettings from "../guideSetting/ElementsSettings";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
import useInfoStore from "../../store/UserInfoStore";
import useHistoryStore from "../../store/historyStore";
import BannerEndUser from "../Bannerspreview/Banner";
import AnnouncementPopup from "../GuidesPreview/AnnouncementPreview";
import { Checklist, Padding } from "@mui/icons-material";
import { useSnackbar } from "../guideSetting/guideList/SnackbarContext";
import userSession from "../../store/userSession";
import CheckIcon from "@mui/icons-material/Check";
import LogoutPopup from "./LogoutPopup";
import PerfectScrollbar from "react-perfect-scrollbar";
import "react-perfect-scrollbar/dist/css/styles.css";
import AddIcon from "@mui/icons-material/Add";
import CreateTooltip from "../Tooltips/Tooltip";
import HotspotPreview from "../GuidesPreview/HotspotPreview";
import TooltipPreview from "../GuidesPreview/TooltipPreview";
// import TooltipUserpreview from "../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview";
import TooltiplastUserview from "../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview";
import FeatureSelectionModal from "../tours/tourTemplate";
import StepCreationPopup from "../tours/stepPopup";
import TourPreview from "../tours/tourPreview";
// import ModernChatWindow from "../AIAgent/ModernChatWindow";

interface Account {
	AccountId: string;
	AccountName: string;
}
interface DismissData {
	Actions: string | null;
	DisplayType: string;
	Color: string;
	DontShowAgain: boolean;
}

interface TextProperties {
	Bold: boolean;
	Italic: boolean;
	BulletPoints: boolean;
	TextFormat: string;
	TextColor: string;
}

interface TextFieldProperties {
	Text: string | undefined;
	Alignment: string;
	Hyperlink: string;
	Emoji: string;
	TextProperties: TextProperties;
}

interface UploadedImage {
	Url: string;
	AltText: string;
	BackgroundColor: string;
	Fit: string;
	Fill: string;
	SectionHeight: string;
}

interface ImageProperties {
	Id: string;
	CustomImage: UploadedImage[];
}

interface CustomButton {
	ButtonStyle: string;
	ButtonName: string;
	Alignment: string;
	ButtonId: string;
	ButtonAction: {
		Action: string;
		ActionValue: string;
		TargetUrl: string;
	};
	Padding: {
		Top: number;
		Right: number;
		Bottom: number;
		Left: number;
	};
	ButtonProperties: {
		Padding: number;
		Width: number;
		Font: number;
		FontSize: number;
		ButtonTextColor: string;
		ButtonBackgroundColor: string;
		ButtonBorderColor: string;
	};
}
interface Canvas {
	Position: string;
	Padding: string;
	Radius: string;
	BorderSize: string;
	BorderColor: string;
	BackgroundColor: string;
	Width: string;
	Zindex: string;
}

interface GotoNext {
	NextStep: string; // Specifies the next step (e.g., "Element" or "Button")
	ButtonId: string; // The name of the button for the next action
	ElementPath: string; // The XPath or selector for the next element
	ButtonName: string;
}
interface Design {
	ViewPortWidth: string;
	BackdropShadow: boolean;
	QuietIcon: true;
	IconColor: string;
	GotoNext: GotoNext;
}

interface Advanced {
	ShowAfter: string;
	OnScrollDelay: string;
}

interface Dismiss {
	Actions: string;
	DisplayType: string;
	Color: string;
	DontShowAgain: boolean;
}
interface CheckpointItem {
	Interaction: string;
	Title: string;
	Description: string;
	RedirectURL: string;
	Icon: any;
	SupporingMedia: any[];
	MediaTitle: string;
	MediaDescription: string;
}

interface GuideStep {
	TextFieldProperties: TextFieldProperties[];
	ImageProperties: ImageProperties[];

	HtmlSnippet: string;

	tooltipplacement: string;
	LayoutPositions: any[];
	ElementProperties?: {
		Design: {
			Dismiss: Dismiss | null;
			Overlay: {
				Enabled: boolean;
			};
		};
	};
	StepTitle?: string;
	Description?: string;
	StepType?: string;
	VideoEmbedCode?: string;
	ButtonSection: ButtonSection[];
	StepTargetURL?: string;

	// Add the missing properties if they are part of the data
	Modal?: {
		InteractionWithPopup: boolean;
		IncludeRequisiteButtons: boolean;
		DismissOption: boolean;
		ModalPlacedOn: string;
		ProgressColor: string;
	};
	Canvas?: {
		Position: string;
		Padding: string;
		Radius: string;
		BorderSize: string;
		BorderWidth: string;
		BorderColor: string;
		BackgroundColor: string;
		Width: string | undefined;
		Zindex: string | number;
		Height: string;
		CornerRadius: string;
		PrimaryColor: string;
		OpenByDefault: boolean;
		HideAfterCompletion: string;
	};
	Checkpoint?: {
		CheckpointItem: CheckpointItem[];
		CheckpointTitleColor: string;
		CheckpointDescriptionColor: string;
		CheckpointIconsColor: string;
		UnlockCheckpointsInOrder: boolean;
		Message: string;
	};

	TitleSubTitle?: {
		Title: string;
		TitleColor: string;
		TitleBold: boolean;
		TitleItalic: boolean;
		SubTitle: string;
		SubTitleColor: string;
		SubTitleBold: boolean;
		SubTitleItalic: boolean;
	};

	Launcher?: {
		LauncherColor: string;
		Type: string;
		Icon: any;
		Text: string;
		TextColor: string;
		LauncherPosition: {
			Left: boolean;
			Right: boolean;
			XAxisOffset: string;
			YAxisOffset: string;
		};
		IconColor: string;
		NotificationBadge: boolean;
		NotificationBadgeText: string;
		NotificationBadgeColor: string;
	};
	Design?: {
		ViewPortWidth: string;
		BackdropShadow: boolean;
		QuietIcon: true;
		IconColor: string;
		GotoNext: GotoNext;
	};
	Advanced?: {
		ShowAfter: string;
		OnScrollDelay: string;
	};
	ElementPath?: string;
	Position?: {
		XAxisOffset?: number;
		YAxisOffset?: number;
	};
	Hotspot?: {
		HotspotPosition: HotspotPosition;
		Type: string;
		Color: string;
		Size: string;
		PulseAnimation: boolean;
		StopAnimation: boolean;
		ShowUpon: string;
		ShowByDefault: boolean;
	};
	Tooltip?: {
		EnableProgress: boolean;
		Color: string;
		ProgressTemplate: string;
		GotoNextStep: string;
		ButtonName: string;
		InteractWithPage: boolean;
	};
}

declare global {
	interface Window {
		chrome:typeof chrome
	}
}
interface HotspotPosition {
	XOffset: string;
	YOffset: string;
}
declare global {
	interface Window {
		appState: any;
	}
}
interface Hotspot {
	HotspotPosition: HotspotPosition;
	Type: string;
	Color: string;
	Size: string;
	PulseAnimation: boolean;
	StopAnimation: boolean;
	ShowUpon: string;
	ShowByDefault: boolean;
}

interface ButtonSection {
	Id: string;
	CustomButtons: CustomButton[];
	BackgroundColor: string;
}

export interface GuideData {
	GuideType: any;
	GuideId: string;
	Name: string;
	OrganizationId: string;
	AccountId: string;
	GuideStatus: string;
	CreatedDate: string;
	UpdatedDate: string;
	TargetUrl: string;
	GuideStep: GuideStep[];
}

let updatedGuideData: any;
let selectedtemp: any;
let selectedStepType: any;
let idToDelete: string;
let nameToDelete: string;
let stepId: any;
const Drawer: React.FC = () => {
	let userId: string;
	let accountId: string;
	let loginUserData: string;
	let isChangesSaved = false;
	let closedGuide = false;
	const { clearAll, clearAccessToken } = useInfoStore.getState();
	const accessToken = useInfoStore((state) => state.accessToken);
	//const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
	const {
		setIsAnnouncementCreationBuilderOpen,
		IsAnnouncementCreationBuilderOpen,
		IsBannerCreationBuilderOpen,
		setIsHotspotCreationBuilderOpen,
		IsHotspotCreationBuilderOpen,
		setIsBannerCreationBuilderOpen,
		setIsGuidesListOpen,
		setIsInHomeScreen,
		clearUserSession,
		setIsAnnouncementListOpen,
		IsTooltipCreationBuilderOpen,
		setIsTooltipCreationBuilderOpen,
		setIsBannerslistOpen,
		isGuideInfoScreen,
		setIsGuideInfoScreen,

		selectedTemplated,
		setSelectedTemplated,
		//selectedTemplateTour,
		//setSelectedTemplateTour,
		isHomeScreen,
		setIsHomeScreen,
		isTemplateScreen,
		setIsTemplateScreen,
	} = userSession.getState();

	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const [stepName, setStepName] = useState("");
	const [errorInStepName, setErrorInStepName] = useState({ error: false, message: "" });
	const [showTextField, setShowTextField] = useState(false);
	const { signOut } = useAuth();
	const { openSnackbar } = useSnackbar();
	const [selectedElement, setSelectedElement] = useState<any>(null);
	const [isthreeDotOpen, setIsthreeDotOpen] = useState<boolean>(false);
	const [isDrawerClosed, setIsDrawerClosed] = useState<boolean>(false);
	const [isExtensionClosed, setIsExtensionClosed] = useState<boolean>(false);
	//const [isHomeScreen, setIsHomeScreen] = useState<boolean>(true);
	const [showPassword, setShowPassword] = useState(false);
	//const [isGuideInfoScreen, setIsGuideInfoScreen] = useState<boolean>(false);
	const [password, setPassword] = useState("");
	const [currentGuide, setCurrentGuide] = useState<any>(null);
	const [EditGuide, setEditGuide] = useState<any>(null);
	const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);
	const [response, setresponse] = useState("");
	//const [isTemplateScreen, setIsTemplateScreen] = useState<boolean>(false);
	//const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
	// const [guideName, setGuideName] = useState<string>("");
	const [showTooltipenduser, setShowTooltipenduser] = useState(false);
	const [isTooltipPopupOpen, setIsTooltipPopupOpen] = useState(false);
	const [showHotspotenduser, setShowHotspotenduser] = useState(false);
	const [isHotspotPopupOpen, setIsHotspotPopupOpen] = useState(false);
	const [startingUrl, setStartingUrl] = useState<string>(window.location.href);
	const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false);
	const [isLoggedIn, setIsLoggedIn] = useState<boolean>(!!accessToken);
	const [isEditable, setIsEditable] = useState<boolean>(false);
	const [email, setEmail] = useState<string>("");
	// const [designpopup, setDesignPopup] = useState<boolean>(false);
	const [loginUserDetails, setUserDetails] = useState<User | null>(null);
	const [guidesSettingspopup, setguidesSettingspopup] = useState<boolean>(false);
	const [error, setError] = useState<string | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	// const [bannerPopup, setBannerPopup] = useState<boolean>(false);
	const [accounts, setAccounts] = useState<Account[]>([]);
	//const [guidedatas, setGuideDataS] = useState<Account[]>([]);
	const [selectedAccountId, setSelectedAccountId] = useState("");
	const { setAccountId } = useContext(AccountContext);
	//const [openDropdown, setOpenDropdown] = useState<string | null>(null);
	// const [imageSrc, setImageSrc] = useState<string>("");
	const textBoxRef = useRef<HTMLDivElement>(null);
	const [htmlContent, setHtmlContent] = useState<string>("");
	const [buttonColor, setButtonColor] = useState<string>("#5F9EA0");
	const [imageName, setImageName] = useState("");
	const [isSelectingElement, setIsSelectingElement] = useState<boolean>(false);
	const [selectedElementDetails, setSelectedElementDetails] = useState<any>(null);
	const [alignment, setAlignment] = useState("");
	const [textvalue, setTextvalue] = useState("");
	const [position, setPosition] = useState("");
	const [radius, setRadius] = useState("100");
	const [borderSize, setBorderSize] = useState("100");
	const [isEditing, setIsEditing] = useState(false);
	// const [backgroundC, setBackgroundC] = useState("");
	// const [Bposition, setBposition] = useState("");
	// const [bpadding, setbPadding] = useState("0");
	// const [BborderSize, setBBorderSize] = useState("0");
	// const [Bbordercolor, setBBorderColor] = useState("");
	const [showBannerenduser, setShowBannerenduser] = useState(false);
	const [dialogBoxPosition, setDialogBoxPosition] = useState({ left: "20", top: "10", right: "10" });
	const [announcementData, setAnnouncementData] = useState<any>(null); // Store announcement data here
	const currentUrl = window.location.href;
	//const [tempGuideName, setTempGuideName] = useState(guideName); // Use temp state for editing
	// const [dismissData, setDismissData] = useState<DismissData | null>(null);
	const [isAnnouncementPopupOpen, setIsAnnouncementPopupOpen] = useState(false);
	const [isTourTemplate, setIsTourTemplate] = useState(false);
	const [isChecklistPreview, setIsChecklistPreview] = useState(false);
	const [isBannerPopupOpen, setIsBannerPopupOpen] = useState(false);
	const dismissData = useDrawerStore((state) => state.dismissData);
	//const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);
	const [overlays, setOverLays] = useState(false);
	const [savedGuideData, setSavedGuideData] = useState<GuideData | null>(null);
	const [hashValue, setHashValue] = useState<string>("");
	//const [zindeex, setZindeex] = useState("999999");
	const [activeMenu, setActiveMenu] = useState<string | null>("");
	const [currentStepNameId, setCurrentStepNameId] = useState<string>("");
	const [isEditStepName, setIsEditStepName] = useState(false);
	const [searchText, setSearchText] = useState<string>("");
	const [guideStep, setGuideStep] = React.useState<GuideStep[]>([]);
	const [isShowIcon, setisShowIcon] = useState<Boolean>(false);
	const [showScrapingButton, setShowScrapingButton] = useState(false);
	const [isSaveInProgress, setIsSaveInProgress] = useState(false);

	// const [activeMenu, setActiveMenu] = useState<string | null>(null);
	// const [searchText, setSearchText] = useState<string>("");
	// const { htmlContent } = useDrawerStore();
	// Added Zustand from here

	// const announcements = useMemo(() => {
	// 	return (
	// 	  (responses?.data as any[]).find(
	// 		(item) => item.GuideType === "Announcement" && item.GuideStep?.length > 0 && item.TargetUrl === currentUrl
	// 	  ) || {}
	// 	);
	//   }, [responses, currentUrl]);
	const divRef = useRef<HTMLDivElement | null>(null);
	const [templateSelectionPopup, setTemplateSelectionPopup] = useState(false);
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (divRef.current && !divRef.current.contains(event.target as Node) && !templateSelectionPopup) {
				//!divRef.current.contains(event.target)
				setOpenStepDropdown(false);
			}
		};
		if (openStepDropdown) {
			document.addEventListener("mousedown", handleClickOutside);
			return () => document.removeEventListener("mousedown", handleClickOutside);
		}
	});
	useEffect(() => {
		setStartingUrl(window.location.href);
	}, [window.location.href]);

	const {
		resetElementStyles,
		setDesignPopup,
		designPopup,
		announcementJson,
		currentStep,
		selectedTemplate,
		setSelectedTemplate,
		fit,
		fill,
		backgroundColor,
		setBackgroundColor,
		sectionHeight,
		setSectionHeight,
		guideName,
		SetGuideName,
		tempGuideName,
		setTempGuideName,
		guidedatas,
		setGuideDataS,
		isUnSavedChanges,
		imageSrc,
		setImageSrc,
		// imageName,
		updateImageContainerOnReload,
		updateButtonContainerOnReload,
		imagesContainer,
		setCanvasSetting,
		buttonsContainer,
		clearGuideDetails,
		clearBannerButtonDetials,
		isCollapsed,
		setIsCollapsed,
		setWidth,
		setBorderColor,
		setIsUnSavedChanges,
		setBorderRadius,
		setBorderSize: setBorderSizeStore,
		setPadding,
		setDismissData,
		backgroundC,
		setBackgroundC,
		Bposition,
		setBposition,
		bpadding,
		setbPadding,
		Bbordercolor,
		setBBorderColor,
		BborderSize,
		setBBorderSize,
		zindeex,
		setZindeex,
		bannerPopup,
		setBannerPopup,
		hotspotPopup,
		setHotspotPopup,
		textvaluess,
		setTextvaluess,
		createNewStep,
		generateSteps,
		renameStep,
		steps,
		changeCurrentStep,
		rtesContainer,
		preview,
		setPreview,
		btnBorderColor,
		btnBgColor,
		btnTextColor,
		isTooltipPopup,
		setIsTooltipPopup,
		elementSelected,
		setElementSelected,
		updateRTEContainerOnReload,
		setSteps,
		deleteStep,
		newCurrentStep,
		setTooltipDataOnEdit,
		updateCanvasInTooltip,
		tooltipPosition,
		tooltippadding,
		tooltipWidth,
		tooltipBordercolor,
		tooltipBackgroundcolor,
		tooltipborderradius,
		tooltipbordersize,
		tooltipXaxis,
		tooltipYaxis,
		hotspbgcolor,
		setHotspBgColor,
		toolTipGuideMetaData,
		updatehotspots,
		setHotspotDataOnEdit,
		updateStepNameInTooltip,
		createNewTooltipSteps,
		setOpenTooltip,
		openTooltip,
		cleanupDuplicateSteps,
		setXpathToTooltipMetaData,
		setAxisData,
		setBannerButtonSelected,
		axisData,
		setShowTooltipCanvasSettings,
		pageinteraction,
		setPageInteraction,
		bannerButtonSelected,
		resetTooltipMetaData,
		autoPosition,
		setAutoPosition,
		targetURL,
		setAnnBorderSize,
		setAnnPadding,
		elementClick,
		setElementClick,
		elementButtonName,
		setElementButtonName,
		isSaveClicked,
		setIsSaveClicked,
		setCurrentStep,
		//isGuideInfoScreen,
		//setIsGuideInfoScreen,
		selectedOption,
		progress,
		setProgress,
		dismiss,
		setDismiss,
		setbtnidss,
		overlayEnabled,
		setOverlayEnabled,
		pulseAnimationsH,
		setPulseAnimationsH,
		selectedTemplateTour,
		setSelectedTemplateTour,
		setTooltipCount,
		tooltipCount,
		setTourDataOnEdit,
		HotspotGuideDetails,
		TooltipGuideDetails,
		TooltipGuideDetailsNew,
		editClicked,
		setEditClicked,
		setSelectedOption,
		openWarning,
		setOpenWarning,
		textArray,
		setTextArray,
		setSelectedStepTypeHotspot,
		setImageAnchorEl,
		bannerJson,
		setBannerCanvasSetting,
		createNewAnnouncementStep,
		announcementGuideMetaData,
		setSettingAnchorEl,
		triggerCloseAllButtonPopups,
		setAnnouncementDataOnEdit,
		isALTKeywordEnabled,
		setIsALTKeywordEnabled,
		resetALTKeywordForNewTooltip,
		checklistGuideMetaData,
		setChecklistDataOnEdit,
		setCheckPointsPopup,
		setCheckPointsAddPopup,
		setCheckPointsEditPopup,
		setActiveMenu: setDrawerActiveMenu,
		setSearchText: setDrawerSearchText,
		ProgressColor,
		setProgressColor,
		setCreateWithAI,
		setInteractionData,
		interactionData,
		createWithAI,
		isAIGuidePersisted,
		setIsAIGuidePersisted,
		syncAITooltipDataForPreview,
		syncAITooltipContainerData,
		restoreTooltipElementClickState,
		restoreManualTooltipElementClickState,
		syncAIAnnouncementDataForPreview,
		syncAIAnnouncementContainerData,
		syncAnnouncementJsonWithMetaData,
		syncAIAnnouncementCanvasSettings,
		syncAITourDataForPreview,
		initializeAITourTooltipMetadata,
		syncCurrentStepDataForAITour,
		syncGlobalOverlayStateForAnnouncements,
		restoreAITourRTEData,
		syncGlobalProgressBarStateForAITour,
		restoreAITourProgressBarState,
		syncButtonContainerToMetadata,
		syncMetadataToButtonContainer,
		syncMetadataToSavedGuideData,
		forceSaveButtonConfiguration,
		saveCurrentStepURL,
		setIsReturningFromPreview,
		setUnsavedChangesBeforePreview,
		getUnsavedChangesBeforePreview,
	} = useDrawerStore((state: any) => state);
	const { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);

	const designPopupRef = useRef<HTMLDivElement | null>(null);
	//const [ttxet, setttxet] = useState("");
	selectedtemp = selectedTemplate;
	const getElementByXPath = (xpath: string | undefined): HTMLElement | null => {
		if (!xpath) return null;
		const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		const ele4 = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		const node = result.singleNodeValue;
		if (node instanceof HTMLElement) {
			return node;
		} else if (node?.parentElement) {
			return node.parentElement;
		} else {
			return null;
		}
	};

	const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);

	const generateDynamicId = () => {
		return "hotspotBlinkCreation";
	};
	const [hotspotClicked, setHotspotClicked] = useState(false);
	let hotspot = document.getElementById("hotspotBlinkCreation");
	let element: any;
	let stepsDetails: any;
	const fetchGuideDetails = () => {
		if (currentGuideId != "" && currentGuideId != null) {
			stepsDetails = updatedGuideData?.GuideStep || [];
			element = getElementByXPath(stepsDetails?.[0]?.ElementPath);
			setTargetElement(element);
			if (element) {
				// element.style.outline = "2px solid red";
			}
			if ((selectedTemplate === "Hotspot" && elementSelected) || selectedStepType === "Hotspot") {
				const hotspotPropData = toolTipGuideMetaData[0].hotspots;
				const xOffset = parseFloat(hotspotPropData?.XPosition || "4");
				const yOffset = parseFloat(hotspotPropData?.YPosition || "4");
				const size = hotspotPropData?.Size;
				let left: any;
				let top: any;

				if (element) {
					const rect = element.getBoundingClientRect();
					if (rect) {
						left = rect.x + xOffset;
						top = rect.y + yOffset;
					}
				}


				left = "375";
				top = "160";
				if (!hotspot) {
					hotspot = document.createElement("div");
					hotspot.id = generateDynamicId();
					document.body.appendChild(hotspot);
				}

				hotspot.style.position = "absolute";
				hotspot.style.left = `${left}px`;
				hotspot.style.top = `${top}px`;
				hotspot.style.width = `${size}px`;
				hotspot.style.height = `${size}px`;
				hotspot.style.backgroundColor = hotspotPropData?.Color;
				hotspot.style.borderRadius = "50%";
				hotspot.style.zIndex = "1000";
				// hotspot.style.pointerEvents = "none";
				hotspot.style.transition = "none";
				hotspot.innerHTML = "";

				if (hotspotPropData?.Type === "Info" || hotspotPropData?.Type === "Question") {
					const textSpan = document.createElement("span");
					textSpan.innerText = hotspotPropData.Type === "Info" ? "i" : "?";
					textSpan.style.color = "white";
					textSpan.style.fontSize = "14px";
					textSpan.style.fontWeight = "bold";
					textSpan.style.fontStyle = hotspotPropData.Type === "Info" ? "italic" : "normal";
					textSpan.style.display = "flex";
					textSpan.style.alignItems = "center";
					textSpan.style.justifyContent = "center";
					textSpan.style.width = "100%";
					textSpan.style.height = "100%";
					hotspot.appendChild(textSpan);
				}

				if (pulseAnimationsH) {
					hotspot.classList.add("pulse-animation");

					if (hotspotPropData?.stopAnimationUponInteraction) {
						const stopAnimation = () => {
							if (hotspot) {
								hotspot.classList.remove("pulse-animation");
								hotspot.onclick = null;
							}
						};

						hotspot.onclick = stopAnimation;
					}
				} else {
					hotspot.classList.remove("pulse-animation");
				}
			}
		}
	};
	useEffect(() => {
		fetchGuideDetails();
		return () => {
			if (hotspot) {
				hotspot.onclick = null;
				// hotspot.onmouseover = null;
			}
		};
	}, [toolTipGuideMetaData[0], hotspotClicked]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (designPopupRef.current && !designPopupRef.current.contains(event.target as Node)) {
				setDesignPopup(false);
			}
		};

		if (designPopup) {
			document.addEventListener("mousedown", handleClickOutside);
		} else {
			document.removeEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [designPopup]);
	const handleMouseEnter = () => {
		const screenWidth = window.innerWidth;
		const dialogWidth = 400; // Approximate width of the dialog

		if (dialogBoxPosition.left === "10") {
			setDialogBoxPosition({ left: "auto", right: "10", top: dialogBoxPosition.top }); // Moves right
		} else {
			setDialogBoxPosition({ left: "10", right: "auto", top: dialogBoxPosition.top }); // Moves left
		}
	};
	const handleEditsClick = () => {
		setIsEditing(true); // Enable edit mode
		setTempGuideName(guideName); // Initialize with current guide name
	};


	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {

		const newValue = e.target.value;
		if (newValue.length <= 50) {
			setTempGuideName(newValue);
		}
	};
	const handlechangeStep = (id: any) => {};
	// const handleElementSelected = (element: HTMLElement | null) => {
	// 	if (element) {
	// 	  // Gather details of the selected element
	// 	  const details = {
	// 		id: element.id,
	// 		className: element.className,
	// 		tagName: element.tagName,
	// 		attributes: Array.from(element.attributes).map((attr) => ({
	// 		  name: attr.name,
	// 		  value: attr.value,
	// 		})),
	// 	  };
	// 	  setSelectedElementDetails(details);
	// 	} else {
	// 	  // Reset details when no element is selected
	// 	  setSelectedElementDetails(null);
	// 	}
	// };
	// Declare custom property on Window interface
	interface ScreenState {
		currentScreen: string;
	}

	const initialState: ScreenState = {
		currentScreen: "home",
	};
	const [currentScreen, setCurrentScreen] = useState<string>(initialState.currentScreen);

	const determineCurrentScreen = () => {
		if (isGuideInfoScreen && !isTemplateScreen && !isPopupOpen) {
			setIsAnnouncementCreationBuilderOpen(false);
			//setIsGuideInfoScreen(true);
			setIsBannerCreationBuilderOpen(false);
			setIsHomeScreen(true);
			// setSelectedTemplated(selectedTemplate);
			// if (selectedTemplated) {
			// 	setSelectedTemplate(selectedTemplated);
			// }
			//setIsHomeScreen(false);
			return "guideInfoScreen";
		} else if (isTemplateScreen && !isGuideInfoScreen && !isPopupOpen) {
			return "templateScreen";
		} else if (
			isPopupOpen &&
			!bannerPopup &&
			currentGuideId &&
			(selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement")
		) {
			return "guidePopupScreen";
		} else if (isDrawerClosed && bannerPopup && !showBannerenduser && currentGuideId) {
			setIsBannerCreationBuilderOpen(true);
			return "guidePopupScreen";
		} else {
			return "home";
		}
	};

	// Update the current screen whenever relevant state changes
	useEffect(() => {
		setCurrentScreen(determineCurrentScreen());
	}, [
		//isGuideInfoScreen,
		isTemplateScreen,
		isPopupOpen,
		bannerPopup,
		currentGuideId,
		selectedTemplate,
		selectedTemplateTour,
	]);

	// Save the entire state to window.appState
	useEffect(() => {
		window.appState = {
			// UI state
			isCollapsed,

			// Screen states
			isGuideInfoScreen,
			isTemplateScreen,
			isPopupOpen,
			bannerPopup,
			currentScreen,

			// Content states
			selectedTemplate,
			selectedTemplateTour,
			guideName,
			currentGuideId,
			selectedStepType,
			guideStep,
			htmlContent,
			imageSrc,
			buttonColor,
			imageName,
			errors,
		};
	}, [
		// UI state
		isCollapsed,

		// Screen states
		isGuideInfoScreen,
		isTemplateScreen,
		isPopupOpen,
		bannerPopup,
		currentScreen,

		// Content states
		selectedTemplate,
		selectedTemplateTour,
		guideName,
		currentGuideId,
		selectedStepType,
		guideStep,
		htmlContent,
		imageSrc,
		buttonColor,
		imageName,
		//errors,
	]);

	// Restore the exact screen state when the component mounts
	useEffect(() => {
		if (initialState && initialState.currentScreen) {
			// No need to set individual states here as they're already set from initialState
		}
	}, [initialState]);
	// Restore banner state after page refresh
	useEffect(() => {
		// Only run this restoration logic once on component mount
		const restoreBannerState = () => {
			// Check if we have a currentGuideId and selectedTemplate is Banner
			const isBannerGuide = currentGuideId &&
				(selectedTemplate === "Banner" || selectedTemplateTour === "Banner");

			// Skip restoration if save is in progress or not a banner guide
			if (!isBannerGuide || isSaveInProgress) return;

			// Always restore banner popup visibility for banner guides
			if (!bannerPopup) {
				// console.log("Drawer: Restoring banner popup visibility after page refresh");

				// Restore banner popup state
				setBannerPopup(true);
				setIsDrawerClosed(true);

				// Ensure the banner creation builder is open
				setIsBannerCreationBuilderOpen(true);
				setIsBannerPopupOpen(true);

				// Make sure other popups are closed
				setIsAnnouncementPopupOpen(false);
				setIsAnnouncementCreationBuilderOpen(false);
				setIsTooltipCreationBuilderOpen(false);
				setIsHotspotCreationBuilderOpen(false);
				setShowBannerenduser(false);
			}

			// Check if we have saved banner data to restore
			const hasSavedRTEContent = (savedGuideData?.GuideStep ?? [])[currentStep-1]?.TextFieldProperties?.length > 0 &&
				savedGuideData?.GuideStep?.[0]?.TextFieldProperties?.some((field: any) =>
					field.Text && field.Text.trim() !== "");

			const hasSavedButtonContent = (savedGuideData?.GuideStep ?? [])[currentStep-1]?.ButtonSection?.length > 0 &&
				savedGuideData?.GuideStep?.[0]?.ButtonSection?.some((section: any) =>
					section.CustomButtons && section.CustomButtons.length > 0);

			// If we have saved data, restore only the saved content and discard unsaved changes
			if (hasSavedRTEContent || hasSavedButtonContent) {
				// Only clear button data if we don't have saved button content to restore
				if (!hasSavedButtonContent) {
					clearBannerButtonDetials();
				}

				// Restore saved RTE content
				if (hasSavedRTEContent && savedGuideData) {
					const savedRTEText = savedGuideData.GuideStep[currentStep-1].TextFieldProperties[0]?.Text || "";
					if (savedRTEText.trim()) {
						// Create RTE data structure for updateRTEContainerOnReload
						const savedRTEData = [{
							Text: savedRTEText,
							style: {
								backgroundColor: "transparent",
								height: 100,
								paddingLeft: 0,
								paddingTop: 0,
								paddingRight: 0,
								paddingBottom: 0,
								maxHeight: 0,
							}
						}];

						// Update RTE container with saved data only
						updateRTEContainerOnReload(savedRTEData, "submit");
					}
				}

				// Restore saved button content
				if (hasSavedButtonContent && savedGuideData) {
					setBannerButtonSelected(true);
					const savedButtons = savedGuideData.GuideStep[0].ButtonSection[0]?.CustomButtons || [];
					if (savedButtons.length > 0) {
						// Create button data structure for updateButtonContainerOnReload
						const savedButtonData = savedButtons.map((button: any) => ({
							ButtonName: button.ButtonName || "Button",
							ButtonProperties: {
								ButtonBackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#5F9EA0",
								ButtonBorderColor: button.ButtonProperties?.ButtonBorderColor || "#5F9EA0",
								ButtonTextColor: button.ButtonProperties?.ButtonTextColor || "#ffffff"
							},
							ButtonAction: {
								Action: button.ButtonAction?.Action || "close",
								TargetUrl: button.ButtonAction?.TargetUrl || "",
								ActionValue: button.ButtonAction?.ActionValue || "same-tab"
							},
							ContainerId: crypto.randomUUID()
						}));

						// Update button container with saved data only
						updateButtonContainerOnReload(savedButtonData, "submit");
					}
				}
			} else {
				// No saved data - clear any unsaved changes from store
				// console.log("Drawer: No saved banner data found, clearing unsaved changes");
				clearBannerButtonDetials();

				// Reset containers to default empty state
				updateRTEContainerOnReload([], "reset");
				updateButtonContainerOnReload([], "reset");
			}
		};

		// Run restoration logic after a small delay to ensure all stores are loaded
		const timeoutId = setTimeout(restoreBannerState, 100);

		return () => clearTimeout(timeoutId);
	}, [savedGuideData, isSaveInProgress]); // Depend on savedGuideData and save progress
	const handleKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {

			const trimmedName = tempGuideName.trim();

			if (!trimmedName || trimmedName.length < 3) {
				// Revert to the original name if the name is invalid
				setTempGuideName(guideName);
				setErrors((prev) => ({ ...prev, GuideName: "Guide name must be at least 3 characters long" }));
				setIsEditing(false);
			} else {
				const guideNameExists = await CheckGuideNameExists(trimmedName, selectedAccountId, selectedTemplate);
if (guideNameExists) {
  setTempGuideName(guideName); // revert to original
  setErrors((prev) => ({ ...prev, GuideName: "" })); // no inline error
  setIsGuideNameUnique(false);
	setIsEditing(false);
	openSnackbar("Guide name already exists", "error");

  return;
}

 else {
					// Save the new name if it's valid and unique
					SetGuideName(trimmedName);
					setErrors((prev) => ({ ...prev, GuideName: "" })); // Clear error
					setIsGuideNameUnique(true);
					setIsEditing(false);

					await UpdateGuideName(
						currentGuideId,
						organizationId,
						trimmedName,
						selectedAccountId,
						selectedTemplate
					);
				}
			}
		} else if (e.key === "Escape") {
			// Revert to the original name on Escape
			setTempGuideName(guideName);
			setIsEditing(false);
		}
	};

	// React.useEffect(() => {
	// 	if (savedGuideData?.GuideStep?.[0]) {
	// 		setGuideStep([savedGuideData.GuideStep[0]]); // Wrap in an array
	// 	}
	// }, [savedGuideData]);


	const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
		// Prevent immediate focus loss to allow snackbar to display properly
		e.preventDefault();

		// Get the trimmed name
		const trimmedName = tempGuideName.trim();

		// If name hasn't changed, just exit edit mode and return
		if (trimmedName === guideName) {
			setIsEditing(false);
			return;
		}

		// Validate name length
		if (!trimmedName || trimmedName.length < 3) {
			setTempGuideName(guideName);
			setErrors((prev) => ({ ...prev, GuideName: "Guide name must be at least 3 characters long" }));
			setIsEditing(false);
			return;
		}

		// Check if guide name exists
		const guideNameExists = await CheckGuideNameExists(trimmedName, selectedAccountId, selectedTemplate);

		if (guideNameExists) {
			// Handle duplicate name case
			setTempGuideName(guideName);
			setErrors((prev) => ({ ...prev, GuideName: "" }));
			setIsGuideNameUnique(false);

			// Exit edit mode first, then show snackbar with a slight delay
			setIsEditing(false);

			// Use setTimeout to ensure the snackbar appears after the edit mode is exited
			setTimeout(() => {
				openSnackbar("Guide name already exists", "error");
			}, 100);

			return;
		} else {
			// Handle success case
			SetGuideName(trimmedName);
			setErrors((prev) => ({ ...prev, GuideName: "" }));
			setIsGuideNameUnique(true);
			setIsEditing(false);

			await UpdateGuideName(
			  currentGuideId,
			  organizationId,
			  trimmedName,
			  selectedAccountId,
			  selectedTemplate
			);
		}

	};
	const [stepCreation, setStepCreation] = useState(false);
	const [count, setCount] = useState(0);
	const [stepData, setStepData] = useState({
		stepNumber: "",
		type: steps[0].stepType,
		description: "",
	});
	let sName: any;

	// Using the cleanupDuplicateSteps function imported from the drawer store

	const handleCreateStep = () => {
		sName = stepName?.trim() || "";
		setStepData({
			...stepData,
			stepNumber: stepName,
			type: stepData?.type || "Announcement",
		});
		if (isEditStepName) {
			sName = stepName;
			renameStep(currentStepNameId, sName, editDescription);
			if (selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") {
				updateStepNameInTooltip(currentStepNameId, sName);
			}

			// Clean up any duplicate steps that might have been created
			cleanupDuplicateSteps();

			// Find the index of the step being renamed in the steps array
			const stepIndex = steps.findIndex((step: any) => step.id === currentStepNameId);

			if (stepIndex !== -1) {
				// Map through GuideStep array and update the step with matching index
				const updatedGuideSteps = updatedGuideData?.GuideStep.map((guideStep: any, index: number) => {
					// Use the step index to match with GuideStep index
					if (index === stepIndex) {
					return {
						...guideStep,
						StepTitle: sName,
							StepId: currentStepNameId, // Ensure StepId is set correctly
					};
				}
				return guideStep;
			});

				// Filter out any duplicate steps (same StepId)
				const uniqueSteps: any[] = [];
				const stepIds = new Set<string>();

				updatedGuideSteps.forEach((step: any) => {
					// If this step has a StepId and we haven't seen it before, add it
					if (step.StepId && !stepIds.has(step.StepId)) {
						stepIds.add(step.StepId);
						uniqueSteps.push(step);
					}
					// If no StepId, check by StepTitle to avoid duplicates
					else if (!step.StepId && !uniqueSteps.some((s: any) => s.StepTitle === step.StepTitle)) {
						uniqueSteps.push(step);
					}
				});

			// Update the savedGuideData with the modified GuideStep array
			setSavedGuideData((prevData: any) => ({
				...prevData,

					GuideStep: uniqueSteps,
			}));
			}
		} else {
			if (selectedTemplate === "Tour") {
				createNewStep(sName, stepData?.type || "Announcement", stepData?.description);
				createNewTooltipSteps(sName, stepData?.type || "Announcement", stepData?.description);

				// Clean up any duplicate steps that might have been created
				cleanupDuplicateSteps();
			} else {
				if (selectedTemplate === "Announcement") {
					createNewAnnouncementStep(sName, stepData?.type, stepData?.description);
				} else {
					createNewStep(sName, stepData?.type, stepData?.description);

					if (selectedTemplate === "Tooltip") {
						createNewTooltipSteps(sName, stepData?.type, stepData?.description);

						// Clean up any duplicate steps that might have been created
						cleanupDuplicateSteps();
				}
			}
			}

			setWidth(500);
			setBorderColor("transparent");
			selectedTemplateTour == "Banner" ? setBackgroundColor("#f1f1f7") : setBackgroundColor("#fff");
			setBorderRadius(4);
			setBorderSizeStore(0);
			setPadding("4");
			setAnnPadding("12");
			setAnnBorderSize(2);
			// Always set Bposition to "Cover Top" for Banner steps in Tour
			if (selectedTemplate == "Tour" && stepData?.type == "Banner") {
				setBposition("Cover Top");
			} else {
				selectedTemplateTour == "Banner" && selectedTemplate == "Tour" ? setBposition("Cover Top") : setBposition("absolute");
						}
			setOverlayEnabled(overlayEnabled);
			setPageInteraction(pageinteraction);
			setbPadding("10");
			setBBorderSize("2");
			setBBorderColor("transparent");
			setBackgroundC("#fff"); //selectedTemplate =="Banner" ? setBackgroundC("#f1f1f7") : setBackgroundC("#fff");
			setStepCreation(false);
			setSelectedStepTypeHotspot(false);
			// if (selectedTemplate === "Tooltip" || selectedTemplateTour==="Tooltip" || stepData?.type==="Tooltip") {
			// 	createNewTooltipSteps(sName,stepData?.type);
			// }
		}

		//setBannerButtonSelected(false);
		// Reset states
		setStepName("");
		setCurrentStepNameId("");
		setIsEditStepName(false);
		setShowTextField(false);
		setEditType("");
		handleSaveGuide();
		setStepCreation(false);
		setPlusIconClick(false);
		setSelectedStepTypeHotspot(false);
		setElementClick("element");
		// Reset AI state when step creation is complete
		if (createWithAI) {
			setCreateWithAI(false);
		}
	};

	// useEffect(() => {
	// 	setTimeout(() => {
	// 		if (textBoxRef.current) {
	// 			textBoxRef.current.innerHTML = textvaluess; // Assign the value when ref is available
	// 		}
	// 	}, 5);
	// }, [textvaluess]); // Trigger whenever `textValue` changes

	// 	container: {
	// 		padding: "10px",
	// 		backgroundColor: "#F6EEEE",
	// 		borderRadius: "8px",
	// 	},
	// 	welcomeText: {
	// 		fontFamily: "Poppins, sans-serif",
	// 		fontSize: "16px",
	// 		fontWeight: 600,
	// 		lineHeight: "24px",
	// 		textAlign: "left",
	// 		color: "rgba(34, 34, 34, 1)",
	// 	},
	// 	headerText: {
	// 		fontFamily: "Poppins, sans-serif",
	// 		fontSize: "14px",
	// 		fontWeight: 400,
	// 		lineHeight: "24px",
	// 		textAlign: "left",
	// 		color: "rgba(68, 68, 68, 1)",
	// 		marginTop: "10px",
	// 	},
	// 	qadptTextdanger :{
	// 		color: "#d9534f",
	// 		fontSize: "0.9rem",
	// 	},
	// 	textField: {
	// 		width: "100%",
	// 		backgroundColor: "rgba(255, 255, 255, 1)",
	// 		borderRadius: "6px",
	// 		height: "46px",
	// 	},
	// 	textFieldInput: {
	// 		fontFamily: "Poppins, sans-serif",
	// 		fontSize: "16px",
	// 		fontWeight: 400,
	// 		color: "rgba(68, 68, 68, 1)",
	// 		padding: "12px",
	// 		border: "1px solid rgba(213, 213, 213, 1)",
	// 		borderRadius: "6px",
	// 		boxShadow: "none",
	// 		height: "46px",
	// 	},
	// 	forgotPassword: {
	// 		color: "#5F9EA0",
	// 		cursor: "pointer",
	// 		fontFamily: "Poppins, sans-serif",
	// 		fontSize: "14px",
	// 		fontWeight: 400,
	// 		lineHeight: "24px",
	// 		marginTop: "25px",
	// 	},
	// 	loginButton: {
	// 		backgroundColor: "#5F9EA0",
	// 		color: "#fff",
	// 		borderRadius: "25px",
	// 		width: "100%",
	// 		marginTop: "20px",
	// 		textTransform: "none",
	// 		fontFamily: "Poppins, sans-serif",
	// 		fontSize: "16px",
	// 		fontWeight: 500,
	// 	},
	// 	endAdornment: {
	// 		fontFamily: "Poppins, sans-serif",
	// 		fontSize: "14px",
	// 		fontWeight: 500,
	// 		color: "#5F9EA0",
	// 		cursor: "pointer",
	// 	},
	// };
	// const handleClickShowPassword = () => {
	//     setShowPassword(!showPassword);
	// };
	// const handlePasswordChange = (event: any) => {
	// 	setPassword(event.target.value);
	// 	setError(null);
	// };
	const toggleDrawer = () => {
		setIsCollapsed(!isCollapsed);
	};

	const handleDesignclick = () => {
		setguidesSettingspopup(false);

		setDesignPopup(true);
		// setTimeout(() => {
		// 	setDesignPopup(true);
		// }, 0);
	};
	const handleGuidesSettingsclick = () => {
		setDesignPopup(false);
		setguidesSettingspopup(true);
	};

	const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
		setAnchorEl(event.currentTarget);
		setIsthreeDotOpen(true);
	};
	const handlePlayIconClick = () => {
		// Store the current unsaved changes state before going to preview
		// This will be restored when returning from preview
		setUnsavedChangesBeforePreview(isUnSavedChanges);

		// Synchronize AI data before preview
		setOpenTooltip(false);
		if (createWithAI && (selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot")) {
			syncAITooltipDataForPreview();
			syncAITooltipContainerData();
		} else if (createWithAI && selectedTemplate === "Tour") {
			// For AI tours, ensure all step data is synchronized before preview
			// This includes XPath data that was selected for tooltip steps
			if (interactionData?.GuideStep) {
				console.log("Syncing AI tour data before preview", {
					currentStep: currentStep,
					selectedTemplateTour: selectedTemplateTour,
					interactionData: interactionData
				});

				// First, sync current step data to preserve any changes
				syncCurrentStepDataForAITour();

				// Apply global progress bar synchronization to ensure all steps have consistent settings
				syncGlobalProgressBarStateForAITour();

				// Then, ensure tooltip metadata is initialized for all tooltip steps
				initializeAITourTooltipMetadata();

				// Finally, sync tooltip metadata back to interactionData
				syncAITourDataForPreview();

				// Update updatedGuideData with latest interactionData
				updatedGuideData = {
					...interactionData,
					GuideId: updatedGuideData?.GuideId || interactionData.GuideId,
					Name: updatedGuideData?.Name || interactionData.Name,
					OrganizationId: updatedGuideData?.OrganizationId || interactionData.OrganizationId,
					AccountId: updatedGuideData?.AccountId || interactionData.AccountId,
				};

				// Update savedGuideData with the synchronized data
				setSavedGuideData(updatedGuideData);

				console.log("Updated savedGuideData for AI tour preview", {
					totalSteps: updatedGuideData?.GuideStep?.length,
					stepTypes: updatedGuideData?.GuideStep?.map((step: any, index: number) => ({
						stepIndex: index,
						stepType: step.StepType,
						elementPath: step.ElementPath,
						possibleElementPath: step.PossibleElementPath
					})),
					toolTipGuideMetaData: toolTipGuideMetaData?.map((metadata: any, index: number) => ({
						stepIndex: index,
						hasXPath: !!metadata?.xpath?.value,
						xpathValue: metadata?.xpath?.value
					}))
				});
			}
		}

		const styleExTag = document.getElementById("dynamic-body-style"); // for element selection need to show full screen
				if (styleExTag) {
					document.head.removeChild(styleExTag);
		}
		if ((
			(selectedTemplate === "Tour"   ||
			selectedTemplate === "Banner"
			) && Bposition == "Push Down")
		) {
			resetHeightofBanner("Push Down",0,0,false,0,0);
		} else if ((selectedTemplate === "Tour" || selectedTemplate === "Banner") && Bposition === "Cover Top") {
			// For Cover Top position in preview mode, call resetHeightofBanner with Cover Top
			resetHeightofBanner("Cover Top", 0, 0, false, 0, 0);
		}
		if (selectedTemplate === "Announcement") {
			// Synchronize AI announcement data before entering preview
			if (createWithAI) {
				// First sync metadata from interactionData
				syncAIAnnouncementDataForPreview(true); // Preserve global state during mode transition
				// Then sync container data from updated metadata
				syncAIAnnouncementContainerData();
				// Finally ensure savedGuideData is updated with latest progress bar state
				syncAnnouncementJsonWithMetaData();
			} else {
				// For non-AI announcements, sync button data
				syncButtonContainerToMetadata();
				syncMetadataToSavedGuideData();
			}

			setImageAnchorEl({
				buttonId: "",
				containerId: "",
				// @ts-ignore
				value: null,
			});
			setShowBannerenduser(false);
			setIsAnnouncementPopupOpen(true);
			setIsTourTemplate(false);
			setIsAnnouncementCreationBuilderOpen(true);
			setIsBannerPopupOpen(false);
			setAnnouncementData(savedGuideData);
			setShowHotspotenduser(false);
		} else if (selectedTemplate === "Banner") {
			setPreview(true);
			setIsTourTemplate(false);

			setShowHotspotenduser(false);
			setIsAnnouncementPopupOpen(false);
			setIsBannerPopupOpen(true);
			setIsBannerCreationBuilderOpen(true);
			setIsAnnouncementCreationBuilderOpen(false);
			setShowBannerenduser(true);
			setAnnouncementData(savedGuideData);
		} else if (selectedTemplate === "Tooltip") {
			// Synchronize tooltip data before entering preview
			if (!createWithAI) {
				// For non-AI tooltips, sync button data
				syncButtonContainerToMetadata();
				syncMetadataToSavedGuideData();
			}
			setPreview(true);
			setIsTourTemplate(false);

			setIsAnnouncementPopupOpen(false);
			setIsBannerPopupOpen(false);
			setIsBannerCreationBuilderOpen(false);
			setIsTooltipCreationBuilderOpen(true);
			setIsAnnouncementCreationBuilderOpen(false);
			setShowBannerenduser(false);
			setShowHotspotenduser(false);
			setIsTooltipPopup(true);
			setShowTooltipenduser(true);
			setAnnouncementData(savedGuideData);
			setShowTooltipCanvasSettings(false);
		} else if (selectedTemplate === "Hotspot") {
			const hotspot = document.getElementById("hotspotBlinkCreation");
			if (hotspot) {
				hotspot.style.display = "none";
			}
			setIsTourTemplate(false);

			setPreview(true);
			setIsAnnouncementPopupOpen(false);
			setIsBannerPopupOpen(false);
			setIsBannerCreationBuilderOpen(false);
			setIsAnnouncementCreationBuilderOpen(false);
			setIsHotspotCreationBuilderOpen(true);
			setShowBannerenduser(false);
			setShowHotspotenduser(true);
			setIsHotspotPopupOpen(true);
			setAnnouncementData(savedGuideData);
			setShowTooltipCanvasSettings(false);
		} else if (
			selectedTemplate === "Tour" &&
			["Hotspot", "Tooltip", "Announcement", "Banner"].includes(selectedTemplateTour)
		) {
			// Synchronize AI data for mixed tour types
			if (createWithAI) {
				// Call specific sync functions for current step type
				// Note: Tour-wide sync is now handled in setCurrentStep to avoid infinite loops
				if (selectedTemplateTour === "Announcement") {
					// For Tour+Announcement, use tooltip sync functions since they use toolTipGuideMetaData
					syncAITooltipContainerData();
				} else if (selectedTemplateTour === "Tooltip") {
					syncAITooltipContainerData();
				} else if (selectedTemplateTour === "Banner") {
					// For Tour+Banner, ensure banner data is synchronized
					syncAITooltipContainerData();
				} else if (selectedTemplateTour === "Hotspot") {
					// For Tour+Hotspot, ensure hotspot data is synchronized
					syncAITooltipContainerData();
				}
			} else {
				// For non-AI tours with announcement, tooltip, or banner steps, sync button data
				if (selectedTemplateTour === "Announcement" || selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Banner") {
					syncButtonContainerToMetadata();
					syncMetadataToSavedGuideData();
				}
			}

			const hotspot = document.getElementById("hotspotBlinkCreation");
			if (hotspot) {
				hotspot.style.display = "none";
			}

			setPreview(true);
			setIsHotspotPopupOpen(false);

			setIsAnnouncementPopupOpen(false);
			setIsHotspotCreationBuilderOpen(false);
			setIsBannerPopupOpen(false);
			setIsBannerCreationBuilderOpen(false);
			setIsAnnouncementCreationBuilderOpen(false);
			setIsTooltipCreationBuilderOpen(false);
			setIsTourTemplate(true);
			setShowBannerenduser(false);
			setShowHotspotenduser(false);
		} else if (selectedTemplate === "Checklist") {
			setPreview(true);
			setIsTourTemplate(false);

			setShowHotspotenduser(false);
			setIsAnnouncementPopupOpen(false);
			setIsBannerPopupOpen(false);
			setIsBannerCreationBuilderOpen(false);
			setIsAnnouncementCreationBuilderOpen(false);
			setShowBannerenduser(false);
			setAnnouncementData(savedGuideData);
			setIsChecklistPreview(true);
		}
	};

	// Function to synchronize preview data with actual steps
	const synchronizePreviewData = () => {
		// Ensure the preview data matches the actual steps
		if (updatedGuideData?.GuideStep && steps) {
			// Create a map of step IDs to steps for quick lookup
			const stepsMap = new Map();
			steps.forEach((step: any) => stepsMap.set(step.id, step));

			// Create a map of existing guide steps by ID
			const guideStepsMap = new Map();
			updatedGuideData.GuideStep.forEach((step: any) => {
				if (step.StepId) {
					guideStepsMap.set(step.StepId, step);
				}
			});

			// Create a new array of guide steps that matches the steps array
			const newGuideSteps: any[] = [];

			// For each step in the steps array, find or create a corresponding guide step
			steps.forEach((step: any, index: number) => {
				// Try to find an existing guide step with this ID
				let guideStep = guideStepsMap.get(step.id);

				if (guideStep) {
					// Update the existing guide step
					guideStep = {
						...guideStep,
						StepTitle: step.name,
						stepName: step.name,
						StepCount: index + 1,
						StepId: step.id,
						Description: step.stepDescription || ""
					};
				} else {
					// Create a new guide step if none exists
					guideStep = {
						StepId: step.id,
						StepTitle: step.name,
						stepName: step.name,
						StepCount: index + 1,
						StepType: step.stepType || "Announcement",
						Description: step.stepDescription || "",
						TextFieldProperties: [
							{
								Id: crypto.randomUUID(),
								Text: "",
								Alignment: "left",
								Hyperlink: "",
								Emoji: "",
								TextProperties: {
									Bold: false,
									Italic: false,
									BulletPoints: false,
									TextFormat: "normal",
									TextColor: "#000000"
								}
							}
						],
						ImageProperties: [],
						ButtonSection: [],
						Overlay: overlayEnabled,
						Tooltip: {
							EnableProgress: progress,
							ProgressTemplate: selectedOption?.toString() || "",
						}
					};
				}

				newGuideSteps.push(guideStep);
			});

			// Update the guide data with the new steps
			updatedGuideData.GuideStep = newGuideSteps;

			// If we have savedGuideData, synchronize that too
			if (savedGuideData?.GuideStep) {
				// Create a map of saved guide steps by ID
				const savedGuideStepsMap = new Map();
				savedGuideData.GuideStep.forEach((step: any) => {
					if (step.StepId) {
						savedGuideStepsMap.set(step.StepId, step);
					}
				});

				// Create a new array of saved guide steps
				const newSavedGuideSteps: any[] = [];

				// For each step in the steps array, find or create a corresponding saved guide step
				steps.forEach((step: any, index: number) => {
					// Try to find an existing saved guide step with this ID
					let savedGuideStep = savedGuideStepsMap.get(step.id);

					if (savedGuideStep) {
						// Update the existing saved guide step
						savedGuideStep = {
							...savedGuideStep,
							StepTitle: step.name,
							stepName: step.name,
							StepCount: index + 1,
							StepId: step.id,
							Description: step.stepDescription || ""
						};
					} else {
						// If no saved guide step exists, use the one from updatedGuideData
						savedGuideStep = newGuideSteps[index];
					}

					newSavedGuideSteps.push(savedGuideStep);
				});

				// Update the saved guide data with the new steps
				setSavedGuideData({
					...savedGuideData,
					GuideStep: newSavedGuideSteps
				});
			}

			// If we have currentGuide, synchronize that too
			if (currentGuide?.GuideStep) {
				// Create a map of current guide steps by ID
				const currentGuideStepsMap = new Map();
				currentGuide.GuideStep.forEach((step: any) => {
					if (step.StepId) {
						currentGuideStepsMap.set(step.StepId, step);
					}
				});

				// Create a new array of current guide steps
				const newCurrentGuideSteps: any[] = [];

				// For each step in the steps array, find or create a corresponding current guide step
				steps.forEach((step: any, index: number) => {
					// Try to find an existing current guide step with this ID
					let currentGuideStep = currentGuideStepsMap.get(step.id);

					if (currentGuideStep) {
						// Update the existing current guide step
						currentGuideStep = {
							...currentGuideStep,
							StepTitle: step.name,
							stepName: step.name,
							StepCount: index + 1,
							StepId: step.id,
							Description: step.stepDescription || ""
						};
					} else {
						// If no current guide step exists, use the one from updatedGuideData
						currentGuideStep = newGuideSteps[index];
					}

					newCurrentGuideSteps.push(currentGuideStep);
				});

				// Update the current guide with the new steps
				setCurrentGuide({
					...currentGuide,
					GuideStep: newCurrentGuideSteps
				});
			}
		}
	};

	const handleBackToEditorClick = () => {
		// Store the original unsaved changes state before preview to restore it
		const originalUnsavedState = getUnsavedChangesBeforePreview();

		// Set flag to indicate we're returning from preview mode
		// This prevents components from incorrectly setting isUnSavedChanges = true
		setIsReturningFromPreview(true);

		// Immediately restore the original save button state to prevent visual flicker
		// This preserves whether the user had unsaved changes before going to preview
		setIsUnSavedChanges(originalUnsavedState);

		// Synchronize AI data when going back to editor
		if (createWithAI && selectedTemplate === "Tooltip") {
			syncAITooltipContainerData();
			// Restore element click state from interactionData back to toolTipGuideMetaData
			restoreTooltipElementClickState();
			// CRITICAL FIX: Add delay to ensure UI updates after restoration
			setTimeout(() => {
				console.log("🔄 handleBackToEditorClick: Element click state restored for AI standalone tooltip");
				// Force a re-render of the Design component to ensure dropdown updates
				setIsUnSavedChanges((prev:any) => prev);
			}, 150);
		} else if (createWithAI && selectedTemplate === "Announcement") {
			// Pure announcement uses announcementGuideMetaData
			// First sync metadata from interactionData
			syncAIAnnouncementDataForPreview(true); // Preserve global state during mode transition
			// Then sync container data from updated metadata
			syncAIAnnouncementContainerData();
			// Finally sync announcement JSON with metadata to ensure savedGuideData is updated
			syncAnnouncementJsonWithMetaData();
		} else if (createWithAI && selectedTemplate === "Tour") {
			// For AI tours, restore RTE data from interactionData back to toolTipGuideMetaData
			restoreAITourRTEData();

			// Restore progress bar state from interactionData back to toolTipGuideMetaData
			restoreAITourProgressBarState();

			// Also sync tooltip container data for tours
			syncAITooltipContainerData();

			// For tooltip steps in tours, restore element click state
			if (selectedTemplateTour === "Tooltip") {
				restoreTooltipElementClickState();
				// CRITICAL FIX: Add delay to ensure UI updates after restoration
				setTimeout(() => {
					console.log("🔄 handleBackToEditorClick: Element click state restored for AI tour tooltip step");
					// Force a re-render of the Design component to ensure dropdown updates
					setIsUnSavedChanges((prev:any) => prev);
				}, 150);
			}
		} else if (createWithAI && selectedTemplateTour === "Announcement") {
			// Tour+Announcement uses toolTipGuideMetaData
			syncAITooltipContainerData();
		} else if (!createWithAI && (selectedTemplate === "Tooltip" || selectedTemplate === "Announcement" ||
			(selectedTemplate === "Tour" && (selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Announcement" || selectedTemplateTour === "Banner")))) {
			// For non-AI guides, sync metadata back to button container
			syncMetadataToButtonContainer();

			// CRITICAL FIX: Also restore manual tooltip dropdown state when returning from preview
			if (selectedTemplate === "Tooltip" || (selectedTemplate === "Tour" && selectedTemplateTour === "Tooltip")) {
				restoreManualTooltipElementClickState();
				// CRITICAL FIX: Add delay to ensure UI updates after restoration
				setTimeout(() => {
					console.log("🔄 handleBackToEditorClick: Element click state restored for manual tooltip");
					// Force a re-render of the Design component to ensure dropdown updates
					setIsUnSavedChanges((prev:any) => prev);
				}, 150);
			}
		}

		// Clear the flag after all operations complete and restore original state as safety measure
		// This includes the updatehotspots call which has a 100ms timeout
		// and any other async operations that might be triggered
		setTimeout(() => {
			setIsReturningFromPreview(false);
			// Restore the original unsaved changes state as a safety measure
			// in case any operations tried to modify it during the sync process
			setIsUnSavedChanges(originalUnsavedState);
		}, 150); // Increased timeout to ensure all effects complete before clearing flag

		// First, synchronize the preview data with the actual steps
		//synchronizePreviewData();

		// Clean up any duplicate steps
		cleanupDuplicateSteps();

		// Skip push-down for announcements
		if (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") {
			// Clean up any existing styles
			const styleExTag = document.getElementById("dynamic-body-style");
			if (styleExTag) {
				document.head.removeChild(styleExTag);
			}
			document.body.classList.remove("dynamic-body-style");
		} else if ((
			(selectedTemplate === "Tour"
				||
			selectedTemplate === "Banner"
			) && Bposition == "Push Down")
		) {
			resetHeightofBanner("Push Down", 0, 0, false, 0, 55);
		} else {
			// For banner creation mode, use the actual position
			if (selectedTemplate === "Banner" || selectedTemplateTour === "Banner") {
				resetHeightofBanner(Bposition || "Cover Top", 0, 0, false, 0, 55);
			} else {
				resetHeightofBanner("builder", 0, 0, false, 0, 55);
			}
		}
		// Store the current hotspot settings before resetting
		const currentHotspotSettings = toolTipGuideMetaData[currentStep - 1]?.hotspots;

		resetElementStyles();
		const hotspot = document.getElementById("hotspotBlink");
		hotspot?.remove();
		const hotspot1 = document.getElementById("hotspotBlinkCreation");
		if (selectedTemplate === "Hotspot") {
			if (hotspot1) {
				hotspot1.style.display = "block";
			}
		}

		// Clear history when navigating back to editor
		useHistoryStore.getState().clearHistory();

		// Close the modals and reset UI states
		setIsHotspotCreationBuilderOpen(false);
		setIsHotspotPopupOpen(false);
		setIsAnnouncementPopupOpen(false);
		setIsChecklistPreview(false);
		setIsTourTemplate(false);
		setIsAnnouncementCreationBuilderOpen(false);
		setIsBannerPopupOpen(false);
		setIsBannerCreationBuilderOpen(false);
		setIsDrawerClosed(true);
		setIsHomeScreen(true);
		setShowTooltipenduser(false);
		clearUserSession();
		setIsthreeDotOpen(false);
		setShowBannerenduser(false);
		setShowHotspotenduser(false);

		setAnchorEl(null);
		setTextvaluess("");

		// Ensure the steps array and guide data are in sync
		if (selectedTemplate === "Tour") {
			// Ensure we're using the correct step type
			getGuideTypeValue(
				updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType || "",
				updatedGuideData?.GuideStep ? updatedGuideData?.GuideStep : []
			);
			setSelectedTemplateTour(updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType || "", true);

			// Handle hotspot display
			if (updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType === "Hotspot") {
				if (hotspot1) {
					hotspot1.style.display = "block";
				}
			}

			// Set banner popup state based on step type
			if (updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType === "Banner") {
				setBannerPopup(true);
			} else {
				setBannerPopup(false);
			}
		} else {
			// For non-tour templates, use the guide type
			getGuideTypeValue(updatedGuideData?.GuideType, updatedGuideData?.GuideStep ? updatedGuideData?.GuideStep : []);
		}

		// Restore the hotspot settings if they exist
		if (currentHotspotSettings && (selectedTemplate === "Hotspot" ||
			(selectedTemplate === "Tour" && updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType === "Hotspot"))) {
			// Make sure to preserve the stopAnimationUponInteraction setting
			const preservedStopAnimation = currentHotspotSettings.stopAnimationUponInteraction;

			// Wait a short time to ensure the UI has updated before applying the settings
			setTimeout(() => {
				// Update the hotspot settings with the preserved values
				updatehotspots({
					...currentHotspotSettings,
					stopAnimationUponInteraction: preservedStopAnimation
				});
			}, 100);
		}
	};

	const handleCloseAnnouncementPopup = () => {
		setIsAnnouncementPopupOpen(false);
	};
	const handleCloseHotspotPopup = () => {
		setIsHotspotPopupOpen(false);
	};
	const handleCloseBannerPopup = () => {
		setIsBannerPopupOpen(false);
	};
	const handleLeave = () => {
		resetElementStyles();
		// Remove existing dynamic style if present
	const oldStyleTag = document.getElementById("dynamic-body-style");
	if (oldStyleTag) {
		document.head.removeChild(oldStyleTag);
	}
		const existingHotspot = document.getElementById("hotspotBlinkCreation");
		if (existingHotspot) {
			existingHotspot.remove();
		}
		setStepCreation(false);
		setEditClicked(false);
		setAnchorEl(null);
		setIsDrawerClosed(false);
		setIsHomeScreen(true);
		setIsGuideInfoScreen(false);
		setIsTemplateScreen(false);
		setIsPopupOpen(false);
		setBannerPopup(false);
		clearUserSession();
		setIsthreeDotOpen(false);
		clearGuideDetails();
		setStepData({
			stepNumber: "",
			type: steps[0].stepType,
			description: "",
		}); // HotspotGuideDetails();//removed these two and didnt run the code previously only one tooltip issue is there
		// TooltipGuideDetails();
		setBannerButtonSelected(false);
		setCurrentGuideId("");
		setTextvaluess("");
		setPreview(false);
		setElementSelected(false);
		selectedStepType = "";
		setOpenTooltip(false);
		setSelectedTemplate("");
		setTooltipCount(0);
		setOpenWarning(false);
		setCheckPointsPopup(false);
		setCheckPointsEditPopup(false);
		setCheckPointsAddPopup(false);
		setElementClick("element");
	};
	// Removed problematic useEffect that was overriding user overlay settings
	// The overlay and page interaction settings should be controlled by user actions only
	// and persist across mode transitions
	//const [openWarning, setOpenWarning] = useState(false);

	const handleClose = () => {
		resetElementStyles();

		// Close launcher popup when closing checklist guides
		if (selectedTemplate === "Checklist") {
			// setShowLauncherSettings(false); // Function not available in this scope
		}

		if (isUnSavedChanges) {
			setOpenWarning(true);
			setDeleteClicked(false);
			setStepCreation(false);
			setOpenStepDropdown(false);
			setCheckPointsPopup(false);
			setCheckPointsEditPopup(false);
			setCheckPointsAddPopup(false);
			setStepData({
				stepNumber: "",
				type: steps[0].stepType,
				description: "",
			});
		} else {
			setDeleteClicked(false);
			if (selectedTemplate == "Announcement") {
				handleLeave();

			} else if (toolTipGuideMetaData[currentStep - 1]?.xpath?.value === "" && !elementSelected) {
				handleLeave();
			} else if (closedGuide) {
				handleLeave();
				closedGuide = false;
			} else {
				setElementSelected(true);
			}
			setStepCreation(false);
			setOpenStepDropdown(false);
		}
	};
	useEffect(() => {
		const handleKeyDowns = (event: KeyboardEvent) => {
			if (event.key === "Escape" && !isShowIcon) {
				if (toolTipGuideMetaData[currentStep - 1]?.xpath?.value === "" && !elementSelected) {
					// Clean up highlights and ensure q key selection is enabled for next tooltip
					resetALTKeywordForNewTooltip();
					handleClose();
				} else {
					setElementSelected(true);
				}
			}
		};

		document.addEventListener("keydown", handleKeyDowns);
		return () => {
			document.removeEventListener("keydown", handleKeyDowns);
		};
	}, [handleClose]);

	const closeExtensionUI = () => {
		resetElementStyles();
		const existingHotspot = document.getElementById("hotspotBlinkCreation");
		if (existingHotspot) {
			existingHotspot.remove();
		}

		setCheckPointsPopup(false);
		setCheckPointsEditPopup(false);
		setCheckPointsAddPopup(false);
		setSelectedTemplateTour("");
		setIsAnnouncementPopupOpen(false);
		setIsChecklistPreview(false);
		setIsTourTemplate(false);
		setIsAnnouncementCreationBuilderOpen(false);
		setIsBannerPopupOpen(false);
		setIsBannerCreationBuilderOpen(false);
		setIsHotspotCreationBuilderOpen(false);
		setIsHotspotPopupOpen(false);
		setIsHomeScreen(true);
		setIsTooltipPopup(false);
		setShowTooltipenduser(false);
		setTooltipCount(0);
		setIsthreeDotOpen(false);
		setShowBannerenduser(false);
		setShowHotspotenduser(false);
		setAnchorEl(null);
		// Set extension closed state in store - this will handle popup closing
		setIsExtensionClosed(true);

		if (window.chrome?.runtime?.sendMessage) {
			window.chrome.runtime.sendMessage({ action: "closeExtension" }, (response) => {
				if (response?.success) {
					window.close();
				}
			});
		}
	};

	const handleExtensionClose = (shouldLogout: boolean = true) => {
		if (shouldLogout) {
			clearUserSession();
			clearGuideDetails();
			clearAll();
		} else {
			// When closing without logout, preserve the state
			setIsExtensionClosed(true);
		}
		window.location.reload();
		closeExtensionUI();
	};

	const handleDropdownOpen = (dropdownName: string) => {
		//	setOpenDropdown(dropdownName === openDropdown ? null : dropdownName);
	};
	const handleElementSelectionToggle = () => {
		setIsSelectingElement((prev) => !prev);
	};
	const handleOpenPortal = () => {
		window.open(`${process.env.REACT_APP_WEB_API}`, "_blank");
	};

	const userInfoObj = useInfoStore((state) => state.user);
	const orgDetails = useInfoStore((state) => state.orgDetails || "{}");
	const organizationId = orgDetails.OrganizationId;
	const [isAnnouncementOpen, setAnnouncementOpen] = useState(false);
	const [openStepDropdown, setOpenStepDropdown] = useState(false);
	// Function to map image properties to avoid repetition

	const mapImageProperties = (container: any) => ({
		Id: container.id,
		CustomImage: container.images.map((image: any) => ({
			Url: image?.url || "",
			AltText: image?.altText || "",
			BackgroundColor: container.style?.backgroundColor || "#ffffff",
			Fit: image?.objectFit || "cover",
			Fill: "contain",
			SectionHeight: String(container.style?.height || 0),
		})),
		MaxImageHeight: container.style?.maxHeight || 0,
		Alignment: "center",
		Padding: {
			Top: container.style?.paddingTop || 0,
			Right: container.style?.paddingRight || 0,
			Bottom: container.style?.paddingBottom || 0,
			Left: container.style?.paddingLeft || 0,
		},
		Hyperlink: container.hyperlink || "",
	});

	// Function to map button section to avoid repetition
	const mapButtonSection = (container: any) => ({
		Id: container.id,
		BackgroundColor: container.BackgroundColor || container.style.backgroundColor || "transparent",
		CustomButtons: container.buttons.map((button: any) => ({
			ButtonStyle: button.type,
			ButtonName: button.name,
			Alignment: button.position,
			ButtonId: button.actions?.Id,
			BackgroundColor: container.style.backgroundColor || "transparent",
			ButtonAction: {
				Action: button.actions?.value || "close",
				ActionValue: button.actions?.tab || "same-tab",
				TargetUrl: button.actions?.targetURL || "",
			},
			Padding: {
				Top: 0,
				Right: 0,
				Bottom: 0,
				Left: 0,
			},
			ButtonProperties: {
				Padding: 0,
				Width: 0,
				Font: 0,
				FontSize: 0,
				ButtonTextColor: button.style.color || "#ffffff",
				ButtonBackgroundColor: button.style.backgroundColor || "#5F9EA0",
				ButtonBorderColor: button.style.borderColor || "#5F9EA0",
			},
		})),
	});
	const TmapButtonSection = (container: any) => ({
		Id: container.id,
		BackgroundColor: container.BackgroundColor || container.style.backgroundColor || "transparent",
		CustomButtons: container.buttons.map((button: any) => ({
			ButtonStyle: button.type,
			ButtonName: button.name,
			Alignment: button.position,
			ButtonId: button.actions?.Id,
			BackgroundColor: container.style.backgroundColor || "transparent",
			ButtonAction: {
				Action: button.actions?.value || "close",
				ActionValue: button.actions?.tab || "same-tab",
				TargetUrl: button.actions?.targetURL || "",
			},
			Padding: {
				Top: 0,
				Right: 0,
				Bottom: 0,
				Left: 0,
			},
			ButtonProperties: {
				Padding: 0,
				Width: 0,
				Font: 0,
				FontSize: 0,
				ButtonTextColor: button.style.color || "#ffffff",
				ButtonBackgroundColor: button.style.backgroundColor || "#5F9EA0",
				ButtonBorderColor: button.style.borderColor || "#5F9EA0",
			},
		})),
	});

	// Function to determine the canvas settings based on selectedTemplate
	const getCanvasPosition = () => {
		if (selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot") {
			return {
				Position: tooltipPosition,
				Padding: tooltippadding,
				Radius: tooltipborderradius,
				BorderSize: tooltipbordersize,
				BorderColor: tooltipBordercolor,
				BackgroundColor: tooltipBackgroundcolor,
				Width: tooltipWidth,
			};
		}

		if (selectedTemplate === "Banner") {
			return {
				Position: "absolute",
				Padding: bpadding,
				Radius: "",
				BorderSize: BborderSize,
				BorderColor: Bbordercolor,
				BackgroundColor: backgroundC,
				Width: "100%",
			};
		}

		return {}; // Default case for other templates
	};

	const initialGuideDataStructure: GuideData = {
		GuideType: selectedTemplate,
		GuideId: currentGuideId || "",
		Name: guideName,
		OrganizationId: organizationId,
		AccountId: selectedAccountId,
		GuideStatus: "Draft",
		CreatedDate: new Date().toISOString(),
		UpdatedDate: new Date().toISOString(),
		TargetUrl: startingUrl,
		// Ensure GuideStep starts with existing steps if available
		GuideStep: (currentGuide?.GuideStep || []).map((step: any, index: any) => ({
			...step,
			ElementPath: "",
			PossibleElementPath: "",
			Position: {},
			TextFieldProperties: rtesContainer
				.map((rteContainer: any) =>
					rteContainer.rtes.map((rte: any) => ({
						Id: rte.id || `textField-${Math.random().toString(36).substr(2, 9)}`,
						Text: rte.text || "",
						Alignment: "",
						Hyperlink: "",
						Emoji: "",
						TextProperties: {
							Bold: false,
							Italic: false,
							BulletPoints: false,
							TextFormat: "",
							TextColor: "",
						},
					}))
				)
				.flat(),
			ButtonSection: bannerButtonSelected ? buttonsContainer.map(mapButtonSection) : [],
			ImageProperties: imagesContainer.map(mapImageProperties),
			StepTitle: steps[currentStep - 1]?.name,
			Description: steps[currentStep - 1]?.stepDescription,
			StepType: steps[currentStep - 1]?.stepType,
			Overlay: selectedTemplate === "Banner" || selectedTemplateTour === "Banner" ? selectedTemplate === "Tour" ?"false": overlays : selectedTemplate === "Tour"?"false": overlayEnabled,
			Arrow: false,
			IsClickable: false,
			HtmlSnippet: htmlContent,
			Modal: {
				InteractionWithPopup: true,
				IncludeRequisiteButtons: true,
				DismissOption: dismissData?.dismisssel ?? false,
				ModalPlacedOn: "",
			},
			Canvas: getCanvasPosition(),
			Design: {
				ViewPortWidth: "",
				BackdropShadow:selectedTemplate === "Tour" ?false: overlayEnabled || false,
				QuietIcon: true,
				IconColor: "",
				GotoNext: {
					NextStep: step?.Design?.GotoNext?.NextStep || "",
					ButtonId: step?.Design?.GotoNext?.ButtonId || "	",
					ElementPath: step?.Design?.GotoNext?.ElementPath || "",
					ButtonName: step?.Design?.GotoNext?.ButtonName || "",
				},
			},
			Advanced: {
				ShowAfter: "",
				OnScrollDelay: "",
			},
			tooltipplacement: "",
			LayoutPositions: [],
		})),
	};

	// Define state for initial guide data
	const [initialGuideData, setInitialGuideData] = useState(initialGuideDataStructure);

	const formatTooltipGuideData = () => {
		if (!Array.isArray(toolTipGuideMetaData)) {
			console.error("announcementGuideMetaData is not an array", toolTipGuideMetaData);
			return;
		}
		const hotspotPropData = toolTipGuideMetaData && toolTipGuideMetaData[0]?.hotspots;
		const GuideStep: GuideStep[] = [];
		toolTipGuideMetaData.forEach((gStep) => {
			const ButtonSection: any[] = [];
			const TextFieldProperties: any[] = [];
			const ImageProperties: any[] = [];
			gStep.containers.forEach((container: any) => {
				switch (container.type) {
					case "button":
						const buttonValue = {
							Id: crypto.randomUUID(),
							CustomButtons: container.buttons.map((button: any) => ({
								ButtonStyle: button.type, // 'primary' or 'secondary'
								ButtonName: button.name,
								Alignment: button.position,
								ButtonId: button?.id,
								BackgroundColor: container.style.backgroundColor,
								ButtonAction: {
									Action: button?.id === gStep?.design?.gotoNext?.ButtonId ? "Next" : button.actions?.value || "",
									ActionValue: button.actions?.tab || "",
									TargetUrl: button.actions?.targetURL || button?.actions?.targetUrl || "",
								},
								Padding: {
									Top: 0,
									Right: 0,
									Bottom: 0,
									Left: 0,
								},
								ButtonProperties: {
									Padding: 0,
									Width: 0,
									Font: 0,
									FontSize: 0,
									ButtonTextColor: button.style.color,
									ButtonBackgroundColor: button.style.backgroundColor,
									ButtonBorderColor: button.style.borderColor,
								},
							})),
						};
						ButtonSection.push(buttonValue);

						break;
					case "image":
						const imageValue = {
							Id: container.id,
							CustomImage: container.images.map((image: any) => ({
								Url: image?.url || "",
								AltText: image?.altText || "",
								BackgroundColor: container.style?.backgroundColor || "#ffffff",
								Fit: image?.objectFit || "cover",
								Fill: "contain",
								SectionHeight: String(container.style?.height || 0),
							})),
							MaxImageHeight: container.style?.maxHeight || 0, // Corresponds to MaxImageHeight in backend
							Alignment: "center", // Corresponds to Alignment in backend
							Padding: {
								Top: container.style?.paddingTop || 0,
								Right: container.style?.paddingRight || 0,
								Bottom: container.style?.paddingBottom || 0,
								Left: container.style?.paddingLeft || 0,
							}, // Matches backend PaddingProperties
							Hyperlink: container.hyperlink || "", // Corresponds to Hyperlink in backend
						};
						ImageProperties.push(imageValue);
						break;
					case "rte":
						const rteValue = {
							Id: crypto.randomUUID(),
							Text: container.rteBoxValue, // Extract text from rtesContainer
							Alignment: "",
							Hyperlink: "",
							Emoji: "",
							TextProperties: {
								Bold: false, // Default properties; update these if provided in rte
								Italic: false,
								BulletPoints: false,
								TextFormat: "",
								TextColor: "",
							},
						};
						TextFieldProperties.push(rteValue);
						break;

					default:
						break;
				}
			});

			GuideStep.push({
				ButtonSection,
				TextFieldProperties,
				ImageProperties,
				StepTitle: gStep.stepName,
				Description: gStep?.stepDescription,
				StepId: gStep?.stepId,
				StepType: gStep?.stepType,
				StepTargetURL: gStep?.stepTargetURL || "",
				// @ts-ignore
				Overlay: selectedTemplate === "Tour" ?false:  overlayEnabled,
				Arrow: false,
				IsClickable: false,
				HtmlSnippet: "",
				VideoEmbedCode: "",

				Classes: "",
				AutoPosition: autoPosition,
				Position: {
					XAxisOffset: gStep?.xpath?.position?.x,
					YAxisOffset: gStep?.xpath?.position?.y,
				},
				ElementPath: gStep?.xpath?.value,
				PossibleElementPath: gStep?.xpath?.PossibleElementPath,
				// here Prudhvi will add code of canvas
				Canvas: {
					Position: gStep?.canvas?.position || "middle-center",
					Padding: gStep?.canvas?.padding || "4",
					Radius: gStep?.canvas?.borderRadius || "",
					BorderSize: gStep?.canvas?.borderSize || "2",
					BorderColor: gStep?.canvas?.borderColor || "",
					BackgroundColor: gStep?.canvas?.backgroundColor || "",
					Width: gStep?.stepType !== "Banner" ? gStep?.canvas?.width || "500" : undefined,
					// @ts-ignore
					XAxisOffset: gStep?.stepType !== "Banner" ? gStep?.canvas?.xaxis || "4px" : undefined,
					// @ts-ignore
					YAxisOffset: gStep?.stepType !== "Banner" ? gStep?.canvas?.yaxis || "4px" : undefined,
					Zindex: gStep?.stepType !== "Banner" ? "" : 9999,
				},
				Modal: {
					InteractionWithPopup: true,
					IncludeRequisiteButtons: true,
					DismissOption: dismiss ?? false,
					ModalPlacedOn: "",
					ProgressColor: ProgressColor,
				},
				Design: {
					ViewPortWidth: "",
					BackdropShadow: false,
					QuietIcon: true,
					IconColor: "",
					GotoNext: {
						NextStep:
							gStep?.design?.gotoNext?.NextStep === "element" ? "" : gStep?.design?.gotoNext?.NextStep || "element",
						ButtonId: gStep?.design?.gotoNext?.NextStep === "element" ? "" : gStep?.design?.gotoNext?.ButtonId || "",
						ElementPath: gStep?.xpath?.value || "",
						ButtonName: gStep?.design?.gotoNext?.ButtonName || "",
					},
				},
				Tooltip: {
					EnableProgress: progress,
					Color: "",
					ProgressTemplate: selectedOption?.toString() || "",
					GotoNextStep: "",
					ButtonName: "",
					InteractWithPage: pageinteraction,
				},
				Hotspot: {
					HotspotPosition: {
						XOffset: hotspotPropData?.XPosition,
						YOffset: hotspotPropData?.YPosition,
					},
					Type: hotspotPropData?.Type,
					Color: hotspotPropData?.Color,
					Size: hotspotPropData?.Size,
					PulseAnimation: pulseAnimationsH,
					StopAnimation: hotspotPropData?.stopAnimationUponInteraction,
					ShowUpon: hotspotPropData?.ShowUpon,
					ShowByDefault: hotspotPropData?.ShowByDefault,
				},
				Advanced: {
					ShowAfter: "",
					OnScrollDelay: "",
				},
				LayoutPositions: [],
				Animation: {
					DelightAnimation: "",
					EntryAnimation: "",
				},
			});
		});
		const tooltipGudieData: GuideData = {
			GuideType: selectedTemplate,
			GuideId: currentGuideId || "",
			Name: guideName || "New Guide1",
			OrganizationId: organizationId,
			AccountId: selectedAccountId,
			GuideStatus: "Draft",
			CreatedDate: new Date().toISOString(),
			UpdatedDate: new Date().toISOString(),
			TargetUrl: startingUrl,

			GuideStep,
		};
		return tooltipGudieData;
	};
	const [plusIconclick, setPlusIconClick] = useState(false);
	const [aiCreationComplete, setAiCreationComplete] = useState(false);

	const handleStepsDropdown = () => {
		// Reset AI state when manually creating steps
		if (createWithAI) {
			setCreateWithAI(false);
		}
		setPlusIconClick(true);
		setDesignPopup(false);
		setStepData({
			...stepData,
			type: "",
		});

		// Set the step name for the new step
		setStepName(`Step ${steps.length + 1}`);

		// First open the dropdown, then set step creation
		setOpenStepDropdown(true);
		// Use setTimeout to ensure the dropdown state is set before step creation
		setTimeout(() => {
			setStepCreation(true);
		}, 0);
	};
	useEffect(() => {
		// Only reset stepCreation and isEditStepName when dropdown closes AND we're not in the middle of creating a step
		if (!openStepDropdown && !plusIconclick) {
			setStepCreation(false);
			setIsEditStepName(false);
			setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
		}
		// Reset plusIconclick when dropdown closes
		if (!openStepDropdown) {
			setPlusIconClick(false);
		}
	}, [openStepDropdown, plusIconclick]);
	const handleOpenStepDropdown = () => {
		setOpenStepDropdown((prevState) => !prevState);
		// Don't reset stepCreation here if we're opening the dropdown for step creation
		if (stepCreation && !plusIconclick) {
			setStepCreation(false);
		}
		setDesignPopup(false);
		setStepName(`Step ${steps.length + 1}`);
		setErrorInStepName({ error: false, message: "" });
		setCurrentStepNameId("");
		setEditType("Announcement");
		setDescription("");
		setIsEditStepName(false);
		setShowTextField(false);
	};

	const defaultButtonSection = [
		{
			Id: crypto.randomUUID(), // Ensure this ID is unique or adjust as needed
			CustomButtons: [
				{
					ButtonStyle: "primary",
					ButtonName: selectedTemplate === "Tooltip" ? "Click Here" : "Got It",
					Alignment: "center",
					BackgroundColor: "transparent",
					ButtonId: "",
					ButtonAction: {
						Action: "close", // Default action
						ActionValue: "same-tab", // Default action value
						TargetUrl: "", // Default empty target URL
					},
					Padding: {
						Top: 0,
						Right: 0,
						Bottom: 0,
						Left: 0,
					},
					ButtonProperties: {
						Padding: 0,
						Width: 0,
						Font: 0,
						FontSize: 0,
						ButtonTextColor: "#ffffff",
						ButtonBackgroundColor: "#5F9EA0",
						ButtonBorderColor: "#5F9EA0",
					},
				},
			],
		},
	];
	let responseData = null;
	const handleWebOpenPortal = async () => {

		if (createWithAI) {
			responseData = interactionData;
		}
		 else {
			responseData = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);
		}
		const queryParams = new URLSearchParams();
		queryParams.set("data", JSON.stringify(response));
		window.open(`${process.env.REACT_APP_WEB_API}/${selectedTemplate.toLowerCase()}s/${currentGuideId}/settings`);
	};
	const additionalData = {
		Content: "Default Content",
		CreatedBy: "UserID",
		UpdatedBy: "UserID",
		Frequency: "Daily",
		Segment: "",
	};
	const formatAnnouncementGuideData = () => {
		if (!Array.isArray(announcementGuideMetaData)) {
			console.error("announcementGuideMetaData is not an array", announcementGuideMetaData);
			return;
		}
		const hotspotPropData = announcementGuideMetaData && announcementGuideMetaData[0]?.hotspots;
		const GuideStep: GuideStep[] = [];
		announcementGuideMetaData.forEach((gStep: any) => {
			const ButtonSection: any[] = [];
			const TextFieldProperties: any[] = [];
			const ImageProperties: any[] = [];
			gStep.containers.forEach((container: any) => {
				switch (container.type) {
					case "button":
						const buttonValue = {
							Id: crypto.randomUUID(),
							CustomButtons: container.buttons.map((button: any) => ({
								ButtonStyle: button.type, // 'primary' or 'secondary'
								ButtonName: button.name,
								Alignment: button.position,
								ButtonId: button?.id,
								BackgroundColor: container.style.backgroundColor,
								ButtonAction: {
									Action: button?.id === gStep?.design?.gotoNext?.ButtonId ? "Next" : button.actions?.value || "",
									ActionValue: button.actions?.tab || "",
									TargetUrl: button.actions?.targetURL || "",
								},
								Padding: {
									Top: 0,
									Right: 0,
									Bottom: 0,
									Left: 0,
								},
								ButtonProperties: {
									Padding: 0,
									Width: 0,
									Font: 0,
									FontSize: 0,
									ButtonTextColor: button.style.color,
									ButtonBackgroundColor: button.style.backgroundColor,
									ButtonBorderColor: button.style.borderColor,
								},
							})),
						};
						ButtonSection.push(buttonValue);

						break;
					case "image":
						const imageValue = {
							Id: container.id,
							CustomImage: container.images.map((image: any) => ({
								Url: image?.url || "",
								AltText: image?.altText || "",
								BackgroundColor: container.style?.backgroundColor || "#ffffff",
								Fit: image?.objectFit || "cover",
								Fill: "contain",
								SectionHeight: String(container.style?.height || 0),
							})),
							MaxImageHeight: container.style?.maxHeight || 0, // Corresponds to MaxImageHeight in backend
							Alignment: "center", // Corresponds to Alignment in backend
							Padding: {
								Top: container.style?.paddingTop || 0,
								Right: container.style?.paddingRight || 0,
								Bottom: container.style?.paddingBottom || 0,
								Left: container.style?.paddingLeft || 0,
							}, // Matches backend PaddingProperties
							Hyperlink: container.hyperlink || "", // Corresponds to Hyperlink in backend
						};
						ImageProperties.push(imageValue);
						break;
					case "rte":
						const rteValue = {
							Id: crypto.randomUUID(),
							Text: container.rteBoxValue, // Extract text from rtesContainer
							Alignment: "",
							Hyperlink: "",
							Emoji: "",
							TextProperties: {
								Bold: false, // Default properties; update these if provided in rte
								Italic: false,
								BulletPoints: false,
								TextFormat: "",
								TextColor: "",
							},
						};
						TextFieldProperties.push(rteValue);
						break;

					default:
						break;
				}
			});
			GuideStep.push({
				ButtonSection,
				TextFieldProperties,
				ImageProperties,

				StepId: gStep?.StepId,
				StepTitle: gStep.stepName ? gStep.stepName : gStep.StepTitle,
				Description: gStep?.stepDescription || gStep?.StepDescription,
				StepType: gStep?.stepType || gStep?.StepType,
				StepTargetURL: gStep?.stepTargetURL || "",
				// @ts-ignore
				Overlay:selectedTemplate === "Tour" ?false:  overlayEnabled,
				Arrow: false,
				IsClickable: false,
				HtmlSnippet: "",
				VideoEmbedCode: "",
				Classes: "",
				AutoPosition: autoPosition,
				Position: {
					XAxisOffset: gStep?.xpath?.position?.x,
					YAxisOffset: gStep?.xpath?.position?.y,
				},
				ElementPath: gStep?.xpath?.value,
				PossibleElementPath: gStep?.xpath?.PossibleElementPath,
				Canvas: {
					Position: gStep?.canvas?.position || "middle-center",
					Padding: gStep?.canvas?.padding || "12",
					Radius: gStep?.canvas?.borderRadius || "8",
					BorderSize: gStep?.canvas?.borderSize || "0",
					BorderColor: gStep?.canvas?.borderColor || "",
					BackgroundColor: gStep?.canvas?.backgroundColor || "",
					Width: gStep?.canvas?.width || "500",
					// @ts-ignore
					XAxisOffset: gStep?.canvas?.xaxis || "4",
					// @ts-ignore
					YAxisOffset: gStep?.canvas?.yaxis || "4",
					Zindex: "",
				},
				Modal: {
					InteractionWithPopup: true,
					IncludeRequisiteButtons: true,
					DismissOption: dismiss ?? false,
					ModalPlacedOn: "",
					ProgressColor: ProgressColor,
				},
				Design: {
					ViewPortWidth: "",
					BackdropShadow: false,
					QuietIcon: true,
					IconColor: "",
					GotoNext: {
						NextStep:
							gStep?.design?.gotoNext?.NextStep === "element" ? "" : gStep?.design?.gotoNext?.NextStep || "element",
						ButtonId: gStep?.design?.gotoNext?.NextStep === "element" ? "" : gStep?.design?.gotoNext?.ButtonId || "",
						ElementPath: gStep?.xpath?.value || "",
						ButtonName: gStep?.design?.gotoNext?.ButtonName || "",
					},
				},
				Tooltip: {
					EnableProgress: progress,
					Color: "",
					ProgressTemplate: selectedOption?.toString() || "",
					GotoNextStep: "",
					ButtonName: "",
					InteractWithPage: pageinteraction,
				},
				Hotspot: {
					HotspotPosition: {
						XOffset: hotspotPropData?.XPosition,
						YOffset: hotspotPropData?.YPosition,
					},
					Type: hotspotPropData?.Type,
					Color: hotspotPropData?.Color,
					Size: hotspotPropData?.Size,
					PulseAnimation: pulseAnimationsH,
					StopAnimation: hotspotPropData?.stopAnimationUponInteraction,
					ShowUpon: hotspotPropData?.ShowUpon,
					ShowByDefault: hotspotPropData?.ShowByDefault,
				},
				Advanced: {
					ShowAfter: "",
					OnScrollDelay: "",
				},
				LayoutPositions: [],
				Animation: {
					DelightAnimation: "",
					EntryAnimation: "",
				},
			});
		});
		const AnnouncementGudieData: GuideData = {
			GuideType: selectedTemplate,
			GuideId: currentGuideId || "",
			Name: guideName || "New Guide1",
			OrganizationId: organizationId,
			AccountId: selectedAccountId,
			GuideStatus: "Draft",
			CreatedDate: new Date().toISOString(),
			UpdatedDate: new Date().toISOString(),
			TargetUrl: startingUrl,

			GuideStep,
		};
		return AnnouncementGudieData;
	};

	const formatChecklistGuideData = () => {
		if (!Array.isArray(checklistGuideMetaData)) {
			console.error("announcementGuideMetaData is not an array", checklistGuideMetaData);
			return;
		}
		const hotspotPropData = toolTipGuideMetaData && toolTipGuideMetaData[0]?.hotspots;
		const GuideStep: GuideStep[] = [];
		checklistGuideMetaData.forEach((gStep: any) => {
			GuideStep.push({
				Canvas: {
					Position: "",
					Padding: "",
					CornerRadius: gStep?.canvas?.cornerRadius || "12",
					BorderSize: "",
					BorderWidth: gStep?.canvas?.borderWidth || "0",
					PrimaryColor: gStep?.canvas?.primaryColor || "",
					BorderColor: gStep?.canvas?.borderColor || "",
					BackgroundColor: gStep?.canvas?.backgroundColor || "",
					Width: gStep?.canvas?.width || "500",
					Height: gStep?.canvas?.height || "500",

					// @ts-ignore
					XAxisOffset: gStep?.canvas?.xaxis || gStep?.launcher?.launcherposition?.xaxisOffset || "10",
					// @ts-ignore
					YAxisOffset: gStep?.canvas?.yaxis || gStep?.launcher?.launcherposition?.yaxisOffset || "10",
					Zindex: "",
					OpenByDefault: gStep?.canvas?.openByDefault || false,
					HideAfterCompletion: gStep?.canvas?.hideAfterCompletion,
				},
				Modal: {
					InteractionWithPopup: true,
					IncludeRequisiteButtons: true,
					DismissOption: dismiss ?? false,
					ModalPlacedOn: "",
					ProgressColor: ProgressColor,
				},
				Design: {
					ViewPortWidth: "",
					BackdropShadow: false,
					QuietIcon: true,
					IconColor: "",
					GotoNext: {
						NextStep:
							gStep?.design?.gotoNext?.NextStep === "element" ? "" : gStep?.design?.gotoNext?.NextStep || "element",
						ButtonId: gStep?.design?.gotoNext?.NextStep === "element" ? "" : gStep?.design?.gotoNext?.ButtonId || "",
						ElementPath: gStep?.xpath?.value || "",
						ButtonName: gStep?.design?.gotoNext?.ButtonName || "",
					},
				},
				Tooltip: {
					EnableProgress: progress,
					Color: "",
					ProgressTemplate: selectedOption?.toString() || "",
					GotoNextStep: "",
					ButtonName: "",
					InteractWithPage: pageinteraction,
				},
				Hotspot: {
					HotspotPosition: {
						XOffset: hotspotPropData?.XPosition,
						YOffset: hotspotPropData?.YPosition,
					},
					Type: hotspotPropData?.Type,
					Color: hotspotPropData?.Color,
					Size: hotspotPropData?.Size,
					PulseAnimation: pulseAnimationsH,
					StopAnimation: hotspotPropData?.stopAnimationUponInteraction,
					ShowUpon: hotspotPropData?.ShowUpon,
					ShowByDefault: hotspotPropData?.ShowByDefault,
				},
				Advanced: {
					ShowAfter: "",
					OnScrollDelay: "",
				},
				LayoutPositions: [],
				Animation: {
					DelightAnimation: "",
					EntryAnimation: "",
				},
				Launcher: {
					Type: gStep?.launcher.type,
					Icon: gStep?.launcher?.icon,
					IconColor: gStep?.launcher?.iconColor,
					LauncherColor: gStep?.launcher?.launcherColor,
					Text: gStep?.launcher?.text,
					TextColor: gStep?.launcher?.textColor,

					LauncherPosition: {
						Left: gStep?.launcher?.launcherposition?.left,
						Right: gStep?.launcher?.launcherposition?.right,
						XAxisOffset: gStep?.launcher?.launcherposition?.xaxisOffset,
						YAxisOffset: gStep?.launcher?.launcherposition?.yaxisOffset,
					},
					NotificationBadge: gStep?.launcher?.notificationBadge,
					NotificationBadgeColor: gStep?.launcher?.notificationBadgeColor,
					NotificationBadgeText: gStep?.launcher?.notificationTextColor,
				},

				TitleSubTitle: {
					Title: gStep?.TitleSubTitle?.title,
					TitleColor: gStep?.TitleSubTitle?.titleColor,
					TitleBold: gStep?.TitleSubTitle?.titleBold,
					TitleItalic: gStep?.TitleSubTitle?.titleItalic,
					SubTitle: gStep?.TitleSubTitle?.subTitle,
					SubTitleColor: gStep?.TitleSubTitle?.subTitleColor,
					SubTitleBold: gStep?.TitleSubTitle?.subTitleBold,
					SubTitleItalic: gStep?.TitleSubTitle?.subTitleItalic,
				},

				Checkpoint: {
					CheckpointItem: gStep?.checkpoints.checkpointsList,
					CheckpointTitleColor: gStep?.checkpoints.checkpointTitles,
					CheckpointDescriptionColor: gStep?.checkpoints.checkpointsDescription,
					CheckpointIconsColor: gStep?.checkpoints.checkpointsIcons,
					UnlockCheckpointsInOrder: gStep?.checkpoints.unlockCHeckpointInOrder,
					Message: gStep?.checkpoints?.message,
				},
			});
		});
		const CHecklistGuideData: GuideData = {
			GuideType: selectedTemplate,
			GuideId: currentGuideId || "",
			Name: guideName || "New Guide1",
			OrganizationId: organizationId,
			AccountId: selectedAccountId,
			GuideStatus: "Draft",
			CreatedDate: new Date().toISOString(),
			UpdatedDate: new Date().toISOString(),
			TargetUrl: startingUrl,

			GuideStep,
		};
		return CHecklistGuideData;
	};
	useEffect(() => {
		if (createWithAI)
		{
			// Prevent setting unsaved changes if returning from preview
			if (!useDrawerStore.getState().isReturningFromPreview) {
				setIsUnSavedChanges(true);
			}
			// Only reset step creation states when AI initially creates content, not during subsequent interactions
			// Use a timeout to avoid interfering with user interactions
			const timeoutId = setTimeout(() => {
				// Check if user is actively trying to create a step before closing the popup
				// This prevents the popup from closing when user clicks "Create Step" after AI guide creation
				if (!stepCreation) {
					setStepCreation(false);
					setPlusIconClick(false);
					setIsEditStepName(false);
					setOpenStepDropdown(false);
				}
				setAiCreationComplete(true);
			}, 100);

			return () => clearTimeout(timeoutId);
		} else {
			setAiCreationComplete(false);
			// When createWithAI is set to false, ensure step creation functionality is re-enabled
			// But don't close the popup if user is actively trying to create a step
			if (!stepCreation) {
				setStepCreation(false);
				setIsEditStepName(false);
				setPlusIconClick(false);
			}
		}
	},[createWithAI])

	const handleSaveGuide = async () => {
		// Synchronize AI data before saving
		if (createWithAI && (selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot")) {
			syncAITooltipContainerData();
			// Ensure element click state is properly synchronized before saving
			restoreTooltipElementClickState();
		} else if (createWithAI && selectedTemplate === "Announcement") {
			// Pure announcement uses announcementGuideMetaData
			syncAIAnnouncementContainerData();
		} else if (createWithAI && selectedTemplateTour === "Announcement") {
			// Tour+Announcement uses toolTipGuideMetaData
			syncAITooltipContainerData();
			console.log("Synced Tour+Announcement data before saving using tooltip sync");
		} else if (createWithAI && selectedTemplate === "Tour" && selectedTemplateTour === "Tooltip") {
			// Tour+Tooltip uses toolTipGuideMetaData
			syncAITooltipContainerData();
			// Ensure element click state is properly synchronized before saving
			restoreTooltipElementClickState();
			console.log("Synced Tour+Tooltip data before saving using tooltip sync");
		}

		// const additionalData = {
		// 	Content: "Default Content",
		// 	CreatedBy: "UserID",
		// 	UpdatedBy: "UserID",
		// 	Frequency: "Daily",
		// 	Segment: "",
		// };
		const currentStepData = {
			TextFieldProperties: rtesContainer
				.map(
					(rteContainer: any) =>
						Array.isArray(rteContainer.rtes) // Check if rtes is an array
							? rteContainer.rtes.map((rte: any) => ({
									Id: rte.id || `textField-${Math.random().toString(36).substr(2, 9)}`,
									Text: rte.text || "",
									Alignment: alignment,
									Hyperlink: "",
									Emoji: "",
									TextProperties: {
										Bold: false,
										Italic: false,
										BulletPoints: false,
										TextFormat: "",
										TextColor: "",
									},
							  }))
							: [] // If rtes is not an array, return an empty array
				)
				.reduce((acc: any, curr: any) => acc.concat(curr), []), // Flatten the resulting array

			ButtonSection:
				selectedTemplate === "Banner" || selectedTemplateTour === "Banner"
					? bannerButtonSelected
						? buttonsContainer.map(mapButtonSection)
						: []
					: elementClick === "button" &&
					  (selectedTemplate === "Tooltip" || selectedStepType === "Tooltip" || selectedTemplateTour === "Tooltip")
					? buttonsContainer.map(TmapButtonSection)
					: buttonsContainer.map(mapButtonSection),
			ImageProperties: imagesContainer.map(mapImageProperties),
			StepId: steps[currentStep - 1]?.id,
			StepTitle: steps[currentStep - 1]?.name,
			Description: steps[currentStep - 1]?.stepDescription,
			StepType: steps[currentStep - 1]?.stepType,
			Overlay: selectedTemplate === "Banner" || selectedTemplateTour === "Banner" ? selectedTemplate === "Tour" ?"false": overlays : selectedTemplate === "Tour"?"false": overlayEnabled,
			Arrow: false,
			IsClickable: false,
			HtmlSnippet: htmlContent,
			Modal: {
				InteractionWithPopup: true,
				IncludeRequisiteButtons: true,
				DismissOption: dismiss ?? false,
				ModalPlacedOn: "",
				ProgressColor: ProgressColor,
			},
			Position: {
				XAxisOffset: tooltipXaxis || "",
				YAxisOffset: tooltipYaxis || "",
			},
			Canvas:
				selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement"
					? announcementJson?.GuideStep?.find((step: any) => step.stepName === currentStep)?.Canvas || {}
					: selectedTemplate === "Banner" || selectedTemplateTour === "Banner"
					? bannerJson?.GuideStep?.find((step: any) => step.stepName === currentStep)?.Canvas || {}
					: {}, // Fallback for other templates or cases

			Design: {
				ViewPortWidth: "",
				BackdropShadow:selectedTemplate === "Tour" ?false:  overlayEnabled || false,
				QuietIcon: true,
				IconColor: "",
				GotoNext: {
					NextStep: "element",
					ButtonId: "Button",
					ElementPath: "",
				},
			},
			Tooltip: {
				EnableProgress: progress,
				Color: "",
				ProgressTemplate: selectedOption?.toString() || "",
				GotoNextStep: "",
				ButtonName: "",
				InteractWithPage: pageinteraction,
			},
			Advanced: {
				ShowAfter: "",
				OnScrollDelay: "",
			},
			tooltipplacement: "",
			LayoutPositions: [],
		};

		// Update GuideStep array with the new step
		const updatedGuideStep = [...(currentGuide?.GuideStep || [])];

		if (currentStep <= updatedGuideStep.length && currentStep > 0) {
			updatedGuideStep[currentStep - 1] = currentStepData; // Update existing step
			updatedGuideStep.forEach((step, index) => {
			  // Create a deep copy of the step to ensure it and all nested objects are mutable
			  const mutableStep = JSON.parse(JSON.stringify(step));
			  updatedGuideStep[index] = mutableStep;

			  // Now safely update properties on the mutable copy
			  mutableStep.Overlay = selectedTemplate === "Tour" ?false:  overlayEnabled;

			  // Ensure Tooltip object exists before accessing its properties
			  if (!mutableStep.Tooltip) {
				mutableStep.Tooltip = {};
			  }
			  mutableStep.Tooltip.EnableProgress = progress;
			  mutableStep.Tooltip.ProgressTemplate = selectedOption?.toString() || "";

			  // Ensure Modal object exists before accessing its properties
			  if (!mutableStep.Modal) {
				mutableStep.Modal = {};
			  }
			  mutableStep.Modal.ProgressColor = ProgressColor;
			});
		  }
		else {
			// If the current step does not exist (i.e., new step), add it to the array
			updatedGuideStep.push(currentStepData);
		}

		if (selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot") {
			if (!createWithAI) {
				updatedGuideData = {
					...formatTooltipGuideData(),
					...additionalData,
				};
			} else {
				// For AI-created tooltips, ensure data is synchronized and use interactionData

				updatedGuideData = {
					...interactionData,
				//	...formatTooltipGuideData(),
					...additionalData,
				};
				syncAITooltipContainerData();


				// Debug button structure
				if (updatedGuideData?.GuideStep) {
					updatedGuideData.GuideStep.forEach((step: any, index: number) => {
						if (step.ButtonSection && step.ButtonSection.length > 0) {
							console.log(`Step ${index + 1} button structure:`, {
								buttonSection: step.ButtonSection,
								customButtons: step.ButtonSection[0]?.CustomButtons,
								buttonProperties: step.ButtonSection[0]?.CustomButtons?.[0]?.ButtonProperties
							});
						}
					});
				}
			}
			} else if (selectedTemplate === "Tour") {
			let tooltipGuideData: any;
			tooltipGuideData = formatTooltipGuideData();

			const maxLength = Math.max(updatedGuideStep.length, tooltipGuideData?.GuideStep.length ?? 0);

			const mergedGuideSteps = Array.from({ length: maxLength }, (_, index) => {
				const step = updatedGuideStep[index] || {};
				const tooltipStep = tooltipGuideData?.GuideStep[index] || {};

				if (step.StepType === "Hotspot" || step.StepType === "Tooltip") {
					return {
						...step,
						...tooltipStep,
					};
				}

				return Object.keys(step).length ? step : tooltipStep;
			});

			updatedGuideData = {
				...tooltipGuideData,
				GuideStep: mergedGuideSteps,
				...additionalData,
			};
		} else if (selectedTemplate === "Announcement") {
			const announcementGuideData = formatAnnouncementGuideData(); // Get Tooltip/Hotspot formatted data
			const announcementSteps = announcementGuideData?.GuideStep || []; // Ensure it's an array

			// Use a Map to store unique steps based on a key (e.g., StepNumber or id)
			const stepMap = new Map();

			// Add updatedGuideStep data
			updatedGuideStep.forEach((step) => {
				stepMap.set(step.StepTitle, step);
			});

			// Add announcement data (only if it doesn't exist)
			announcementSteps.forEach((step) => {
				if (!stepMap.has(step.StepTitle)) {
					stepMap.set(step.StepTitle, step);
				}
			});

			// Convert map back to an array
			const mergedGuideSteps = Array.from(stepMap.values());

			if (!createWithAI) {
				updatedGuideData = {
					...announcementGuideData,
					GuideStep: mergedGuideSteps,
					GuideType: selectedTemplate,
					GuideId: currentGuideId || "",
					Name: guideName || "New Guide1",
					OrganizationId: organizationId,
					AccountId: selectedAccountId,
					GuideStatus: "Draft",
					CreatedDate: new Date().toISOString(),
					UpdatedDate: new Date().toISOString(),
					TargetUrl: startingUrl,
					...additionalData,
				};
			} else {
				// For AI-created announcements, ensure data is synchronized and use interactionData
				// First sync metadata from interactionData
				syncAIAnnouncementDataForPreview(true); // Preserve global state during save
				// Then sync container data from updated metadata
				syncAIAnnouncementContainerData();

				updatedGuideData = {
					...interactionData,
					...additionalData,
				};
			}
		} else if (selectedTemplate === "Checklist") {
			const data = formatChecklistGuideData();
			updatedGuideData = {
				...data,
				...additionalData,
			};
		} else if (selectedTemplate === "Banner") {
			updatedGuideData = {
				...initialGuideDataStructure,
				...additionalData,
				GuideStep: updatedGuideStep,
				UpdatedDate: new Date().toISOString(),
			};
		}
		setShowTextField(false);
		setInitialGuideData(updatedGuideData);
		setCurrentGuide(updatedGuideData);
		setSelectedStepTypeHotspot(false);
	};
	const handleUpdateGuideThroughAI = async () => {
		setCurrentGuide("");
		saveGuide(
			updatedGuideData, // Pass the updated guide data
			() => {
				setIsTemplateScreen(true);
				setSavedGuideData(updatedGuideData);
				setCurrentGuideId(updatedGuideData.GuideId);
				setIsAIGuidePersisted(true); // Mark AI guide as persisted after successful save
			},
			(error) => {
				console.error("Error saving guide:", error);
			},
			setLoading
		);

		setIsUnSavedChanges(false);
		setDesignPopup(false);
		setCreateWithAI(false);

		if (currentGuide?.Name && currentGuide?.GuideType && isChangesSaved) {
			openSnackbar(`${currentGuide?.Name} ${currentGuide?.GuideType} Saved Successfully`, "success");
		}
	};

	const handleUpdateGuide = async () => {
		updateGuide(
			updatedGuideData, // Pass the updated guide data
			() => {
				setIsTemplateScreen(true);
				//	setCurrentGuide(null);
				setSavedGuideData(updatedGuideData);
			},
			(error) => {
				console.error("Error saving guide:", error);
				openSnackbar(error, "error");
			},
			setLoading
		);

		setIsUnSavedChanges(false);
		setDesignPopup(false);

		if (currentGuide?.Name && currentGuide?.GuideType && isChangesSaved) {
			openSnackbar(`${currentGuide?.Name} ${currentGuide?.GuideType} Saved Successfully`, "success");
		}
	};

	const handlePreviewGuide = async () => {
		setDesignPopup(false);
		setShowBannerenduser(true);
		if (selectedTemplate === "Tooltip") {
			setShowTooltipenduser(true);
		} else if (selectedTemplate === "Hotspot") {
			setShowHotspotenduser(true);
		} else {
			setShowBannerenduser(true);
		}
		return (
			<Navigate
				to={startingUrl}
				replace
			/>
		);
	};

	const handleNewInteractionClick = () => {
		setIsHomeScreen(false);
	};

	const handleGridItemClick = async (template: string, isEditing: boolean = false, guideDetails: any = null) => {
		let selectedValue: string;
		if (template === "Announcement") {
			selectedValue = "Announcement";
			setIsAnnouncementCreationBuilderOpen(true);
		} else if (template === "Tour") {
			selectedValue = "Tour";
		} else if (template === "Tooltip") {
			selectedValue = "Tooltip";
			if (selectedValue === "Tooltip") {
				setIsTooltipPopup(true);
				setIsTooltipCreationBuilderOpen(true);
				//setIsBannerCreationBuilderOpen(true);
			}
		} else if (
			template === "Banner" ||
			selectedTemplateTour === "Banner" ||
			updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType === "Banner"
		) {
			selectedValue = "Banner";
			if (
				selectedValue === "Banner" ||
				selectedTemplateTour === "Banner" ||
				updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType === "Banner"
			) {
				setBannerPopup(true);
				setIsBannerCreationBuilderOpen(true);
			}
		} else if (template === "Checklist") {
			selectedValue = "Checklist";
		} else if (template === "Hotspot") {
			selectedValue = "Hotspot";
			if (selectedValue === "Hotspot") {
				// setIsHotspotPopupOpen(true);
				setIsTooltipPopup(true);
				setIsHotspotCreationBuilderOpen(true);
			}
		} else if (template === "Survey") {
			selectedValue = "Survey";
		} else {
			selectedValue = template;
		}
		if (isEditing && guideDetails) {
			const res = await GetGudeDetailsByGuideId(guideDetails.GuideId, createWithAI, interactionData); // Api call to get Guide Details
			setCurrentGuide(res.GuideDetails);
			// Set savedGuideData to enable proper banner data restoration on refresh
			setSavedGuideData(res.GuideDetails);
			SetGuideName(guideDetails.Name);
			setStartingUrl(guideDetails.TargetUrl);
			setIsEditable(isEditing);
			// Set isAIGuidePersisted to true for existing guides since they are already persisted
			setIsAIGuidePersisted(true);
		} else {
			setCurrentGuide(null);
			SetGuideName("");
			//setStartingUrl("");
			setIsEditable(false);
		}
		setSelectedTemplate(selectedValue, true);
		setIsGuideInfoScreen(true);
		setErrors((prev) => ({ ...prev, GuideName: "" }));
		setIsGuideNameUnique(true);
		// Set isAIGuidePersisted to true for manual guides so they always use updateGuide()
		setIsAIGuidePersisted(true);
	};
	useEffect(() => {
		getCurrentGuideId();
		if (accounts.length > 0) {
			setSelectedAccountId(accounts[0].AccountId);
			setAccountId(accounts[0].AccountId);
			accountId = accounts[0].AccountId;
		}
		const hash = window.location.hash.substring(1);
		if (IsAnnouncementCreationBuilderOpen || hash == "announcementEdit") {
			setIsDrawerClosed(true);
			setIsAnnouncementCreationBuilderOpen(true);
			setIsPopupOpen(true);
		} else if (IsBannerCreationBuilderOpen || hash == "bannerEdit") {
			setIsDrawerClosed(true);
			setIsAnnouncementCreationBuilderOpen(false);
			setIsBannerCreationBuilderOpen(true);
			setIsAnnouncementPopupOpen(false);
			setBannerPopup(true);
		} else if (IsHotspotCreationBuilderOpen || hash == "hotspotEdit") {
			setIsDrawerClosed(true);
			setIsAnnouncementCreationBuilderOpen(false);
			setIsBannerCreationBuilderOpen(false);
			setIsHotspotCreationBuilderOpen(true);
			setIsAnnouncementPopupOpen(false);
			setBannerPopup(false);
		} else if (IsTooltipCreationBuilderOpen || hash == "tooltipEdit") {
			setIsDrawerClosed(true);
			setIsAnnouncementCreationBuilderOpen(false);
			setIsBannerCreationBuilderOpen(false);
			setIsTooltipCreationBuilderOpen(true);
			setIsAnnouncementPopupOpen(false);
			setBannerPopup(false);
			setIsTooltipPopup(true);
		}
	}, [accounts]);

	const handleBackClick = () => {
		// Close all button popups when navigating back
		triggerCloseAllButtonPopups();
		
		const styleTag = document.getElementById("dynamic-body-style");
		if (styleTag) {
			document.head.removeChild(styleTag);
		}
		// Clear history when navigating back to template selection screen
		useHistoryStore.getState().clearHistory();

		setisShowIcon(false);
		setIsDrawerClosed(false);
		setIsHomeScreen(true);
		setIsGuideInfoScreen(false);
		setIsTemplateScreen(false);
		setIsPopupOpen(false);
		setBannerPopup(false);
		clearUserSession();
		clearGuideDetails();
		setAnchorEl(null);
		handleClose();
		setCurrentGuideId("");
		setElementSelected(false);
		resetTooltipMetaData();
		handleLeave();
	};
	const [isTourPopupOpen, setIsTourPopupOpen] = useState(false);

	const handleCreateGuideClick = async () => {
		setCreateWithAI(false);
		setProgressColor("#5f9ea0");

		// Clear history when creating a new guide
		useHistoryStore.getState().clearHistory();

		// Validate guide name uniqueness
		setisShowIcon(false);
		const guideNameExists = await CheckGuideNameExists(guideName, selectedAccountId, selectedTemplate);
		if (guideNameExists) {
			setTempGuideName(guideName); // revert to original
			const newErrors: Partial<Record<string, string>> = {
				GuideName: `${selectedTemplate} Already Exists`,
			};
			setErrors(newErrors);
			setIsGuideNameUnique(false);
			// setIsEditing(false);
			// openSnackbar("Guide name already exists", "error");

			return;
		  }


		// Apply style changes for specific templates only if guide name is unique
		if (["Banner"].includes(selectedTemplate)) {
			let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
			const bodyElement = document.body;

			// Add a dynamic class to the body
			bodyElement.classList.add("dynamic-body-style");

			if (!styleTag) {
				styleTag = document.createElement("style");
				styleTag.id = "dynamic-body-style";

				// Add styles for body and nested elements
				let styles = `
					.dynamic-body-style {
						padding-top: 55px !important;
						max-height:calc(100% - 55px);
					}

				`;

				styleTag.innerHTML = styles;
				document.head.appendChild(styleTag);
			}
			if (["Banner", "Tour"].includes(selectedTemplate)) {
				setBposition("Cover Top");
				setOverlayEnabled(true);
			}
		}

		// Proceed with guide creation or update
		const guideData = {
			GuideId: isEditable ? currentGuide?.GuideId : "",
			GuideType: selectedTemplate,
			Name: guideName,
			Content: "",
			OrganizationId: organizationId,
			CreatedDate: "",
			CreatedBy: "",
			UpdatedBy: "",
			TargetUrl: startingUrl,
			Frequency: "One-time",
			Segment: "All users",
			AccountId: selectedAccountId,
			GuideStatus: "Draft",
			GuideStep: isEditable ? currentGuide?.GuideStep : [],
		};

		if (isEditable) {
			updateGuide(
				// @ts-expect-error
				guideData,
				async (res) => {
					const resp = await GetGudeDetailsByGuideId(currentGuide?.GuideId, createWithAI, interactionData);
					setCurrentGuide(resp.GuideDetails);
					// Set savedGuideData to enable proper banner data restoration on refresh
					setSavedGuideData(resp.GuideDetails);
					setImageSrc(resp.GuideDetails?.GuideStep[0]?.ImageProperties?.[0]?.CustomImage[0]?.Url || "");

					// Clean up any duplicate steps that might exist in the loaded guide
					cleanupDuplicateSteps();
					setImageName(resp.GuideDetails?.GuideStep[0]?.ImageProperties?.[0]?.CustomImage[0]?.AltText || "");
					setHtmlContent(resp.GuideDetails?.GuideStep[0]?.HtmlSnippet || "");
					setButtonColor(resp.GuideDetails?.GuideStep[0]?.ButtonSection?.[0]?.CustomButtons?.[0] || "");
					setIsTemplateScreen(true);
					handlecreatefromScratchclick();
					setIsTourPopupOpen(true);
				},
				(error) => {
					console.error("Error Updating guide:", error);
				},
				setLoading
			);
		} else {
			saveGuide(
				// @ts-expect-error
				guideData,
				async (res) => {
					if (res && res?.data?.Data) {
						if (selectedTemplate === "Tour") {
							const resp = await GetGudeDetailsByGuideId(res?.data?.Data, createWithAI, interactionData);
							setCurrentGuide(resp.GuideDetails);
							// Set savedGuideData to enable proper banner data restoration on refresh
							setSavedGuideData(resp.GuideDetails);
							setCurrentGuideId(resp.GuideDetails.GuideId);

							// Clean up any duplicate steps that might exist in the loaded guide
							cleanupDuplicateSteps();

							//setIsTemplateScreen(true);
							handlecreatefromScratchclick();
							setIsTourPopupOpen(true);
							//HotspotGuideDetails();

							TooltipGuideDetails();
						} else {
							const resp = await GetGudeDetailsByGuideId(res?.data?.Data, createWithAI, interactionData);
							setCurrentGuide(resp.GuideDetails);
							// Set savedGuideData to enable proper banner data restoration on refresh
							setSavedGuideData(resp.GuideDetails);
							setCurrentGuideId(resp.GuideDetails.GuideId);

							// Clean up any duplicate steps that might exist in the loaded guide
							cleanupDuplicateSteps();

							setIsTemplateScreen(true);
							handlecreatefromScratchclick();
						}
					}
				},
				(error) => {
					console.error("Error Creating guide:", error);
				},
				setLoading
			);
		}
	};
	const handlecreatefromScratchclick = async (doCallGetAllGuide = true) => {
		const filters = [
			{
				FieldName: "GuideType",
				ElementType: "string",
				Condition: "equals",
				Value: selectedTemplate,
				IsCustomField: false,
			},
			{
				FieldName: "GuideStatus",
				ElementType: "string",
				Condition: "equals",
				Value: "Draft",
				IsCustomField: false,
			},
			{
				FieldName: "AccountId",
				ElementType: "string",
				Condition: "contains",
				Value: accountId,
				IsCustomField: false,
			},
		];
		try {
			if (doCallGetAllGuide) {
				const data = await getAllGuides(0, 15, filters, "");
				const currentguideDetails = data?.results?.[0] || null;

				setCurrentGuide(currentguideDetails);

				setIsDrawerClosed(true);
				//setIsPopupOpen(true);
			} else {
				setIsDrawerClosed(true);
				setIsPopupOpen(true);
			}
		} catch (error) {}
	};

	const handleEditClick = () => {
		setIsEditable(!isEditable);
	};
	const open = Boolean(anchorEl);
	const popoverId = open ? "three-dot-menu-popover" : undefined;
	const loginuser = useInfoStore((state) => state.accessToken);

	useEffect(() => {
		if (loginuser && loginuser != "") {
			setIsLoggedIn(true);
		} else {
			setIsLoggedIn(false);
			localStorage.removeItem("CurrentGuideId");
		}
	}, [loginuser]);

	useEffect(() => {
		if (isLoggedIn && organizationId) {
			const fetchAccounts = async () => {
				setLoading(true);
				try {
					const response = await GetAccountsList(setAccounts, setLoading, organizationId, -1, -1, "", "", "");
				} catch (err) {
					//console.error(err);
					throw err;
				} finally {
					setLoading(false);
				}
			};
			fetchAccounts();
		}
	}, [isLoggedIn, organizationId]);

	const [editType, setEditType] = useState("");
	const [editDescription, setDescription] = useState("");
	const [editstepNameClicked, setEditStepNameClicked] = useState(false);

	const handleRenamStepName = (id: string, title: string, type: string, description: string) => {
		setStepCreation(false);

		// First navigate to the particular step in GuidePopUp.tsx
		const stepIndex = steps.findIndex((step: any) => step.id === id);
		if (stepIndex !== -1) {
			// Check if we need to navigate to a different step
			const stepCount = steps[stepIndex]?.stepCount;
			const needsNavigation = stepCount ;
			
			if (needsNavigation) {
				// Check if we're already on the step we want to edit
				const currentStepId = steps.find((step: any) => step.stepCount === currentStep)?.id;
				
				if (currentStepId === id) {
					// We're already on the target step, no need to navigate
					// Close all button popups before opening the edit dialog
					triggerCloseAllButtonPopups();
					setImageAnchorEl({
						buttonId: "",
						containerId: "",
						// @ts-ignore
						value: null,
					});
					setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
					setBannerButtonSelected(false);
					
					// Just set up the edit form directly
					setStepData({
						stepNumber: title,
						type: type,
						description: "",
					});
					setCurrentStepNameId(id);
					setIsEditStepName(true);
					setStepName(title);
					setEditType(type || "Hotspot");
					setShowTextField(false);
					setDescription(description);
					setOpenStepDropdown(true);
				} else {
					// We need to navigate to the step first, but handleStepChange closes the dropdown
					// So we'll call it with a modified approach

					// Save the current dropdown state
					const wasDropdownOpen = openStepDropdown;

					// Temporarily close the dropdown to prevent handleStepChange from affecting it
					if (wasDropdownOpen) {
						setOpenStepDropdown(false);
					}

					// Navigate to the step			 
						handleStepChange(id);
					
					// Now set up the edit form data after navigation
					setTimeout(() => {
						setStepData({
							stepNumber: title,
							type: type,
							description: "",
						});
						setCurrentStepNameId(id);
						setIsEditStepName(true);
						setStepName(title);
						setEditType(type || "Hotspot");
						setShowTextField(false);
						setDescription(description);

						// Reopen the dropdown for editing
						setOpenStepDropdown(true);
					}, 50);
				}
			}
		} else {
			// If we can't find the step, just set up the edit form without navigation
			setStepData({
				stepNumber: title,
				type: type,
				description: "",
			});
			setCurrentStepNameId(id);
			setIsEditStepName(true);
			setStepName(title);
			setEditType(type || "Hotspot");
			setShowTextField(false);
			setDescription(description);
		}
	};

	const handleAccountChange = (event: any) => {
		const AccountId = event.target.value;
		setSelectedAccountId(AccountId);
		setAccountId(AccountId);
		accountId = AccountId;
	};
	// handllng unsaved changes
	useEffect(() => {
		const handleBeforeUnload = (event: BeforeUnloadEvent) => {
			if (isUnSavedChanges) {
				event.preventDefault();
				event.returnValue = ""; // Chrome requires this line for the dialog to display
			}
		};
		clearUserSession();
		window.addEventListener("beforeunload", handleBeforeUnload);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
		};
	}, [isUnSavedChanges, clearUserSession, currentStep]);

	const handleNextClick = () => {};
	const [errors, setErrors] = useState<Partial<Record<string, string>>>({});
	const [isValid, setIsValid] = useState(false);
	const [isFormValid, setIsFormValid] = useState(false);

	// Validation for Guide Name and Target URL
	const handleEventChange = (event: ChangeEvent<HTMLInputElement>) => {
		const { name, value } = event.target;
		let error = "";

		if (name === "TargetUrl") {
			const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/.*)?$/;

			if (value.length > 200) {
				error = "Target URL must be less than 200 characters.";
			} else if (/[^a-zA-Z0-9-._~:/?#[\]@!$&'()*+,;=]/.test(value)) {
				error = "Target URL contains invalid characters.";
			} else if (!urlPattern.test(value)) {
				error = "Please enter a valid URL. E.g., https://example.com";
			}
			setStartingUrl(value);
		}
		setErrors((prev) => ({ ...prev, [name]: error }));
	};

	const [isGuideNameUnique, setIsGuideNameUnique] = useState(true); // Track if guide name is unique
	const handleGuideNameChange = (e: ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		SetGuideName(value);


		const trimmedValue = value.trim();
		if (trimmedValue.length < 3) {
			setErrors((prev) => ({ ...prev, GuideName: "Name Must be atleast 3 Characters Long" }));
			setIsGuideNameUnique(false);

		} else if (trimmedValue.length > 50) {
			setErrors((prev) => ({ ...prev, GuideName: "Name Cannot exceed 50 Characters" }));
			setIsGuideNameUnique(false);
		} else {
			// Clear length error if any
			setErrors((prev) => ({ ...prev, GuideName: "" }));
			setIsGuideNameUnique(true);
		}
	};
	useEffect(() => {
		if (updatedGuideData?.GuideStep && updatedGuideData?.GuideType != "Checklist") {
			// Create a new filtered array instead of modifying the original
			const filteredSteps = updatedGuideData?.GuideStep.filter((step: any) => step?.StepTitle !== undefined);
			// Create a new object with the filtered steps
			updatedGuideData = { ...updatedGuideData, GuideStep: filteredSteps };
		}

	}, [
		updatedGuideData?.GuideStep,
		isAnnouncementPopupOpen,
		showBannerenduser,
		showTooltipenduser,
		showHotspotenduser,
		isTourTemplate,
	]);
const handleStepChange = (id: any) => {
		// Close all button popups when changing steps
		triggerCloseAllButtonPopups();
		
		if (createWithAI && interactionData) {
			// Prevent setting unsaved changes if returning from preview
			if (!useDrawerStore.getState().isReturningFromPreview) {
				setIsUnSavedChanges(true);
			}
			generateSteps(interactionData?.GuideStep || []);
			updatedGuideData = interactionData;

			// Synchronize AI data when changing steps
			if (selectedTemplate === "Tooltip") {
				syncAITooltipContainerData();
				// Restore element click state when changing steps
				restoreTooltipElementClickState();
			} else if (selectedTemplate === "Announcement") {
				// Pure announcement uses announcementGuideMetaData
				// First sync metadata from interactionData to preserve global progress state
				syncAIAnnouncementDataForPreview(true); // Preserve global state during step navigation
				// Then sync container data from updated metadata
				syncAIAnnouncementContainerData();
			} else if (selectedTemplate === "Tour") {
				// For AI tours, sync current step data before navigation			 
				syncCurrentStepDataForAITour();
				} else if (selectedTemplateTour === "Announcement") {
				// Tour+Announcement uses toolTipGuideMetaData			  
				syncAITooltipContainerData();				 
			} else if (selectedTemplateTour === "Tooltip") {
				// Tour+Tooltip uses toolTipGuideMetaData				 
				syncAITooltipContainerData();
				// Restore element click state when changing steps in tours
				restoreTooltipElementClickState();
			}
			} else if (!createWithAI && (selectedTemplate === "Tooltip" || selectedTemplate === "Announcement" ||
			(selectedTemplate === "Tour" && (selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Announcement" || selectedTemplateTour === "Banner")))) {
			// For non-AI guides, force save button configuration before changing steps
			console.log("🔄 handleStepChange: Force saving button configuration before step navigation");
			forceSaveButtonConfiguration();
		}


		setImageAnchorEl({
			buttonId: "",
			containerId: "",
			// @ts-ignore
			value: null,
		});
		const selectedStepTitle = steps.find((item: any) => item.id === id)?.name;
		const stepIndex = steps.findIndex((item: any) => item.id === id);
		selectedStepType = steps.find((item: any) => item.id === id)?.stepType || "";
		setSelectedTemplateTour(selectedStepType, true);
		if (selectedStepType === "Banner") {
			setBannerPopup(true);
			// Don't reset the position when navigating between steps
		} else {
			setBannerPopup(false);
		}
		if (selectedStepType === "Tooltip") {
			setIsPopupOpen(false); 
		} else if (selectedStepType === "Announcement") {
			setIsPopupOpen(true);
	}
		changeCurrentStep(id, selectedStepType);  
		if (updatedGuideData?.GuideStep) {
			// Create a new filtered array instead of modifying the original
			const filteredSteps = updatedGuideData?.GuideStep.filter((step: any) => step.StepTitle !== undefined);
			// Create a new object with the filtered steps
			updatedGuideData = { ...updatedGuideData, GuideStep: filteredSteps };
		}
		if (currentGuide?.GuideStep) {
			// Create a new filtered array instead of modifying the original
			const filteredSteps = currentGuide?.GuideStep.filter((step: any) => step.StepTitle !== undefined);
			// Create a new object with the filtered steps and update the state
			setCurrentGuide({ ...currentGuide, GuideStep: filteredSteps });
		}
		if (selectedStepType !== "Hotspot") {
			const existingHotspot = document.getElementById("hotspotBlinkCreation");
			if (existingHotspot) {
				existingHotspot.remove();
			}
		}
		if (selectedStepType === "Hotspot") {
			setElementSelected(true);
			fetchGuideDetails();
		}
		if (selectedTemplate !== "Tour") {
			handleSaveGuide();
		}
		const matchedGuideStep = updatedGuideData?.GuideStep?.[stepIndex]?.StepTitle
			? updatedGuideData?.GuideStep?.find((step: any) => step.StepTitle === selectedStepTitle)
			: currentGuide?.GuideStep?.find((step: any) => step.StepTitle === selectedStepTitle);  
		if (selectedTemplate === "Tour") {
			handleSaveGuide();
		}
		isChangesSaved = false;
		setIsSaveClicked(false);
		const styleExTag = document.getElementById("dynamic-body-style");
		if (styleExTag) {
			document.head.removeChild(styleExTag);
		}
		if (
			(selectedTemplate === "Tour" &&
				(selectedStepType === "Banner" || selectedStepType === "Tooltip" || selectedStepType === "Hotspot")) ||
			selectedTemplate === "Banner" ||
			selectedTemplate === "Tooltip" ||
			selectedTemplate === "Hotspot"
		) {
			let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
			const bodyElement = document.body;

			// Add a dynamic class to the body
			bodyElement.classList.add("dynamic-body-style");

			if (!styleTag) {
				styleTag = document.createElement("style");
				styleTag.id = "dynamic-body-style";

				// Add styles for body and nested elements
				let styles = `
					.dynamic-body-style {
						padding-top: 55px !important;
						max-height:calc(100% - 55px);
					}

				`;

				styleTag.innerHTML = styles;
				document.head.appendChild(styleTag);
			}
		}
		if (selectedTemplate === "Tour" && selectedStepType === "Banner") {
			setBannerButtonSelected(false);

			if (updatedGuideData?.GuideStep?.[stepIndex].ButtonSection.length > 0) {
				setBannerButtonSelected(true);
			}
		}
		if (matchedGuideStep) {
			resetHeightofBanner(
				matchedGuideStep?.Canvas?.Position,
				parseInt(matchedGuideStep?.Canvas?.Padding || 10),
				parseInt(matchedGuideStep?.Canvas?.BorderSize || 2),
				true
			);

			if (updatedGuideData?.GuideStep?.[stepIndex]?.ElementPath !== "") {
				setElementSelected(true);
			} else {
				const styleExTag = document.getElementById("dynamic-body-style");
				if (styleExTag) {
					document.head.removeChild(styleExTag);
				}
				if (updatedGuideData?.GuideStep?.[currentStep-1]?.ElementPath !== "") {

				 }
				else {
					setElementSelected(false);
				}
			}

			if (selectedTemplate === "Tour") {
				//handleSaveGuide();
				getGuideTypeValueNew(selectedStepType, [matchedGuideStep]);
			} else {
				getGuideTypeValueNew(updatedGuideData?.GuideType, [matchedGuideStep]);
			}
		} else {
			setGuideStep([]);
			if (selectedTemplate === "Tour" && selectedStepType === "Banner") {
				// For new steps, set default position to "Cover Top"
				setBposition("Cover Top");
				resetHeightofBanner("Cover Top"); // For new created step, use Cover Top as default
			} else if (selectedTemplate === "Tour" && (selectedStepType === "Tooltip" || selectedStepType === "Hotspot")) {
				const styleExTag = document.getElementById("dynamic-body-style"); // for element selection need to show full screen
				if (styleExTag) {
					document.head.removeChild(styleExTag);
				}
			}
		}
		setOpenStepDropdown(false);

		// Check if current step has button click configuration
		const currentStepCount = steps.find((item: any) => item.id === id)?.stepCount;
		if (currentStepCount && toolTipGuideMetaData?.[currentStepCount - 1]?.design?.gotoNext?.ButtonId) {
			setElementClick("button");
		} else {
			setElementClick("element");
		}
	};

	const resetHeightofBanner = (
		position: any,
		padding: any = 0,
		border: any = 0,
		isFromApply: any = false,
		oldPadding: any = 0,
		top: any = 55,
	) => {
		// Skip applying any styles for Announcement type
		if (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") {
			// Clean up any existing styles before returning
			const styleExTag = document.getElementById("dynamic-body-style");
			if (styleExTag) {
				document.head.removeChild(styleExTag);
			}
			// Remove the dynamic-body-style class from the body
			document.body.classList.remove("dynamic-body-style");
			return;
		}

		const styleExTag = document.getElementById("dynamic-body-style");
		if (styleExTag) {
			document.head.removeChild(styleExTag);
		}
		let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
		const bodyElement = document.body;

		bodyElement.classList.add("dynamic-body-style");

		if (!styleTag) {
			styleTag = document.createElement("style");
			styleTag.id = "dynamic-body-style";

			let styles = `
					.dynamic-body-style {
						padding-top: ${top}px !important;
						max-height:calc(100% - 55px);
					}

					`;
			// Add styles for body and nested elements
			if (position == "Push Down") {
				const banner = document.getElementById("guide-popup");
				const bannerHeight = !isFromApply ? banner?.offsetHeight || 49 : 31;
				// Create default values for when GuideStep is empty
				// Always get the latest progress state from the store to ensure we have the current value
				const drawerStore = useDrawerStore.getState();
				const enableProgress = isFromApply
					? drawerStore.progress // When applying changes, use the store's current value
					: (updatedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || drawerStore.progress || false);
				let progressTop = 0;
				if (selectedTemplate === "Tour") {
					// Get selectedOption with fallback to the store value if GuideStep is empty
					let selectedOption = updatedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || drawerStore.selectedOption || "dots"
					// Set progressTop based on the selectedOption
					if (selectedOption === "dots") {
						progressTop = 10;
					} else if (selectedOption === "breadcrumbs") {
						progressTop = 14;
					} else if (selectedOption === "BreadCrumbs" || selectedOption === "linear") {
						progressTop = 7;
					} else if (typeof selectedOption === 'number' || !isNaN(Number(selectedOption))) {
						// Handle numeric values (from the store) or string numbers
						const numOption = typeof selectedOption === 'number' ? selectedOption : Number(selectedOption);
						if (numOption === 1 || selectedOption === "1") { // dots
							progressTop = 10;
						} else if (numOption === 2 || numOption === 3 || selectedOption === "2" || selectedOption === "3") { // linear
							progressTop = 7;
						} else if (numOption === 4 || selectedOption === "4") { // breadcrumbs
							progressTop = 14;
						}
					}
				}
				// Calculate the height with the current progress state
				const height =
				top + bannerHeight + (isFromApply ? padding * 2 + border * 2 + (bannerButtonSelected ? 9 : 0) + (enableProgress ? progressTop : 0) : 0); //55 for Above Drawer then 55 for Banner then 10 for padding and 2 for border
				styles = `
					.dynamic-body-style {
						padding-top: ${height}px !important;
						max-height:calc(100% - 55px);
					}
					.dynamic-body-style header {
						top: ${height}px !important;
					}

							.dynamic-body-style .page-sidebar {
						padding-top: ${height}px !important;
					}

					`;
			} else if (position === "Cover Top") {
				// For Cover Top position, apply overflow hidden
				styles = `
					.dynamic-body-style {
						padding-top: ${top}px !important;
						max-height:calc(100% - 55px);
						overflow: hidden !important;
					}
					`;
				setOverlayEnabled(overlayEnabled);
				setPageInteraction(pageinteraction);
			} else {
				setOverlayEnabled(overlayEnabled);
				setPageInteraction(pageinteraction);
			}

			styleTag.innerHTML = styles;
			document.head.appendChild(styleTag);
		}
	};

	const [deleteClicked, setDeleteClicked] = useState(false);
	const handleDelete = (id: string) => {
		idToDelete = id;
		const deletedStepName = steps.find((step: any) => step.id === id)?.name;
		nameToDelete = deletedStepName || "";
		setDeleteClicked(true);
		setOpenDialog(true);
	};
	const handleDeleteStep = (id: string) => {
		// First, find the step information before deleting it
		const stepToDelete = steps.find((step: any) => step.id === id);
		if (!stepToDelete) {
			setOpenDialog(false);
			return;
		}

		const deletedStepName = stepToDelete.name;
		const deletedStepIndex = steps.findIndex((step: any) => step.id === id);
		const stepTypeToDelete = stepToDelete.stepType;
		const stepCount = stepToDelete.stepCount;

		// Ensure returning from preview flag is reset before deleting the step
		setIsReturningFromPreview(false);

		// Call the store's deleteStep function to remove the step
		deleteStep(id);

		setOpenDialog(false);

		// Use setTimeout to ensure the deletion is processed before setting the flag
		setTimeout(() => {
			// Force update the unsaved changes flag directly in the store
			// This bypasses any conditions in the store that might prevent the update
			useDrawerStore.setState((state) => ({
				...state,
				isUnSavedChanges: true
			}));

			// Show success message
			openSnackbar(`${deletedStepName} deleted successfully`, "success");
		}, 50);


		// Clean up steps in updatedGuideData
		if (updatedGuideData?.GuideStep) {
			// First, filter out the deleted step by multiple criteria
			const filteredUpdatedGuideSteps = updatedGuideData.GuideStep.filter(

				(guideStep: any) =>
					guideStep.StepTitle !== deletedStepName &&
					guideStep.stepName !== deletedStepName &&
					guideStep.StepCount !== stepCount &&
					guideStep.StepId !== id
			);

			// Update the filtered steps
			updatedGuideData = { ...updatedGuideData, GuideStep: filteredUpdatedGuideSteps };
		}

		// Clean up steps in currentGuide
		if (currentGuide?.GuideStep) {
			// First, filter out the deleted step by multiple criteria
			const filteredCurrentGuideSteps = currentGuide.GuideStep.filter(

				(guideStep: any) =>
					guideStep.StepTitle !== deletedStepName &&
					guideStep.stepName !== deletedStepName &&
					guideStep.StepCount !== stepCount &&
					guideStep.StepId !== id
			);

			// Update the current guide with filtered steps
			setCurrentGuide({ ...currentGuide, GuideStep: filteredCurrentGuideSteps });
		}


		// Update announcementGuideMetaData in the store
		useDrawerStore.setState((state: any) => {
			if (!state.announcementGuideMetaData?.GuideStep) {
				return { announcementGuideMetaData: state.announcementGuideMetaData };
			}

			// Filter out the deleted step
			const filteredAnnouncementGuideSteps = state.announcementGuideMetaData.GuideStep.filter(
				(guideStep: any) =>
					guideStep.StepTitle !== deletedStepName &&
					guideStep.stepName !== deletedStepName &&
					guideStep.StepCount !== stepCount &&
					guideStep.StepId !== id
			);

			return {
				announcementGuideMetaData: {
					...state.announcementGuideMetaData,
					GuideStep: filteredAnnouncementGuideSteps
		}
			};
		});

		// Synchronize the preview data with the actual steps
		// setTimeout(() => {
		// 	synchronizePreviewData();
		// }, 100);

		// Handle template-specific logic
		if (selectedTemplate === "Tour") {
			const updatedSteps = updatedGuideData?.GuideStep || [];
			let newCurrentStep;

			if (currentStep - 1 === deletedStepIndex) {
				// If deleting the current step, move to the next step or fallback to the last step
				newCurrentStep = updatedSteps[deletedStepIndex] || updatedSteps[updatedSteps.length - 1];
			} else {
				// Otherwise, stay on the same step
				newCurrentStep = updatedSteps[currentStep - 1] || updatedSteps[updatedSteps.length - 1];
			}

			// Handle step type conditions
			const stepType = newCurrentStep?.StepType || "";
			setSelectedTemplateTour(stepType, true);

			// Clean up hotspot elements if needed
			if (stepTypeToDelete === "Hotspot") {
				const existingHotspot = document.getElementById("hotspotBlinkCreation");
				if (existingHotspot) {
					existingHotspot.remove();
				}
			}

			// Update guide type value
			getGuideTypeValue(stepType, updatedSteps);

			// Update UI based on step type
			if (stepType === "Banner") {
				setBannerPopup(true);
			} else {
				setBannerPopup(false);
			}

			if (stepType === "Announcement") {
				setIsPopupOpen(true);
			}
		} else {
			// Handle cases other than "Tour"
			getGuideTypeValue(updatedGuideData?.GuideType, updatedGuideData?.GuideStep);
		}

		// Clean up any duplicate steps that might remain
		cleanupDuplicateSteps();

		// Close the dialog
		setOpenDialog(false);
	};

	const [openPopup, setOpenPopup] = useState(false);

	const handleLogoutClick = () => {
		setOpenPopup(true);
		setAnchorEl(null);
	};
	// Track overall form validity
	useEffect(() => {
		const isTargetUrlValid = startingUrl.length <= 200 && !errors.TargetUrl;
		const trimmedGuideName = guideName?.trim() || "";
		const isGuideNameValid = trimmedGuideName.length >= 3 && trimmedGuideName.length <= 50 && !errors?.GuideName;

		setIsValid(isTargetUrlValid);
		setIsFormValid(isGuideNameValid && isTargetUrlValid);
	}, [startingUrl, guideName, errors]);

	const getGuideTypeValue = (guideType: string, guideSteps: any[]) => {
		if (!guideSteps?.length) return;

		const currentGuideStep = guideSteps[currentStep - 1] || guideSteps[currentStep - 2];
		const step = currentGuideStep?.StepCount;

		if ((guideType === "Tooltip" || guideType === "Hotspot") && currentGuideStep?.ElementPath) {
			setOpenTooltip(true);
			setElementSelected(true);
			setTooltipDataOnEdit(guideSteps);
			if (selectedTemplate === "Tour") {
				setTourDataOnEdit(guideSteps, true);
			}
			generateSteps(guideSteps);
			if (step && step != undefined) setCurrentStep(step);
		} else if (deleteClicked && (guideType === "Tooltip" || guideType === "Hotspot")) {
			if (selectedTemplate === "Tour") {
				setTourDataOnEdit(guideSteps, true);
			}
			setTooltipDataOnEdit(guideSteps);
			setOpenTooltip(false);
			setElementSelected(false);
			if (step && step != undefined) setCurrentStep(step);
		}

		if (guideType === "Checklist") {
			setChecklistDataOnEdit(guideSteps);
		}
		if (guideType === "Announcement" || guideType === "Banner") {
			if (!guideSteps || guideSteps.length === 0) {
				// Reset states when guideSteps is empty
				updateImageContainerOnReload([], "reset");
				updateButtonContainerOnReload([], "reset");
				setWidth(500);
				setBorderColor("transparent");
				guideType == "Banner" ? setBackgroundColor("#f1f1f7") : setBackgroundColor("#fff");
				setBorderRadius(4);
				setBorderSizeStore(0);
				setPadding("4");
				// For new banners (when guideSteps is empty), set default position
				if (guideType === "Banner") {
					// Only set default position for new banners in tours
					if (selectedTemplate === "Tour" && !currentGuide?.GuideStep?.length) {
						setBposition("Cover Top");
					} else {
						setBposition("Cover Top"); // Default for standalone banners
					}
				} else {
					setBposition("absolute");
				}
				setbPadding("10");
				setBBorderSize("2");
				setBBorderColor("transparent");
				setBackgroundC("#fff"); //guideType === "Banner" ? setBackgroundC("#f1f1f7") :setBackgroundC("#fff");
				setTextvaluess("");
				setDismissData({
					Actions: "",
					DisplayType: "Cross Icon",
					Color: "#000000",
					DontShowAgain: true,
					dismisssel: false, // Set to false by default - will only show after user enables and applies
				});
			} else {
				if (EditGuide?.GuideType === "Announcement") {
					setAnnouncementDataOnEdit(guideSteps);
				}
				generateSteps(guideSteps);
				const guide: any = guideSteps[currentStep - 1] || guideSteps[currentStep - 2]; // Ensure we use the right step
				//const step = steps.find((item) => item.name === guide?.StepTitle)?.stepCount;
				const step = guide?.StepCount;
				// const stepNew = guide?.StepTitle;
				// const step2 = steps.find((i: any) => i.name === stepNew)?.stepCount;
				if (step) setCurrentStep(step);
				// Process all guide steps
				setGuideStep(guide);

				for (const guideProperty in guide) {
					switch (guideProperty) {
						case "ImageProperties": {
							const uploadedImages = guide[guideProperty]?.reduce((acc: any[], property: any) => {
								return acc.concat(property?.CustomImage || []);
							}, []);

							const mode = uploadedImages.length === 0 ? "reset" : "submit";
							updateImageContainerOnReload(uploadedImages, mode);
							break;
						}

						case "Canvas": {
							const canvas = guide[guideProperty];
							const padding = canvas?.Padding !== undefined && canvas?.Padding !== null ? `${canvas.Padding}` : "10";

							setCanvasSetting(canvas);
							setBannerCanvasSetting(canvas);
							setWidth(canvas?.Width || 500);
							setBorderColor(canvas?.BorderColor || "transparent");
							guideType === "Banner"
								? setBackgroundColor(canvas?.BackgroundColor || "#f1f1f7")
								: setBackgroundColor(canvas?.BackgroundColor || "#fff");
							setBorderRadius(canvas?.Radius || 4);
							setBorderSizeStore(canvas?.BorderSize || 0);
							setPadding(canvas?.Padding || "4");
							// Use the saved position for existing steps
							if (guideType === "Banner") {
								setBposition(canvas?.Position || "Cover Top");
							} else {
								setBposition(canvas?.Position || "absolute");
							}
							setbPadding(padding);
							setBBorderSize(canvas?.BorderSize || "0");
							setBBorderColor(canvas?.BorderColor || "transparent");
							guideType === "Banner" ? setBackgroundC(canvas?.BackgroundColor || "#f1f1f7") : setBackgroundC(canvas?.BackgroundColor || "#fff");

							break;
						}

						case "ButtonSection": {
							handleButtonSection(guide[guideProperty]);
							break;
						}

						case "TextFieldProperties": {
							const textFields = guide[guideProperty]; // This is an array of text field objects
							const mode = textFields.length === 0 ? "reset" : "submit"; // Determine whether to reset or submit
							updateRTEContainerOnReload(textFields, mode); // Update the RTE container with the processed text data

							break;
						}

						case "Modal": {
							const modal = guide[guideProperty];
							const dismiss = modal?.DismissOption || false;
							setDismissData({
								Actions: "",
								DisplayType: "",
								Color: "",
								DontShowAgain: true,
								dismisssel: dismiss,
							});
							break;
						}

						default:
							break;
					}
				}
			}

			// For announcements, apply global overlay synchronization after processing step data
			if (guideType === "Announcement") {
				// Use setTimeout to ensure all state updates are complete before syncing
				setTimeout(() => {
					syncGlobalOverlayStateForAnnouncements();
					console.log("🔄 getGuideTypeValue: Applied global overlay sync for announcement after data loading");
				}, 0);
			}

			return;
		}
	};

	const getGuideTypeValueNew = (guideType: string | undefined, guideSteps: any[]) => {
		if (
			selectedTemplate === "Tour" &&
			guideSteps?.[0]?.ElementPath !== "" &&
			(guideType === "Tooltip" || guideType === "Hotspot") &&
			guideSteps &&
			guideSteps.length
		) {
			setOpenTooltip(true);
			setElementSelected(true);

			return;
		}
		if (guideType === "Announcement" || guideType === "Banner") {
			if (!guideSteps || guideSteps.length === 0) {
				// Reset states when guideSteps is empty
				updateImageContainerOnReload([], "reset");
				updateButtonContainerOnReload([], "reset");
				setWidth(500);
				setBorderColor("transparent");
				guideType === "Banner" ? setBackgroundColor("#f1f1f7") : setBackgroundColor("#fff");
				setBorderRadius(4);
				setBorderSizeStore(0);
				setPadding("4");
				guideType === "Banner" ? setBposition("Cover Top") : setBposition("absolute");
				setbPadding("10");
				setBBorderSize("2");
				setBBorderColor("transparent");

				guideType === "Banner" ? setBackgroundC("#f1f1f7") : setBackgroundC("#fff");
				setTextvaluess("");
				setDismissData({
					Actions: "",
					DisplayType: "Cross Icon",
					Color: "#000000",
					DontShowAgain: true,
					dismisssel: false, // Set to false by default - will only show after user enables and applies
				});
			} else {
				// Process all guide steps
				guideSteps.forEach((guide) => {
					setGuideStep(guide);
					for (const guideProperty in guide) {
						switch (guideProperty) {
							case "ImageProperties": {
								const uploadedImages = guide[guideProperty]?.reduce((acc: any[], property: any) => {
									return acc.concat(property?.CustomImage || []);
								}, []);

								const mode = uploadedImages.length === 0 ? "reset" : "submit";
								updateImageContainerOnReload(uploadedImages, mode);
								break;
							}

							case "Canvas": {
								const canvas = guide[guideProperty];
								const padding = canvas?.Padding !== undefined && canvas?.Padding !== null ? `${canvas.Padding}` : "10";
								setCanvasSetting(canvas);
								setBannerCanvasSetting(canvas);

								setWidth(canvas?.Width || 500);
								setBorderColor(canvas?.BorderColor || "transparent");
								guideType === "Banner"
									? setBackgroundColor(canvas?.BackgroundColor || "#f1f1f7")
									: setBackgroundColor(canvas?.BackgroundColor || "#fff");
								setBorderRadius(canvas?.Radius || 4);
								setBorderSizeStore(canvas?.BorderSize || 0);
								setPadding(canvas?.Padding || "4");
								// Use the saved position for existing steps
								if (guideType === "Banner") {
									setBposition(canvas?.Position || "Cover Top");
								} else {
									setBposition(canvas?.Position || "absolute");
								}
								setbPadding(padding);
								setBBorderSize(canvas?.BorderSize || "0");
								setBBorderColor(canvas?.BorderColor || "transparent");

								guideType === "Banner" ? setBackgroundC(canvas?.BackgroundColor || "#f1f1f7") : setBackgroundC(canvas?.BackgroundColor || "#fff");
								setAnnBorderSize(canvas?.BorderSize || 2);
								setAnnPadding(canvas?.Padding || "12");
								break;
							}

							case "ButtonSection": {
								handleButtonSection(guide[guideProperty]);
								break;
							}

							case "TextFieldProperties": {
								const textFields = guide[guideProperty]; // This is an array of text field objects

								const mode = textFields.length === 0 ? "reset" : "submit"; // Determine whether to reset or submit
								updateRTEContainerOnReload(textFields, mode); // Update the RTE container with the processed text data
								break;
							}

							case "Modal": {
								const modal = guide[guideProperty];
								const dismiss = modal?.DismissOption || false;
								const progressColor = modal?.ProgressColor || "var(--primarycolor)";
								setDismissData({
									Actions: "",
									DisplayType: "",
									Color: "",
									DontShowAgain: true,
									dismisssel: dismiss,
								});
								setProgressColor(progressColor);
								break;
							}
								case "Overlay": {
								const overlayValue = guide[guideProperty];
								// For announcements, preserve global overlay state; for others, load from step data
								if (selectedTemplate !== "Announcement") {
									setOverlayEnabled(overlayValue !== undefined ? overlayValue : true);
								}
								break;
							}

								case "Design": {
								const design = guide[guideProperty];
								const backdropShadow = design?.BackdropShadow;

								// For announcements, preserve global overlay state; for others, load from step data
								if (selectedTemplate !== "Announcement" && backdropShadow !== undefined) {
									setOverlayEnabled(backdropShadow);
								}
								break;
							}

							case "Tooltip": {
								const tooltip = guide[guideProperty];
								const enableProgress = tooltip?.EnableProgress;
								const progressTemplate = tooltip?.ProgressTemplate;
								const interactWithPage = tooltip?.InteractWithPage;

								// Set progress settings from API response
								if (enableProgress !== undefined) {
									setProgress(enableProgress);
								}

								if (progressTemplate !== undefined) {
									setSelectedOption(parseInt(progressTemplate) || 1);
								}

								// For announcements, preserve global page interaction state; for others, load from step data
								if (selectedTemplate !== "Announcement" && interactWithPage !== undefined) {
									setPageInteraction(interactWithPage);
								}
								break;
							}

							default:
								break;
						}
					}
				});
			}

			// For announcements, apply global overlay synchronization after processing all steps
			if (guideType === "Announcement") {
				// Use setTimeout to ensure all state updates are complete before syncing
				setTimeout(() => {
					syncGlobalOverlayStateForAnnouncements();
					console.log("🔄 getGuideTypeValueNew: Applied global overlay sync for announcement after data loading");
				}, 0);
			}
		}
	};

	function getAlignment(alignment: string) {
		switch (alignment) {
			case "start":
				return "flex-start";
			case "end":
				return "flex-end";
			case "center":
			default:
				return "center";
		}
	}
	const [popupVisible, setPopupVisible] = useState(false);
	const [triggerType, setTriggerType] = useState("");
	useEffect(() => {
		if (currentGuide?.GuideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {
			setPopupVisible(true);
			setTriggerType("default");
		}
	}, [currentGuide?.GuideStep?.[currentStep - 1], currentStep]);
	const handleHotspotHover = () => {
		if (currentGuide?.GuideStep?.[currentStep - 1]?.Hotspot?.ShowUpon === "Hovering Hotspot") {
			setPopupVisible(true);
		}
	};

	const handleHotspotClick = () => {
		if (currentGuide?.GuideStep?.[currentStep - 1]?.Hotspot?.ShowUpon === "Clicking Hotspot") {
			setPopupVisible(true);
			setTriggerType("click");
		}
	};
	const handleButtonSection = (buttonSection: any[]) => {
		if (buttonSection.length === 0) {
			setBannerButtonSelected(false);
		}

		if (
			(selectedTemplate === "Banner" ||
				EditGuide?.GuideType === "Banner" ||
				selectedTemplateTour === "Banner" ||
				selectedStepType === "Banner") &&
			buttonSection.length == 0
		) {
			buttonSection = buttonsContainer.map(mapButtonSection);
		} else if ((EditGuide?.GuideType === "Banner" || EditGuide?.GuideType === "Tour") && bannerButtonSelected) {
			const customButton =
				buttonSection
					?.map((section) =>
						section.CustomButtons.map((button: any) => ({
							...button,
							ContainerId: section.Id, // Attach the container ID for grouping
						}))
					)
					?.reduce((acc, curr) => acc.concat(curr), []) || [];
		} else if (EditGuide?.GuideType === "Banner") {
			buttonSection = [];
		}
		const customButton =
			buttonSection
				?.map((section) =>
					section.CustomButtons.map((button: any) => ({
						...button,
						ContainerId: section.Id, // Attach the container ID for grouping
					}))
				)
				?.reduce((acc, curr) => acc.concat(curr), []) || [];

		// Group buttons by ContainerId to preserve section separation
		const groupedButtons = customButton.reduce((acc: any, button: any) => {
			const containerId = button.ContainerId || "default"; // Use a ContainerId or fallback
			if (!acc[containerId]) {
				acc[containerId] = [];
			}
			acc[containerId].push(button);
			return acc;
		}, {});
		//   {Object.keys(groupedButtons).map((containerId) => (
		// 	<Box
		// 	  key={containerId}
		// 	  sx={{
		// 		display: "flex",
		// 		justifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),
		// 		  flexWrap: "wrap",
		// 		  margin:0,
		// 	 backgroundColor:groupedButtons[containerId][0]?.BackgroundColor,
		// 	   padding: "10px",
		// 	  }}
		// 	>
		// 	  {groupedButtons[containerId].map((button: any, index: number) => (
		// 		<Button
		// 			  key={index}

		// 		  variant="contained"
		// 			  sx={{
		// 				  marginRight:"13px",
		// 				margin: "0 5px",
		// 			backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff",
		// 			color: button.ButtonProperties?.ButtonTextColor || "#fff",
		// 			border: button.ButtonProperties?.BorderColor || "transparent",
		// 			fontSize: button.ButtonProperties?.FontSize || "15px",
		// 			width: button.ButtonProperties?.Width || "auto",
		// 			paddingTop: "3px",
		// 									paddingRight: "16px",
		// 									paddingBottom: "3x",
		// 									paddingLeft:"16px",
		// 			textTransform: "none",
		// 			borderRadius: button.ButtonProperties?.BorderRadius || "15px",

		// 		  }}
		// 		>
		// 		  {button.ButtonName}
		// 		</Button>
		// 	  ))}
		// 	</Box>
		//   ))}

		updateButtonContainerOnReload(customButton, "submit");
		// Update the button container in the state
	};
	useEffect(() => {
		(async () => {
			try {
				let data = null;
				let steps = [];
				let position = "";
				let padding = 0;
				let borderSize = 0;
				if (createWithAI) {
					data = interactionData;
					steps = interactionData?.GuideStep || [];
					position = steps[0]?.Canvas?.Position || "top"; // Default fallback
					padding = parseInt(steps[0]?.Canvas?.Padding ?? "10");
					borderSize = parseInt(steps[0]?.Canvas?.BorderSize ?? "2");
					if (elementSelected) {
						resetHeightofBanner(position, padding, borderSize, true);
					} else {
						const styleExTag = document.getElementById("dynamic-body-style");
						if (styleExTag) {
							document.head.removeChild(styleExTag);
						}
					}

				} else {
					if (currentGuideId != "") {
						data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);
						steps = data?.GuideDetails?.GuideStep || [];

					// Clean up any duplicate steps that might exist in the loaded guide
					cleanupDuplicateSteps();

						position = steps[0]?.Canvas?.Position || "top"; // Default fallback
						padding = parseInt(steps[0]?.Canvas?.Padding ?? "10");
						borderSize = parseInt(steps[0]?.Canvas?.BorderSize ?? "2");

						if (elementSelected) {
							resetHeightofBanner(position, padding, borderSize, true);
						} else {
							const styleExTag = document.getElementById("dynamic-body-style");
							if (styleExTag) {
								document.head.removeChild(styleExTag);
							}
							if (elementSelected) {
								resetHeightofBanner(position, padding, borderSize, true);
							} else {
								const styleExTag = document.getElementById("dynamic-body-style");
								if (styleExTag) {
									document.head.removeChild(styleExTag);
								}
							}
						}
					}
				}
			} catch (error) {
				console.error("Failed to fetch guide details:", error);
			}
		})();
	}, [elementSelected]);

	const isDisabled = true; // Set to true when you want to disable
	useEffect(() => {
		if (currentGuideId && currentGuideId != "") {
			(async () => {
				try {
					let guideDetails = null;
					let steps = [];
					let hasTooltipOrHotspot = false;
					if (createWithAI) {
						// Create a deep copy of interactionData to avoid modifying the original object
						guideDetails = JSON.parse(JSON.stringify(interactionData));
					} else {
						const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);
						hasTooltipOrHotspot = data?.GuideDetails?.GuideStep?.some(
							(step: any) => step.StepType === "Tooltip" || step.StepType === "Hotspot"
						);
						guideDetails = data?.GuideDetails || null;

					 // Clean up any duplicate steps that might exist in the loaded guide
					 cleanupDuplicateSteps();

						steps = data?.GuideDetails?.GuideStep || [];
					}
					if (!guideDetails) return;

					if (createWithAI) {
						hasTooltipOrHotspot = guideDetails?.GuideStep?.some(
							(step: any) => step.StepType === "Tooltip" || step.StepType === "Hotspot"
						);

						steps = guideDetails?.GuideStep || [];
					}
					setSelectedTemplateTour(steps[currentStep-1]?.StepType, true);
					setIsGuideInfoScreen(false);
					setSelectedTemplate(guideDetails?.GuideType || "", true);
					// Create a deep copy to avoid modifying read-only properties
					updatedGuideData = JSON.parse(JSON.stringify(guideDetails));
					setCurrentGuide(guideDetails);
					//setCurrentGuide(guideDetails);
					setEditGuide(guideDetails);
					// Set savedGuideData to enable proper banner data restoration on refresh
					setSavedGuideData(guideDetails);
					handlecreatefromScratchclick(false);
					SetGuideName(guideDetails?.Name || "New Guide");
					setProgress(

						 ["Tooltip", "Announcement"].includes(guideDetails?.GuideType) &&
  steps[0]?.Tooltip?.EnableProgress !== undefined
    ? steps[0]?.Tooltip?.EnableProgress  // Use the saved value
    : true  // Keep default as true for new guides
					);
					if (["Banner"].includes(guideDetails?.GuideType)) {
						resetHeightofBanner(
							steps[0]?.Canvas?.Position,
							parseInt(steps[0]?.Canvas?.Padding || 10),
							parseInt(steps[0]?.Canvas?.BorderSize || 2),
							true
						);
					}
					if (progress) {
						setSelectedOption(
							["Tooltip", "Announcement"].includes(guideDetails?.GuideType) && steps[0]?.Tooltip.ProgressTemplate
								? parseInt(steps[0]?.Tooltip.ProgressTemplate)
								: 1
						);
					}
					setDismiss(
						steps[0] && steps[0]?.Modal && steps[0]?.Modal?.DismissOption != undefined
							? steps[0]?.Modal?.DismissOption
							: true
					);

					// Set progress color from API response
					if (steps[0]?.Modal?.ProgressColor) {
						setProgressColor(steps[0].Modal.ProgressColor);
					}

					// Set progress settings from API response
					if (steps[0]?.Tooltip?.EnableProgress !== undefined) {
						setProgress(steps[0].Tooltip.EnableProgress);
					}
					if (steps[0]?.Tooltip?.ProgressTemplate) {
						setSelectedOption(parseInt(steps[0].Tooltip.ProgressTemplate) || 1);
					}

					// Set overlay and page interaction settings from API response
					// Load both values simultaneously to avoid mutual exclusivity conflicts
					const savedOverlayEnabled = steps[0]?.Overlay !== undefined
						? steps[0].Overlay
						: (steps[0]?.Design?.BackdropShadow !== undefined ? steps[0].Design.BackdropShadow : overlayEnabled);
					const savedPageInteraction = steps[0]?.Tooltip?.InteractWithPage !== undefined
						? steps[0].Tooltip.InteractWithPage
						: pageinteraction;

					// Load both values using the skipMutualExclusivity parameter to preserve saved state
					setOverlayEnabled(savedOverlayEnabled, true); // Skip mutual exclusivity during loading
					setPageInteraction(savedPageInteraction, true); // Skip mutual exclusivity during loading

					// For announcements, apply global overlay state to all steps after loading
					if (guideDetails?.GuideType === "Announcement") {
						// Apply the first step's overlay setting to all steps
						const globalOverlayState = savedOverlayEnabled;
						const globalPageInteractionState = savedPageInteraction;

						steps.forEach((step:any, index:any) => {
							step.Overlay = globalOverlayState;
							if (step.Tooltip) {
								step.Tooltip.InteractWithPage = globalPageInteractionState;
							}
						});

						console.log("🔄 Applied global overlay state to all announcement steps", {
							globalOverlay: globalOverlayState,
							globalPageInteraction: globalPageInteractionState,
							totalSteps: steps.length
						});
					}
					const guideType = guideDetails?.GuideType || "";
					const guideSteps = steps || [];

					if (guideDetails?.GuideType === "Tour") {
						setTooltipCount(steps.length - 1); //added now until previous all re working fine
						getGuideTypeValue(steps[currentStep-1]?.StepType, steps);
						setTourDataOnEdit(steps, hasTooltipOrHotspot);
						//setTooltipDataOnEdit(data?.GuideDetails?.GuideStep)
						if (steps[0]?.StepType === "Banner") {
							setBannerPopup(true);
							// Only set default position for new banners
							if (!steps[0]?.Canvas?.Position) {
								setBposition("Cover Top");
							} else {
								// Use the saved position for existing banners
								setBposition(steps[0]?.Canvas?.Position);
							}
							resetHeightofBanner(
								steps[0]?.Canvas?.Position || "Cover Top",
								parseInt(steps[0]?.Canvas?.Padding),
								parseInt(steps[0]?.Canvas?.BorderSize)
							);
						}
					} else {
						getGuideTypeValue(guideDetails.GuideType, steps);
					}
				} catch (error) {
					console.error("Failed to fetch guide details:", error);
				}
			})();
		}
	}, [currentGuideId, interactionData]);

	useEffect(() => {
		if (currentGuideId && currentGuideId != "") {
			setIsGuideInfoScreen(false);
			setSelectedTemplate(updatedGuideData?.GuideType || "", true);
			handlecreatefromScratchclick(false);
			SetGuideName(updatedGuideData?.Name || "New Guide");

			const guideType = updatedGuideData?.GuideType || "";
			const guideSteps = steps || [];
			if (selectedTemplate === "Tour") {
				//setSelectedTemplate(updatedGuideData?.GuideStep?.[currentStep - 1].StepType ||"");
				getGuideTypeValue(updatedGuideData?.GuideStep?.[currentStep - 1]?.StepType || "", updatedGuideData?.GuideStep);
			} else getGuideTypeValue(updatedGuideData?.GuideType, updatedGuideData?.GuideStep);
		}
	}, [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview]);
	// const deleteStep = (stepId: string) => {
	// 	setSteps((prevSteps:Steps) => {
	// 		// Find the index of the step to be removed
	// 		const stepIndex = prevSteps.findIndex((step:any) => step.id === stepId);

	// 		// Filter out the step to be removed
	// 		const updatedSteps = prevSteps.filter((step:any) => step.id !== stepId);

	// 		// If the current step corresponds to the deleted step, update it
	// 		if (prevSteps[stepIndex]?.stepCount === currentStep) {
	// 			const newCurrentStep =
	// 				updatedSteps[stepIndex - 1]?.stepCount || updatedSteps[0]?.stepCount || 0;
	// 			setCurrentStep(newCurrentStep); // `currentStep` is a number
	// 		}

	// 		return updatedSteps;
	// 	});
	// };

	const [openDialog, setOpenDialog] = useState(false);
	if (updatedGuideData?.GuideStep) {
		// Create a new array with updated properties instead of modifying in place
		const updatedSteps = updatedGuideData.GuideStep.map((step: any) => ({
			...step,
			Modal: {
				...step.Modal,
				DismissOption: dismiss,
				ProgressColor: ProgressColor,
			},
			Tooltip: {
				...step.Tooltip,
				// For announcements, use global page interaction state; for others, use current state
				InteractWithPage: selectedTemplate === "Announcement" ? pageinteraction : step.Tooltip?.InteractWithPage || pageinteraction,
			},
			// For announcements, use global overlay state; for others, preserve individual step overlay
			Overlay: selectedTemplate === "Announcement" ? overlayEnabled : step.Overlay,
		}));
		// Update the guide data with the new steps
		updatedGuideData = { ...updatedGuideData, GuideStep: updatedSteps };
	}
const[showtrainingField, setShowtrainingField] = useState(false);
	const handleEnableAI = async () => {
        // Check if scraping is already active
        if (isScrapingActive()) {
            alert('Scraping is already in progress');
            return;
        }
		setShowtrainingField(false);
        try {
			setIsCollapsed(true);
            // Start scraping directly without file upload
            startScraping();
            setShowScrapingButton(true);
        } catch (error) {
            console.error('Error starting scraping:', error);
            alert('Error starting scraping');
        }
    };

	const handleStartTraining = () => {
		setShowtrainingField(true);
}

	useEffect(() => {
		// Helper to open builder for a guide
		const openGuideBuilder = (guideId: string) => {
			if (guideId) {
				setCurrentGuideId(guideId);
				// Set appropriate UI state to open the builder
				setIsHomeScreen(false);
				setIsGuideInfoScreen(false);
				setIsTemplateScreen(false);
				setIsPopupOpen(false);
				setBannerPopup(false);
				setIsBannerCreationBuilderOpen(false);
				setIsAnnouncementPopupOpen(false);
				setIsAnnouncementCreationBuilderOpen(false);
				setIsTooltipCreationBuilderOpen(false);
				setIsHotspotCreationBuilderOpen(false);
				setShowBannerenduser(false);
				setShowHotspotenduser(false);
				setIsTooltipPopup(false);
				setIsChecklistPreview(false);
				setIsTourTemplate(false);
				// Optionally set the correct template based on guide type
				// setSelectedTemplate(...)
			}
		};

		// Listen for custom event from content.js
		const handler = (e: CustomEvent<{ guideId: string }>) => {
			const guideId = e.detail && e.detail.guideId;
			if (guideId) {
				sessionStorage.setItem('pending_guide_id', guideId);
				if (isLoggedIn) {
					openGuideBuilder(guideId);
					sessionStorage.removeItem('pending_guide_id');
				}
			}
		};
		window.addEventListener('quickadopt-guide-id-detected', handler as EventListener);

		// On mount, check sessionStorage for pending_guide_id
		const pendingGuideId = sessionStorage.getItem('pending_guide_id');
		if (pendingGuideId && isLoggedIn) {
			openGuideBuilder(pendingGuideId);
			sessionStorage.removeItem('pending_guide_id');
		}

		return () => {
			window.removeEventListener('quickadopt-guide-id-detected', handler as EventListener);
		};
	}, [isLoggedIn]);

	// Add this useEffect to always show FeatureSelectionModal for Tour guides
	// useEffect(() => {
	// 	if (selectedTemplate === "Tour" && isDrawerClosed && isLoggedIn && !showBannerenduser) {
	// 		setIsTourPopupOpen(true);
	// 	}
	// 	// Optionally, you can add more conditions if needed
	// }, [selectedTemplate, isDrawerClosed, isLoggedIn, showBannerenduser]);

	return (
		<>
			{/* <ElementSelector
  setSelectedElement={setSelectedElement}
  targetUrl={initialGuideData.TargetUrl}
			/> */}

			{/* Display selected element details */}
			{/* {selectedElement && (
  <div style={{ marginTop: "20px", padding: "10px", border: "1px solid #ccc" }}>
	<h3>Selected Element Details:</h3>
	<p><strong>Tag:</strong> {selectedElement.tagName}</p>
	<p><strong>ID:</strong> {selectedElement.id || "None"}</p>
	<p><strong>Class:</strong> {selectedElement.className || "None"}</p>
	<p><strong>Attributes:</strong></p>
	<ul>
	  {selectedElement.attributes.map((attr: any, index: number) => (
		<li key={index}>
		  {attr.name}: {attr.value}
		</li>
	  ))}
	</ul>
  </div>

)} */}

			{showtrainingField && (
				<TrainingField
					setShowtrainingField={setShowtrainingField}
					handleEnableAI={handleEnableAI}
					showtrainingField={showtrainingField}

				/>
			)}



			{(selectedTemplate === "Checklist" || EditGuide?.GuideType === "Checklist") &&
				!isChecklistPreview &&
				currentGuideId &&
				isLoggedIn &&
				isDrawerClosed &&
				!showBannerenduser && (
					<>
						<ChecklistApp
							setopenWarning={setOpenWarning}
							handleLeave={handleLeave}
						/>
					</>
				)}

			{deleteClicked && (
				<Dialog
					className="qadpt-tour-popup"
					open={openDialog}
					onClose={() => setOpenDialog(false)}
					PaperProps={{
						style: {
							borderRadius: "4px",
							maxWidth: "400px",
							textAlign: "center",
							maxHeight: "300px",
							boxShadow: "none",
						},
					}}
				>
					<DialogTitle sx={{ padding: 0 }}>
						<div style={{ display: "flex", justifyContent: "center", padding: "10px" }}>
							<div
								style={{
									backgroundColor: "#e4b6b0",
									borderRadius: "50%",
									width: "40px",
									height: "40px",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
								}}
							>
								<DeleteOutlineOutlinedIcon sx={{ color: "#F44336", height: "20px", width: "20px" }} />
							</div>
						</div>
						<Typography sx={{ fontSize: "16px !important", fontWeight: 600, padding: "0 10px" }}>
							Confirmation
						</Typography>
					</DialogTitle>

					<DialogContent sx={{ padding: "20px !important" }}>
						<DialogContentText style={{ fontSize: "14px", color: "#000" }}>
							Are you sure you want to delete the {nameToDelete}?
						</DialogContentText>
					</DialogContent>

					<DialogActions sx={{ justifyContent: "space-between", borderTop: "1px solid var(--border-color)" }}>
						<Button
							onClick={() => setOpenDialog(false)}
							sx={{
								color: "#9E9E9E",
								border: "1px solid #9E9E9E",
								borderRadius: "4px",
								textTransform: "capitalize",
								padding: "var(--button-padding)",
								lineHeight: "var(--button-lineheight)",
							}}
						>
							Cancel
						</Button>
						<Button
							onClick={() => handleDeleteStep(idToDelete)}
							sx={{
								backgroundColor: "var(--error-color)",
								color: "#FFF",
								borderRadius: "4px",
								textTransform: "capitalize",
								padding: "var(--button-padding)",
								lineHeight: "var(--button-lineheight)",
								// "&:hover": {
								// 	backgroundColor: "#D32F2F",
								// },
							}}
						>
							Delete
						</Button>
					</DialogActions>
				</Dialog>
			)}

			{designPopup && (
				<DesignMenu
					currentGuide={currentGuide}
					overlays={overlays}
					setDesignPopup={setDesignPopup}
					designPopup={designPopup}
					setOverLays={setOverLays}
					selectedTemplate={selectedTemplate}
					backgroundC={backgroundC}
					setBackgroundC={setBackgroundC}
					Bposition={Bposition}
					setBposition={setBposition}
					bpadding={bpadding}
					setbPadding={setbPadding}
					Bbordercolor={Bbordercolor}
					setBBorderColor={setBBorderColor}
					BborderSize={BborderSize}
					setBBorderSize={setBBorderSize}
					zindeex={zindeex}
					setZindeex={setZindeex}
					initialGuideData={initialGuideData}
					updatedGuideData={updatedGuideData}
					handleSaveGuide={handleSaveGuide}
					resetHeightofBanner={resetHeightofBanner}
				/>
			)}

{isChecklistPreview ? (



<>
	<div className="qadpt-editor">
<Button
	onClick={handleBackToEditorClick}
	variant="text"
	>
		<div className="qadpt-sep">
		<span dangerouslySetInnerHTML={{ __html: backicon }} />
			<div className="edt-txt">Editor</div>
			</div>

	</Button>
	<div className="qadpt-curstep">Step:{`${currentStep}`}</div>
</div>
	<ChecklistLauncher />
</>


) : isAnnouncementPopupOpen ? (
<>

<div className="qadpt-editor">
<Button
	onClick={handleBackToEditorClick}
	variant="text"
	>
		<div className="qadpt-sep">
		<span dangerouslySetInnerHTML={{ __html: backicon }} />
			<div className="edt-txt">Editor</div>
			</div>

	</Button>
	<div className="qadpt-curstep">Step:{`${currentStep}`}</div>
</div>

	<AnnouncementPopup
		selectedTemplate={selectedTemplate}
		handlecloseBannerPopup={handleCloseBannerPopup}
		guideStep={guideStep}
		anchorEl={document.body}
		onClose={handleCloseAnnouncementPopup}
		onPrevious={() => {}}
		onContinue={() => {}}
		title={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || ""}
		text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
		imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
		previousButtonLabel="Back"
		continueButtonLabel="Next"
		currentStep={currentStep} // Adjust currentStep for display (1-based index)
		totalSteps={updatedGuideData?.GuideStep?.length || 1}
		onDontShowAgain={() => {}}
		progress={(currentStep / (updatedGuideData?.GuideStep?.length || 1)) * 100}
		textFieldProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
		imageProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
		customButton={
			updatedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section: any) =>
				section.CustomButtons.map((button: any) => ({
					...button,
					ContainerId: section.Id, // Attach the container ID for grouping
				}))
			)?.reduce((acc: any, curr: any) => acc.concat(curr), []) || []
		}
		modalProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
		canvasProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
		htmlSnippet={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
		OverlayValue={overlayEnabled}
		savedGuideData={updatedGuideData}
		backgroundC={backgroundC}
		Bposition={Bposition}
		bpadding={bpadding}
		Bbordercolor={Bbordercolor}
		BborderSize={BborderSize}
		ProgressColor={ProgressColor}
	/>


</>
) : showTooltipenduser ? (
<>
	<div className="qadpt-editor">
<Button
	onClick={handleBackToEditorClick}
	variant="text"
	>
		<div className="qadpt-sep">
		<span dangerouslySetInnerHTML={{ __html: backicon }} />
			<div className="edt-txt">Editor</div>
			</div>

	</Button>
	<div className="qadpt-curstep">Step:{`${currentStep}`}</div>
</div>
<TooltiplastUserview
		guideStep={guideStep}
		onClose={handleCloseHotspotPopup}
		title={updatedGuideData?.GuideStep?.[currentStep]?.StepTitle || ""}
		text={updatedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
		imageUrl={updatedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
		onPrevious={() => {}}
		onContinue={() => {}}
		currentStep={currentStep} // Adjust currentStep for display (1-based index)
		totalSteps={updatedGuideData?.GuideStep?.length || 1}
		onDontShowAgain={() => {}}
		progress={(currentStep / (updatedGuideData?.GuideStep?.length || 1)) * 100}
		textFieldProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
		imageProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
		customButton={
			updatedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section: any) =>
				section.CustomButtons.map((button: any) => ({
					...button,
					ContainerId: section.Id, // Attach the container ID for grouping
				}))
			)?.reduce((acc: any, curr: any) => acc.concat(curr), []) || []
		}
		modalProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
		canvasProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
		htmlSnippet={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
		OverlayValue={overlayEnabled}
		savedGuideData={updatedGuideData}
		//hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
		anchorEl={document.body}
		previousButtonLabel={""}
		continueButtonLabel={""}
	/>


</>
) : showHotspotenduser ? (
	<>
	<div className="qadpt-editor qadpt-baneditor">
<Button
	onClick={handleBackToEditorClick}
	variant="text"
	>
		<div className="qadpt-bansep">
		<span dangerouslySetInnerHTML={{ __html: backicon }} />
			<div className="edt-txt">Editor</div>
			</div>

	</Button>
	{/* <div className="qadpt-curstep">Step:{`${currdentStep}`}</div> */}
</div>
<HotspotPreview
		isHotspotPopupOpen={isHotspotPopupOpen}
		showHotspotenduser={showHotspotenduser}
		handleHotspotHover={handleHotspotHover}
		handleHotspotClick={handleHotspotClick}
		guideStep={guideStep}
		onClose={handleCloseHotspotPopup}
		title={updatedGuideData?.GuideStep?.[currentStep]?.StepTitle || ""}
		text={updatedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
		imageUrl={updatedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
		onPrevious={() => {}}
		onContinue={() => {}}
		currentStep={currentStep} // Adjust currentStep for display (1-based index)
		totalSteps={updatedGuideData?.GuideStep?.length || 1}
		onDontShowAgain={() => {}}

		progress={(currentStep / (updatedGuideData?.GuideStep?.length || 1)) * 100}
		textFieldProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
		imageProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
		customButton={
			updatedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section: any) =>
				section.CustomButtons.map((button: any) => ({
					...button,
					ContainerId: section.Id, // Attach the container ID for grouping
				}))
			)?.reduce((acc: any, curr: any) => acc.concat(curr), []) || []
		}
		modalProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
		canvasProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
		htmlSnippet={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
		OverlayValue={overlayEnabled}
		savedGuideData={updatedGuideData}
		hotspotProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
		anchorEl={document.body}
	/>
</>
) : isTourTemplate ? (
		<>
			<div className="qadpt-editor">
<Button
	onClick={handleBackToEditorClick}
	variant="text"
	>
		<div className="qadpt-sep">
		<span dangerouslySetInnerHTML={{ __html: backicon }} />
			<div className="edt-txt">Editor</div>
			</div>

	</Button>
	<div className="qadpt-curstep">Step:{`${currentStep}`}</div>
</div>
<TourPreview
		selectedTemplate={selectedTemplate}
		handlecloseBannerPopup={handleCloseBannerPopup}
		guideStep={guideStep}
		onClose={handleCloseAnnouncementPopup}
		onPrevious={() => {}}
		onContinue={() => {}}
		title={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || ""}
		text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
		imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
		previousButtonLabel="Back"
		continueButtonLabel="Next"
		currentStep={currentStep} // Adjust currentStep for display (1-based index)
		totalSteps={updatedGuideData?.GuideStep?.length || 1}
		onDontShowAgain={() => {}}

		progress={(currentStep / (updatedGuideData?.GuideStep?.length || 1)) * 100}
		textFieldProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
		imageProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
		customButton={
			updatedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section: any) =>
				section.CustomButtons.map((button: any) => ({
					...button,
					ContainerId: section.Id, // Attach the container ID for grouping
				}))
			)?.reduce((acc: any, curr: any) => acc.concat(curr), []) || []
		}
		modalProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
		canvasProperties={updatedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
		htmlSnippet={updatedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
		OverlayValue={overlayEnabled}
		savedGuideData={updatedGuideData}
		//hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
		anchorEl={document.body}
		backgroundC={undefined}
		Bposition={undefined}
		bpadding={undefined}
		Bbordercolor={undefined}
		BborderSize={undefined}
	/>
</>
) :  (
<>
{showBannerenduser && (
<>
	<div className="qadpt-editor qadpt-baneditor">
<Button
	onClick={handleBackToEditorClick}
	variant="text"
	>
		<div className="qadpt-bansep">
		<span dangerouslySetInnerHTML={{ __html: backicon }} />
			<div className="edt-txt">Editor</div>
			</div>

	</Button>
	{/* <div className="qadpt-curstep">Step:{`${currdentStep}`}</div> */}
</div>
	<BannerEndUser
		showBannerenduser={showBannerenduser}
		setShowBannerenduser={setShowBannerenduser}
		initialGuideData={updatedGuideData}
		setInitialGuideData={setInitialGuideData}
		onClose={handleCloseBannerPopup}
		backgroundC={backgroundC}
		Bposition={Bposition}
		bpadding={bpadding}
		Bbordercolor={Bbordercolor}
		BborderSize={BborderSize}
	/>
</>
				)}

					{isPopupOpen &&
						!bannerPopup &&
						currentGuideId &&
						(selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") && (
							<GuidePopUp
								//	currentStep={currentStep}
								selectedStepType={selectedStepType}
								guideStep={guideStep}
								setImageSrc={setImageSrc}
								imageSrc={imageSrc}
								textBoxRef={textBoxRef}
								setHtmlContent={setHtmlContent}
								htmlContent={htmlContent}
								buttonColor={buttonColor}
								setButtonColor={setButtonColor}
								setImageName={setImageName}
								imageName={imageName}
								openStepDropdown={openStepDropdown}
								isUnSavedChanges={isUnSavedChanges}
								openWarning={openWarning}
								setopenWarning={setOpenWarning}
								handleLeave={handleLeave}
							/>
							// <div></div>
						)}

					{/* {selectedTemplate === "Tour" && selectedTemplateTour === "Announcement"&&  (
									<GuidePopUp
									//	currentStep={currentStep}
									guideStep={guideStep}
									setImageSrc={setImageSrc}
									imageSrc={imageSrc}
									textBoxRef={textBoxRef}
									setHtmlContent={setHtmlContent}
									htmlContent={htmlContent}
									buttonColor={buttonColor}
									setButtonColor={setButtonColor}
									setImageName={setImageName}
									imageName={imageName}
									openStepDropdown={openStepDropdown}
										/>
								)} */}
					{guidesSettingspopup && <GuideSettings />}
					{isTourPopupOpen && isLoggedIn && isDrawerClosed && !showBannerenduser && (
						<FeatureSelectionModal
							isOpen={isTourPopupOpen}
							onClose={() => setIsTourPopupOpen(false)}
							guideName={guideName}
							setStepData={setStepData}
							stepData={stepData}
							count={count}
						/>
					)}
					{!isTourPopupOpen && isLoggedIn && isDrawerClosed && !showBannerenduser && (
						<>
							{(["Hotspot", "Tooltip"].includes(selectedTemplate) && elementSelected === false) ||
							(selectedTemplate === "Tour" &&
								(selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot") &&
								elementSelected === false) ? (
								<>
									<Dialog
										open={true}
										onClose={handleClose}
										maxWidth="sm"
										fullWidth
										disableEnforceFocus
										disableAutoFocus
										disableScrollLock={true}
										sx={{
											width: "500px",
											bottom: "auto !important",
											left: `${dialogBoxPosition.left}`,
											top: `${dialogBoxPosition.top}`,
											right: `${dialogBoxPosition.right}`,
											"& .MuiBackdrop-root": {
												position: "initial !important",
											},
											"& .MuiPaper-root": {
												borderRadius: "20px !important",
												boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2) !important",
												backgroundColor: "white",
											},
											zIndex:'999999 !important'
																}}
										onMouseEnter={handleMouseEnter}
									>
										<div
											style={{
												display: "flex",
												alignItems: "center",
												flexDirection: "row",
												justifyContent: "space-between",
											}}
										>
											<DialogTitle
												sx={{
													display: "flex",
													justifyContent: "space-between",
													alignItems: "center",
													backgroundColor: "#E2F1F4",
													borderRadius: "15px",
													padding: "10px 16px",
													margin: "16px 10px 0px 16px",
													width: "fit-content",
												}}
											>
												<Typography sx={{ fontSize: "14px", fontWeight: "bold", color: "#1B798E" }}>
													Element Selection
												</Typography>
												{/* <IconButton size="small">
												<CloseIcon sx={{ color: "#D9534F" }} />
											</IconButton> */}
											</DialogTitle>
											<li style={{ margin: "16px 10px 0px 16px", listStyle: "none" }}>
												<ul
													style={{
														listStyleType: "disc",
														paddingLeft: "20px",
														margin: 0,
														background: "#ededed",
														borderRadius: "10px",
														padding: "0 6px 0 20px",
													}}
												>
													{" "}
													{isALTKeywordEnabled ? (
														<Typography sx={{ color: "#ccc", fontSize: "12px !important" }}>
															{" "}
															Element Selection Mode
														</Typography>
													) : (
														<Typography sx={{ color: "#ccc", fontSize: "12px !important" }}>
															{" "}
															Page Interaction Mode{" "}
														</Typography>
													)}
												</ul>
											</li>
										</div>

										{/* Content */}
										<DialogContent sx={{ padding: "16px" }}>
  {[
    { text: "Press the 'Esc' key to return.", boldPart: "'Esc' key" },
    { text: "Hover over the element to highlight it.", boldPart: "highlight" },
    { text: "Click on the element to select it.", boldPart: "select" },
    {
      text: "Press the 'q' key to switch between Element selection Mode & Page Interaction Mode.",
      boldPart: "'q' key",
    },
    {
      text: "Press the 'Esc' key and then press 'q' again when Three Dot menu is Highlighted on Chrome to Navigate Element selection Mode Again.",
      boldPart: ["'Esc' key", "'q'"],
    },
  ].map((item, index) => {
    const boldParts = Array.isArray(item.boldPart) ? item.boldPart : [item.boldPart];

    // Split text into fragments and apply bold where matched
    const renderTextWithBold = (text:any, boldWords:any) => {
      const regex = new RegExp(`(${boldWords.map((word:any) => word.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')).join('|')})`, 'g');
      return text.split(regex).map((part:any, i:any) =>
		boldWords.some((word:any) => part.includes(word)) ? <b key={i}>{part}</b> : <React.Fragment key={i}>{part}</React.Fragment>
      );
    };

    return (
      <Box key={index} sx={{ display: "flex", alignItems: "center", marginBottom: "8px" }}>
        <span style={{ color: "#1B798E", fontSize: "16px", marginRight: "8px" }}>➤</span>
        <Typography sx={{ fontSize: "14px", color: "#333" }}>
          {renderTextWithBold(item.text, boldParts)}
        </Typography>
      </Box>
    );
  })}
</DialogContent>

									</Dialog>
								</>
							) : (
								<>
									<div className="qadpt-ext-banner">
										<div className="qadpt-ext-banner">
											<div className="qadpt-left-banner">
												<div style={{ display: "flex", alignItems: "center" }}>
													<IconButton
														className="qadpt-banner-button qadpt-icon"
														disableRipple
													>
														{/* Back Icon Part */}
														<div
															onClick={handleBackClick}
															style={{ display: "flex", alignItems: "center", cursor: "pointer", marginLeft: "6px" }}
														>
														<span className="back-icon" dangerouslySetInnerHTML={{ __html: backicon }}
														/>
														</div>
														<div
															style={{
																marginLeft: 8,
																maxWidth: "120px",
																paddingLeft: "10px",
																borderLeft: "2px solid #ccc",
															}}
														>
															<Tooltip
																title={`Rename the ${selectedTemplate}`}
																style={{ zIndex: 9999999 }}
															>
																{isEditing ? (
																	<TextField
																		value={tempGuideName}
																		onChange={handleChange}
																		onKeyDown={handleKeyDown}
																		onBlur={handleBlur}
																		autoFocus
																		inputRef={(input) => {
																			// This ensures the input stays focused when validation fails
																			if (input && tempGuideName.trim().length < 3) {
																				input.focus();
																			}
																		}}
																		style={{ borderRight: "1px solid #EDEDED", width: "100%" }}
																		InputProps={{
																			sx: {
																				"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
																				"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
																				"& fieldset": { border: "none" },
																				"&.MuiInputBase-root": {
																					padding: "0 4px",
																				},
																			},
																		}}
																	/>
																) : (
																	<span
																		onClick={handleEditsClick} // Switch to edit mode when clicked
																		style={{
																			cursor: "pointer",
																			display: "inline-block",
																			maxWidth: "80%", // Ensures the ellipsis effect is applied
																			overflow: "hidden",
																			whiteSpace: "nowrap",
																			textOverflow: "ellipsis",
																			verticalAlign: "middle",
																		}}
																		title={guideName} // Shows full text on hover
																	>
																		{guideName.length > 10 ? `${guideName.substring(0, 10)}...` : guideName}
																	</span>
																)}
															</Tooltip>
														</div>
													</IconButton>
												</div>

												<Tooltip title="Design Properties">
													<Button
														className="qadpt-banner-button"
														onClick={handleDesignclick}
													>
														<span dangerouslySetInnerHTML={{ __html: designicon }} />
														<span>Design</span>
													</Button>
												</Tooltip>

												{/* <Button
											className="qadpt-banner-button"
											onClick={handleGuidesSettingsclick}
										>
											<span dangerouslySetInnerHTML={{ __html: settingsicon }} />
											<span>Settings</span>
										</Button> */}
											</div>
											<div
												className="qadpt-center-banner"
												style={{ position: "relative" }}
											>
												{selectedTemplate === "Banner" ||
												selectedTemplate === "Hotspot" ||
												selectedTemplate === "Checklist" ? (
													""
												) : (
													<>
														<Tooltip title="Create New Step">
															<IconButton
																className="qadpt-banner-button qadpt-icon"
																sx={{
																	background: "var(--primarycolor)",
																	padding: "10px !important",
																	"&:hover": {
																		background: "var(--primarycolor)",
																		color: "var(--white-color)"
																	},
																}}
																onClick={() => {
																	handleStepsDropdown();

																	//
																}}
															>
																<AddIcon sx={{ height: "24px", width: "24px" }} />
															</IconButton>
														</Tooltip>
														<div>
															<Button
																className="qadpt-banner-button"
																onClick={handleOpenStepDropdown}
																// disabled={selectedTemplate === "Hotspot"}
															>
																{steps.find((item: any) => item.stepCount === currentStep)?.name}

																{/* Step {currentStep} */}
																<ArrowDropDownIcon fontSize="medium"
																	sx={{fontSize: "26px !important", marginRight: "-10px"}}
																/>
															</Button>
															<Box ref={divRef}>
																<div
																	className="step-dropdown"
																	style={{
																		display: "flex",
																		position: "absolute",
																		top: "51px",
																		gap: "5px",
																		left: "10%",
																	}}
																>
																	{!openStepDropdown ? null : (
																		<div
																			className="qadpt-stpdrp"
																			style={{
																				backgroundColor: "var(--white-color)",
																				width: "290px",
																				boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
																				borderRadius: "8px",
																				// padding: "16px",
																				zIndex: "9999",
																				display: "flex",
																				flexDirection: "column",
																				height: "fit-content",
																				fontSize: "16px",
																			}}
																		>
																			{/* <div>

																				<IconButton
																					size="small"
																					aria-label="close"
																					onClick={handleOpenStepDropdown}
																					style={{
																						position: "absolute",
																						top: "1px",
																						right: "-18px",
																						width: "16px",
																						height: "16px",
																						backgroundColor: "var(--white-color)",
																						boxShadow: "rgba(0, 0, 0, 0.1) 0px 4px 8px",
																					}}
																				>
																					<CloseIcon />
																				</IconButton>
																			</div> */}
																			{/* Buttons with icons */}

											<div
																onClick={(e) => {					 
																	const target = (e.target as HTMLElement).closest<HTMLDivElement>("[data-id]");
																	if (!target) return;
																	const selectedStepCount = steps.find(
																		(item: any) => item.id === target.dataset.id
																	)?.stepCount;
																	if (selectedStepCount !== currentStep) {
																		const selectedStepTitle = steps.find(
																			(item: any) => item.id === target.dataset.id
																		)?.name;

																		handleStepChange(target.dataset.id); // Pass the step title
																	}
																}}
																style={{ padding: "10px 10px 0 10px" }}
																			>
																				<PerfectScrollbar style={{ maxHeight: "170px" }}>
																					{steps.map((item: any, index: number) => (
																						<div
																							key={item.id}
																							data-id={item.id}
																							style={{
																								transition: "background-color 0.3s",
																								display: "flex",
																								justifyContent: "space-between",
																								alignItems: "center",
																								cursor: "pointer",
																								borderRadius: "12px",
																								padding: "5px 10px",
																								maxHeight: "70px",
																								textTransform: "capitalize",
																								backgroundColor:
																									Number(currentStep) === Number(item.stepCount)
																										? "#EFF5F6"
																										: "transparent",
																							}}
																							onMouseEnter={(e) => {
																								e.currentTarget.style.backgroundColor = "#EFF5F6";
																								e.currentTarget.style.marginTop = "5px";
																							}}
																							onMouseLeave={(e) => {
																								if (Number(currentStep) !== Number(item.stepCount)) {
																									e.currentTarget.style.backgroundColor = "transparent";
																									e.currentTarget.style.marginTop = "5px";
																								}
																							}}
																							onClick={() => {
																								if (item.stepCount !== currentStep) {
																									changeCurrentStep(item.id, item.stepType);
																								}
																							}}
																						>
																							<div
																								style={{
																									display: "flex",
																									flexDirection: "column",
																									alignItems: "start",
																									width: "100%",
																								}}
																							>
																								<div
																									style={{
																										display: "flex",
																										alignItems: "center",
																										width: "100%",
																										justifyContent: "space-between",
																									}}
																								>
																									<div style={{ display: "flex", alignItems: "center", gap: "6px" }}>
																										<div
																											style={{
																												width: "16px",
																												height: "16px",
																												borderRadius: "50%",
																												backgroundColor: "var(--primarycolor)",
																												color: "var(--white-color)",
																												display: "flex",
																												alignItems: "center",
																												justifyContent: "center",
																												fontSize: "12px",
																											}}
																										>
																											{index + 1}
																										</div>
																										<div
																											style={{
																												display: "block",
																												overflow: "hidden",
																												textOverflow: "ellipsis",
																												whiteSpace: "nowrap",
																												fontSize: "14px",
																												textAlign: "left",
																												width: "140px",
																											}}
																										>
																											{item.name}
																										</div>
																										{/* URL indicator
																										{toolTipGuideMetaData[index]?.stepTargetURL && (
																											<div
																												style={{
																													fontSize: "12px",
																													color: "#4CAF50"
																												}}
																												title={`URL saved: ${toolTipGuideMetaData[index]?.stepTargetURL}`}
																											>
																												🔗
																											</div>
																										)} */}
																									</div>
																									<div style={{ display: "flex", gap: "3px" }}>
																										<Button
																											onClick={(e) => {
																												e.stopPropagation();
																												handleRenamStepName(
																													item.id,
																													item.name,
																													item.stepType,
																													item.stepDescription
																												);
																											}}
																											size="small"
																											sx={{
																												placeContent: "end",
																												minWidth: "auto",
																												borderRadius: "50%",
																												"&:hover": {
																													background: "#5F9D9F47",
																												},
																											}}
																										>
																											<span dangerouslySetInnerHTML={{ __html: editline }} />
																										</Button>
																										<Button
																											onClick={(e) => {
																												e.stopPropagation();

																												handleDelete(item.id);
																												isChangesSaved = false;
																												setIsSaveClicked(false);
																												handleSaveGuide();
																												handleUpdateGuide();
																											}}
																											disabled={steps.length === 1}
																											size="small"
																											sx={{
																												placeContent: "end",
																												minWidth: "auto",
																												opacity: steps.length === 1 ? 0.5 : 1, // Optional: Reduce opacity if disabled
																												pointerEvents: steps.length === 1 ? "none" : "auto", // Optional: Prevent hover effects
																											}}
																										>
																											<span dangerouslySetInnerHTML={{ __html: deletestep }} />
																										</Button>
																									</div>
																								</div>
																								{selectedTemplate === "Tour" && (
																									<div style={{ color: "#ccc" }}>{item?.stepType}</div>
																								)}
																								<div
																									style={{
																										overflow: "hidden",
																										width: "140px",
																										height: "auto",
																										textOverflow: "ellipsis",
																										display: "-webkit-box",
																										WebkitBoxOrient: "vertical",
																										WebkitLineClamp: 2,
																										textAlign: "left",
																										fontSize: "13px",
																									}}
																								>
																									{item?.stepDescription}
																								</div>
																							</div>
																							{/* <div style={{ display: "flex", gap: "3px" }}>
																							<Button
																								onClick={(e) => {
																									e.stopPropagation();

																											handleRenamStepName(item.id, item.name, item.stepType, item.stepDescription);

																								}}
																								size="small"
																								sx={{ placeContent: "end", minWidth: "auto", borderRadius: "50%",
																									"&:hover": {
																											background: "#5F9D9F47",
																											}, }}
																							>
																								<span
																									dangerouslySetInnerHTML={{ __html: editline }}
																								/>
																							</Button>
																							<Button
																								onClick={(e) => {
																											if (steps.length === 1) return;
																									e.stopPropagation();

																									handleDelete(item.id);
																									setIsSaveClicked(false);

																									handleUpdateGuide();
																								}}
																								disabled={steps.length === 1}
																								size="small"
																								sx={{
																									placeContent: "end",
																									minWidth: "auto",
																									opacity: steps.length === 1 ? 0.5 : 1, // Optional: Reduce opacity if disabled
																									pointerEvents: steps.length === 1 ? "none" : "auto", // Optional: Prevent hover effects
																								}}
																							>
																								<span
																									dangerouslySetInnerHTML={{ __html: deletestep }}
																								/>
																							</Button>
																						</div> */}
																						</div>
																					))}
																				</PerfectScrollbar>
																			</div>
																			<div style={{ padding: "10px" }}>
																				<Button
																					onClick={() => {
																						// Close all button popups when creating new step
																						triggerCloseAllButtonPopups();
																						// Reset AI state when manually creating steps
																						if (createWithAI) {
																							setCreateWithAI(false);
																						}
																						setPlusIconClick(true);
																						setStepCreation(true);
																						setStepName(`Step ${steps.length + 1}`);
																						setCurrentStepNameId("");
																						setIsEditStepName(false);
																						setShowTextField((prev) => !prev);
																						setDescription("");
																						setEditType("Announcement");
																						setSelectedStepTypeHotspot(false);
																						setStepData({
																							...stepData,
																							type: "",
																							stepNumber: `Step ${steps.length + 1}`,
																						});
																						// Reset AI state when manually creating steps (do this last)
																						if (createWithAI) {
																							setCreateWithAI(false);
																						}
																					}}
																					disabled={stepCreation || isEditStepName}
																					style={{
																						width: "100%",
																						padding: "10px",
																						backgroundColor: "var(--primarycolor)",
																						borderRadius: "6px",
																						color: "var(--white-color)",
																						height: "29px",
																						gap: "8px",
																						opacity: stepCreation || isEditStepName ? "0.5" : "1",
																					}}
																				>
																					<AddIcon style={{ fontSize: "16px" }} />
																					Create Step
																				</Button>
																			</div>
																		</div>
																	)}
																	{stepCreation && (
																		<StepCreationPopup
																			isOpen={stepCreation}
																			onClose={() => {
																				setStepCreation(false);
																				setPlusIconClick(false);
																				handleOpenStepDropdown();
																			}}
																			onCreateStep={handleCreateStep}
																			stepData={stepData}
																			setStepData={setStepData}
																			isEditStepName={false}
																			stepName={stepName}
																			setStepName={setStepName}
																			setEditType={setEditType}
																			editType={editType}
																			setDescription={setDescription}
																			editDescription={editDescription}
																			setTemplateSelectionPopup={setTemplateSelectionPopup}
																			//  position={popupPosition}
																		/>
																	)}
																	{isEditStepName && (
																		<StepCreationPopup
																			isOpen={isEditStepName}
																			onClose={() => {
																				setIsEditStepName(false);
																				setStepCreation(false);
																				setPlusIconClick(false);
																				handleOpenStepDropdown();
																			}}
																			onCreateStep={handleCreateStep}
																			stepData={stepData}
																			setStepData={setStepData}
																			isEditStepName={isEditStepName}
																			stepName={stepName}
																			setStepName={setStepName}
																			setEditType={setEditType}
																			editType={editType}
																			setDescription={setDescription}
																			editDescription={editDescription}
																			setTemplateSelectionPopup={setTemplateSelectionPopup}

																			//  position={popupPosition}
																		/>
																	)}
																</div>
															</Box>
															{/* </>
												</ClickAwayListener> */}
														</div>
													</>
												)}
												{/* Undo/Redo Buttons */}
												<UndoRedoButtons
													size="small"
													className="qadpt-banner-button qadpt-icon"
													disabled={isUnSavedChanges === false}
												/>
											</div>
											<div className="qadpt-right-banner">
												<Tooltip title="Preview">
													<IconButton
														className="qadpt-banner-button qadpt-icon"
														onClick={async () => {
															setIsSaveClicked(false);
															isChangesSaved = false;
															setIsSaveInProgress(true);

															try {
																handleSaveGuide();

																handlePlayIconClick();
																handlePreviewGuide();
															} finally {
																setTimeout(() => setIsSaveInProgress(false), 100);
															}
														}}
													>
														<span
															dangerouslySetInnerHTML={{ __html: playicon }}
															style={{ width: "24px", placeContent: "center" }}
														/>{" "}
													</IconButton>
												</Tooltip>

												<Tooltip title={!isUnSavedChanges ? "Make changes to enable" : "Save Changes"}>
													<span>
														<IconButton
															className={`qadpt-banner-button qadpt-icon ${!isUnSavedChanges ? "qadpt-save" : ""}`}
															onClick={async () => {
																setIsSaveClicked(true);
																isChangesSaved = true;
																setIsSaveInProgress(true);

																try {
																	

																	// Save current URL for the current step using store function
																	saveCurrentStepURL();

																	// Add a small delay to ensure state update is reflected
																	await new Promise(resolve => setTimeout(resolve, 50));

																	// First prepare the guide data
																	handleSaveGuide();

																	// Then call the appropriate save function
																	if (isAIGuidePersisted) {
																		await handleUpdateGuide();
																	} else {
																		await handleUpdateGuideThroughAI();
																	}
																} finally {
																	// Reset save progress after a delay to allow restoration logic to complete
																	setTimeout(() => setIsSaveInProgress(false), 1000);
																}
															}}
															disabled={!isUnSavedChanges}
														>
																					{loading ? "Saving..." : <span dangerouslySetInnerHTML={{ __html: Save }}
																					style={{ width: "24px", placeContent: "center" }}/>}
														</IconButton>
													</span>
												</Tooltip>

												<Tooltip title="Open Panel in New Tab">
													<IconButton
														className="qadpt-banner-button qadpt-icon"
														onClick={handleWebOpenPortal}
														disabled={loading}
													>
																				{loading ? "Opening.." : <span dangerouslySetInnerHTML={{ __html: PublishAndMore }}
																				style={{ width: "24px", placeContent: "center" }}/>}
													</IconButton>
												</Tooltip>
												<Tooltip title="Close Extension">
													<IconButton
														className="qadpt-banner-button"
														onClick={() => {
															closedGuide = true;
															handleClose();
														}}
														disabled={loading}
													>
														{<CloseIcon sx={{ zoom: "1.4" }} />}
													</IconButton>
												</Tooltip>
											</div>
										</div>
									</div>
								</>
							)}
						</>
					)}
					{isDrawerClosed && bannerPopup && !showBannerenduser && currentGuideId && (
						<Banner
							isUnSavedChanges={isUnSavedChanges}
							openWarning={openWarning}
							setopenWarning={setOpenWarning}
							handleLeave={handleLeave}
							setImageSrc={setImageSrc}
							imageSrc={imageSrc}
							textBoxRef={textBoxRef}
							setHtmlContent={setHtmlContent}
							htmlContent={htmlContent}
							buttonColor={buttonColor}
							setButtonColor={setButtonColor}
							setImageName={setImageName}
							imageName={imageName}
							alignment={alignment}
							setAlignment={setAlignment}
							textvalue={textvalue}
							setTextvalue={setTextvalue}
							isBanner={false}
							overlays={overlays}
							setOverLays={setOverLays}
							Bposition={Bposition}
							setBposition={setBposition}
							bpadding={bpadding}
							setbPadding={setbPadding}
							// setBBorderColor={setBBorderColor}
							// BborderSize={BborderSize}
							// setBorderSize={setBBorderSize}
							savedGuideData={updatedGuideData}
							Progress={(currentStep / (updatedGuideData?.GuideStep?.length || 1)) * 100}
						/>
					)}

					{/* {(selectedTemplateTour==="Banner" && bannerPopup) &&(
								<Banner
							setImageSrc={setImageSrc}
							imageSrc={imageSrc}
							textBoxRef={textBoxRef}
							setHtmlContent={setHtmlContent}
							htmlContent={htmlContent}
							buttonColor={buttonColor}
							setButtonColor={setButtonColor}
							setImageName={setImageName}
							imageName={imageName}
							alignment={alignment}
							setAlignment={setAlignment}
							textvalue={textvalue}
							setTextvalue={setTextvalue}
							isBanner={false}
							overlays={overlays}
							setOverLays={setOverLays}
							Bposition={Bposition}
							setBposition={setBposition}
							bpadding={bpadding}
							setbPadding={setbPadding}
							// setBBorderColor={setBBorderColor}
							// BborderSize={BborderSize}
							// setBorderSize={setBBorderSize}
						/>
			)} */}

					{!isDrawerClosed && (
						<div
							className={`qadpt-overlay ${
								!isCollapsed && !isExtensionClosed ? "notcollapsed" : isExtensionClosed ? "closed" : ""
							}`}
						>
							<div
								id="leftDrawer"
								className={`leftDrawer ${isCollapsed ? "collapsed" : ""}`}
							>
								<div
									id="drawerHeader"
									className="qadpt-drawerHeader"
								>
									<div
										id="toggleIcon"
										className="qadpt-toggleIcon"
										dangerouslySetInnerHTML={{
											__html: isCollapsed ? openicon : closeicon,
										}}
										onClick={toggleDrawer}
									/>

									{!isCollapsed && (
										<div
											id="drawerTitle"
											className="qadpt-drawerTitle"
										>
											QUICKADOPT
										</div>
									)}
									{isCollapsed && isLoggedIn && !isShowIcon && (
										<Guidemenu
											activeMenu={activeMenu}
											setActiveMenu={setActiveMenu}
											searchText={searchText}
											setSearchText={setSearchText}
											onGridItemClick={handleGridItemClick}
											isCollapsed={isCollapsed}
											toggleDrawer={toggleDrawer}
											setisShowIcon={setisShowIcon}
											setIsLoggedIn={setIsLoggedIn}
											setIsTourPopupOpen={setIsTourPopupOpen}
											setIsDrawerClosed={setIsDrawerClosed}
											setShowBannerenduser={setShowBannerenduser}
										/>
									)}
								{!isCollapsed && (
  <>
    <div
      id="threeDotMenu"
      className="qadpt-threeDotMenu"
      dangerouslySetInnerHTML={{
        __html: isLoggedIn ? threedotmenu : `<div class="svg-wrapper">${closepluginicon}</div>`,
      }}
      onClick={(e) => {
        if (isLoggedIn) {
          handleClick(e);
        } else {
          handleExtensionClose(true);
        }
      }}
      style={{ cursor: "pointer" }}
    />

  </>
)}

								</div>

								<>
									{!isCollapsed &&
										isLoggedIn &&
										isHomeScreen &&
										!isGuideInfoScreen &&
										!isTemplateScreen &&
										isthreeDotOpen && (
											<Popover
												id={popoverId}
												open={open}
												anchorEl={anchorEl}
												onClose={handleClose}
												anchorOrigin={{
													vertical: "top",
													horizontal: "right",
												}}
												transformOrigin={{
													vertical: "bottom",
													horizontal: "right",
												}}
												className="qadpt-threedot-popup"
											>
												<Box>
													<Box className="qadpt-popup-item">
														<Grid
															item
															xs={12}
														>
															<FormControl fullWidth>
																<InputLabel id="select-account-label">Select Account</InputLabel>
																<Select
																	className="qadpt-selectaccount"
																	labelId="select-account-label"
																	value={selectedAccountId}
																	onChange={handleAccountChange}
																	displayEmpty
																	fullWidth
																	label="Select Account"
																	onOpen={() => handleDropdownOpen("first")}
																	MenuProps={{
																		className: "qadpt-acnt-drpdwn",
																		PaperProps: {
																			style: {
																				position: "absolute",
																			},
																		},
																	}}
																>
																	{accounts.length > 0 ? (
																		accounts.map((account) => (
																			<MenuItem
																				key={account.AccountId}
																				value={account.AccountId}
																			>
																				{account.AccountName}
																			</MenuItem>
																		))
																	) : (
																		<MenuItem disabled>No accounts available</MenuItem>
																	)}
																</Select>
															</FormControl>
														</Grid>
													</Box>
													<Box
														className={`qadpt-popup-item `}
														onClick={handleOpenPortal}
													>
														<div
															dangerouslySetInnerHTML={{ __html: portalicon }}
															className="qadpt-popup-icon"
														/>
														<div className="qadpt-popup-text">Open Portal</div>
													</Box>

													<Box
														className={`qadpt-popup-item`}
														onClick={() => handleExtensionClose(false)}
													>
														<div
															dangerouslySetInnerHTML={{ __html: closepluginicon }}
															className="qadpt-popup-icon"
														/>
														<div className="qadpt-popup-text">Close Plugin</div>
																	</Box>


																	{/* {!showScrapingButton && ( */}
																		<Box
																			className={`qadpt-popup-item`}
																			onClick={handleStartTraining}
																		>
																			<div
																				dangerouslySetInnerHTML={{ __html: ai }}
																				className="qadpt-popup-icon" style={{    height: "20px",
																					width: "17px"}}
																			/>
																			<div className="qadpt-popup-text">Start Training</div>
																		</Box>

																	{/* {showScrapingButton && (
																		<Box
																			className={`qadpt-popup-item`}
																			onClick={async () => {
																				try {
																					await stopScraping(selectedAccountId);
																					setShowScrapingButton(false);
																					setIsthreeDotOpen(false);
																					setAnchorEl(null);
																				} catch (error) {
																					console.error('Error stopping scraping:', error);
																				}
																			}}
																		>
																			<div
																				dangerouslySetInnerHTML={{ __html: ai }}
																				className="qadpt-popup-icon"
																			/>
																			<div className="qadpt-popup-text">Stop Scraping</div>
																		</Box>
																	)}

 */}


													<Box
														className="qadpt-popup-item"
														onClick={() => {
															handleLogoutClick();
														}}
													>
														<div
															dangerouslySetInnerHTML={{ __html: closepluginicon }}
															className="qadpt-popup-icon"
														/>
														<div className="qadpt-popup-text">Logout</div>
													</Box>
												</Box>
											</Popover>
										)}
								</>
								<>
									{openPopup ? (
										<LogoutPopup
											onClose={() => setOpenPopup(false)}
											onOk={() => {
												setOpenPopup(false);
												setIsLoggedIn(false);
												clearAll();
												setIsthreeDotOpen(false);
												clearGuideDetails();
												clearAccessToken();
												document.cookie.split(";").forEach((cookie) => {
													const [name] = cookie.split("=");
													document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
												});
											}}
											title="Confirmation"
											description="Are you sure you want to logout from extension?"
											button1="Cancel"
											button2="Logout"
										/>
									) : null}
								</>
								{!isCollapsed && !isLoggedIn && <ExtensionLogin setIsLoggedIn={setIsLoggedIn} />}
								{/* {!isCollapsed && !isLoggedIn && (
						<div id="drawerContent">
							<div style={loginStyles.container}>
								<Typography sx={loginStyles.welcomeText}>Welcome back</Typography>

								<Typography sx={loginStyles.headerText}>Email</Typography>

								<TextField
									fullWidth
									value={email}
									onChange={(e) => {
										setEmail(e.target.value);
										setError(null);
									  }}
									placeholder="Enter your email"
									InputProps={{
										style: loginStyles.textFieldInput,
										disableUnderline: true,
									}}
									sx={loginStyles.textField}
									variant="standard"
									style={{ marginBottom: "10px" }}
								/>

								<Typography  sx={loginStyles.headerText}>Password</Typography>
								<TextField
									required
									fullWidth
									type={showPassword ? "text" : "password"}
									id="password"
									name="password"
									autoComplete="password"
									autoFocus

									value={password}
									onChange={handlePasswordChange}
									placeholder="Enter your password"
									className="qadpt-custom-input"
									InputProps={{
										endAdornment: (
											<InputAdornment position="end">
												<IconButton
													aria-label="toggle password visibility"
													onClick={handleClickShowPassword}
													edge="end"
												>
													{showPassword ? <VisibilityOff /> : <Visibility />}
												</IconButton>
											</InputAdornment>

										),
										style: loginStyles.textFieldInput,
										disableUnderline: true,
									}}
									sx={loginStyles.textField}
									variant="standard"
									helperText={error && (
										<FormHelperText error sx={loginStyles.qadptTextdanger}>
											{error}
										</FormHelperText>
									)}
								/>

								<Typography style={loginStyles.forgotPassword} >
									<a href={`${process.env.REACT_APP_WEB_API}/forgotpassword`} style={{ textDecoration: 'none', color: 'inherit' }}>
									Forgot password?
									</a>
								</Typography>

								<Button
									variant="contained"
									onClick={handleLoginSuccess}
									sx={loginStyles.loginButton}
								>
									Log in
								</Button>
							</div>
						</div>
					)} */}

								{!isCollapsed && isLoggedIn && isHomeScreen && !isGuideInfoScreen && !isTemplateScreen && (
									<div
										id="drawerContent"
										className="qadpt-drawerContent"
									>
										<Guidemenu
											setStepData={setStepData}
											stepData={stepData}
											activeMenu={activeMenu}
											setActiveMenu={setActiveMenu}
											searchText={searchText}
											setSearchText={setSearchText}
											onGridItemClick={handleGridItemClick}
											isCollapsed={isCollapsed}
											toggleDrawer={toggleDrawer}
											setisShowIcon={setisShowIcon}
											setIsPopupOpen={setIsPopupOpen}
											setIsLoggedIn={setIsLoggedIn}
											setIsTourPopupOpen={setIsTourPopupOpen}
											setIsDrawerClosed={setIsDrawerClosed}
																setShowBannerenduser={setShowBannerenduser}
																showScrapingButton={showScrapingButton}
																setShowScrapingButton={setShowScrapingButton}

										/>
									</div>
								)}

								{!isCollapsed && isGuideInfoScreen && !isTemplateScreen && (
									<div
										id="drawerContent"
										className="qadpt-drawerContent"
									>
										<div className="qadpt-subhead">
											<div
												id="backButton"
												onClick={handleBackClick}
												className="qadpt-backbtn"
											>
												<span dangerouslySetInnerHTML={{ __html: backicon }} />
											</div>

											<div className="qadpt-subhead-title">{selectedTemplate} info</div>
										</div>
										<Divider className="qadpt-divider" />
										<div className="qadpt-guide-form">
											<div className="qadpt-guide-label">{selectedTemplate} Name</div>
											<TextField
												name="GuideName"
												fullWidth
												placeholder={`Enter ${selectedTemplate} Name`}
												variant="outlined"
												value={guideName}
												onChange={handleGuideNameChange}
												InputProps={{
													className: "qadpt-guide-input",

													sx: {
														"&:hover .MuiOutlinedInput-notchedOutline": { borderColor: "#a8a8a8" },
														"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "1px solid #a8a8a8" },
													},
												}}

												helperText={
													errors.GuideName ? (
														<span style={{ display: "flex", fontSize: "12px", alignItems: "center" }}>
															<span
																style={{ marginRight: "4px" }}
																dangerouslySetInnerHTML={{ __html: warning }}
															/>{" "}
															{errors.GuideName}
														</span>
													) : null
												}
												error={!!errors.GuideName}
											/>

											{/* <div className="qadpt-guide-label">Starting URL</div> */}
											{/* <TextField
											fullWidth
											//placeholder="http://www.quixy.com"
											variant="outlined"
											value={startingUrl}
											onChange={(e) => setStartingUrl(e.target.value)}
											InputProps={{
												className: "qadpt-guide-input",
											}}
											disabled
										/> */}
										</div>

										<div className="qadpt-drawerFooter">
											<Button
												variant="contained"
												disabled={!isFormValid}
												onClick={handleCreateGuideClick}
												className={`qadpt-btn ${isFormValid ? "" : "disabled"}`}
											>
												{isEditable ? `Update ${selectedTemplate}` : `Create ${selectedTemplate} `}
											</Button>
										</div>
									</div>
								)}
								{/* {isCollapsed && isLoggedIn && isHomeScreen && !isGuideInfoScreen && !isTemplateScreen && (
						<ElementsSettings onDismissDataChange={handleDismissDataChange} />
					)} */}
								{/* {isSelectingElement && (
								<ElementSelection
								onElementSelected={handleElementSelected}
								/>
							)} */}
							</div>
						</div>
					)}

					{(selectedTemplate === "Tour" && selectedTemplateTour === "Tooltip") ||
					selectedTemplate === "Tooltip" ||
					selectedTemplate === "Hotspot" ? (
<div
  style={{
	position: elementSelected ? "absolute" : "relative",
    inset: elementSelected ? 0 : undefined,
    display: "grid",
    placeItems: "center",
    zIndex: 99999,
  }}
>								<CreateTooltip
								isUnSavedChanges={isUnSavedChanges}
								openWarning={openWarning}
								setopenWarning={setOpenWarning}
								handleLeave={handleLeave}
								updatedGuideData={updatedGuideData}
								isTooltipNameScreenOpen={!isCollapsed && isGuideInfoScreen && !isTemplateScreen}
							/>
						</div>
					) : null}

					{selectedTemplate === "Tour" && selectedTemplateTour === "Hotspot" ? (
<div
  style={{
	position: elementSelected ? "absolute" : "relative",
    inset: elementSelected ? 0 : undefined,
    display: "grid",
    placeItems: "center",
    zIndex: 99999,
  }}
>
							<CreateTooltip
								isUnSavedChanges={isUnSavedChanges}
								openWarning={openWarning}
								setopenWarning={setOpenWarning}
								handleLeave={handleLeave}
								updatedGuideData={updatedGuideData}
								isTooltipNameScreenOpen={!isCollapsed && isGuideInfoScreen && !isTemplateScreen}
							/>
						</div>
					) : null}
					{/* {selectedTemplate === "Tooltip" && (
						<>
							<TooltipGuide
								steps={currentStep}
								currentUrl={currentUrl}
								onClose={() => setOpenPopup(false)}
								tooltipConfig={initialGuideData}
							/>
						</>
					)} */}
				</>
			)}
		</>
	);
};

export { selectedtemp,updatedGuideData };
export default Drawer;
